import {Component, computed, input, Signal} from '@angular/core';
import {Tam4TranslationModule} from '@creactives/tam4-translation-core';
import {CommonModule} from '@angular/common';
import {FieldType} from "@creactives/models";
import {PipesModule} from "../../../pipes";
import {DynamicComponentProps} from "./dynamic-components.models";
import {ObjectsUtils} from "../../../utils";

@Component({
  standalone: true,
  selector: '[dynamicValueTranslate]',
  imports: [
    CommonModule,
    Tam4TranslationModule,
    PipesModule
  ],
  template: `
    @if (attributeText()) {
      {{ attributeText() }}
    } @else if (isNumericField()) {
      {{ dynamicValueTranslate() | number:digitsInfo():locale() }}
    } @else if (useTranslatePipe() === 'plant') {
      {{ dynamicValueTranslate() | plantTranslate:currentClient():true }}
    } @else if (useTranslatePipe() === 'client') {
      {{ dynamicValueTranslate() | clientTranslate }}
    } @else if (useTranslatePipe() === 'autodetect') {
      {{ dynamicValueTranslate() | tam4Translate:useTranslatePipe():fieldName():currentClient():defaultTextIfMissing() }}
    } @else {
      {{ dynamicValueTranslate() | attributeValueTranslate:fieldName():defaultTextIfMissing() }}
    }
  `
})
export class DynamicValueTranslateComponent {

  // TODO: Check this for currency

  dynamicValueTranslate: Signal<any> = input<any>(null);
  fieldName: Signal<string> = input<string>(null);
  useTranslatePipe: Signal<string> = input<string>('attributeValue');
  attributeText: Signal<string> = input<string>(null);
  defaultTextIfMissing: Signal<string> = input<string>(null);
  currentClient: Signal<string> = input<string>(null);
  locale: Signal<string> = input.required<string>();
  fieldConfig: Signal<DynamicComponentProps> = input<DynamicComponentProps>(null);

  fieldType: Signal<FieldType> = computed(() =>  ObjectsUtils.isNoU(this.fieldConfig()?.type) ? FieldType.textfield : FieldType[this.fieldConfig()?.type]);

  isNumericField  = computed(() => this.fieldType() === FieldType.textfieldNumeric );

  digits: Signal<number> = computed(() => this.fieldConfig()?.decimals);

  digitsInfo = computed<string>(() => {
    if (ObjectsUtils.isNoU(this.digits())) {
      return `1.0-2`;
    }

    return `1.0-${this.digits() ?? 2}`;
  });

  protected readonly FieldType = FieldType;
}
