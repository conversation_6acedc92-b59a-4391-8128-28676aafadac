import { CategoryKey, Change, Completeness, MaterialDetails, MaterialKey, MaterialPlantDetails, PlantKey, TechnicalAttribute, WarningDetails } from '@creactives/models';
import { FormState } from "src/app/modules/components/item-details/form.state";
import { MaterialAuthorizations } from 'src/app/modules/search/endpoint/search.model';

export type UUID = string;

export interface MaterialAssigned {
  processId: UUID;
  materialId: UUID;
}

export interface ExistingEditProcess {
  processId: UUID;
  patchWithoutAssignment: boolean;
  createdNote: string;
}

export interface MaterialNotAssigned {
  materialId: UUID;
  client: string;
  materialCode: string;
  existingEditProcessList: Array<ExistingEditProcess>;
}

export interface AssignmentResponse {
  materialsAssigned: Array<MaterialAssigned>;
  materialsNotAssigned: Array<MaterialNotAssigned>;
}

export enum FieldType {
  dropdown = 'dropdown',
  date = 'textfieldDate',
  alternativeUnitsOfMeasure = 'alternativeUnitsOfMeasure',
  materialValuations = 'materialValuations',
  plantData = 'plantData',
  storageLocations = 'storageLocations',
  textfield = 'textfield',
  textfieldNumeric = 'textfieldNumeric',
  textarea = 'textarea',
  localizedTextarea = 'localizedTextarea',
  popupCategoryChooser = 'popup-category-chooser',
  extended = 'extended',
  warehouse = 'warehouse',
  autocomplete = 'autocomplete',
  autocomplete_base = 'autocomplete_base',
  description_input = 'description_input',
  description_textarea = 'description_textarea',
  plants = 'plants',
  relationship = 'relationship',
  boolean = 'boolean',
  checkbox = 'checkbox',
  link = 'link',
  aggregatedCrossPlantData = 'aggregatedCrossPlantData'
}

export interface BulkEditMaterialsWithErrors {
  materialKey: MaterialKey;
  error: string;
}

export interface BulkEditMaterialsStarted {
  materialKey: MaterialKey;
  processCode: string;
  processId: UUID;
}

export interface BulkEditConfirmResponse {
  status: 'OK' | 'KO';
  errorMessage?: string;
  bulkOperationCode?: string;
  processesWithErrorsList?: BulkEditMaterialsWithErrors[];
  startedProcessesList?: BulkEditMaterialsStarted[];
}


export interface LocalizedFieldValue {
  key: string;
  text: string;
  label?: string;
}

export interface FieldConfiguration {
  id: string;
  attributeName: string;
  label?: string;
  source?: string;
  type?: FieldType;
  popupType?: string;
  language?: string;
  defaultValue?: string;
  mandatory?: boolean;
  coreAttribute?: boolean;
  technical?: boolean;
  textareaSizeRows?: number;
  textareaSizeCols?: number;
  length?: number;
  decimals?: number;
  dropdownValues?: LocalizedFieldValue[];
  unitsOfMeasure?: string[];
  targetTaxonomyName?: string;
  tabKey?: string;
  customer?: boolean;
  localized?: boolean;
  //  new fields to be added to the backend
  editable?: boolean;
  hidden?: boolean;
  multiple?: boolean;
  useTranslatePipe?: string;
  tableFields?: string[];
  descriptionLanguages?: string[];
  editableDescriptionsLanguage?: { [language: string]: boolean };
  mandatoryDescriptionsLanguage?: { [language: string]: boolean };
  goldenRecordAttribute?: boolean;
  order?: number;
}

export interface MaterialsInRelationship {
  materialId: string;
  role: string;
  shortDescription: string;
  materialCode: string;
  client: string;
}

export interface Relationship {
  relationshipId: string;
  relationshipType: string;
  materialsInRelationship: MaterialsInRelationship[];
}

export class ItemTab {
  tabKey: string;
  tabLabel: string;
  hasMandatoryAttributes: boolean;
  hasTechnicalAttributes: boolean;
  requests: Array<FieldConfiguration>;
  relationships?: Array<Relationship>;
}

export interface BulkEditResponse {
  client: string;
  bulkEditRequestId: string;
  tabs: Array<ItemTab>;
  numberOfMaterialIds: number;
  numberOfEditableMaterialIds: number;
}

export interface BulkExtendResponse {
  editableMaterials: Array<UUID>;
  client: string;
}

export interface BulkExtensionRequest {
  masterdatas: string[];
  plantKeys: Array<PlantKey>;
  language: string;
}

export interface Supplier {
  client: string;
  code: string;
  newCode: string;
  locationId: string;
  createdDate: Date;
  createdBy: number;
  lastUpdatedDate: Date;
  updatedBy: number;
  name: string;
  contactLastName: string;
  vatRegistrationNumber: string;
  countryCode: string;
  address: string;
  name2: string;
  name3: string;
  name4: string;
  supplierGroupNormalized: string;
  supplierNameNormalized: string;
  enabled: boolean;
}

export interface SupplierUpsertRequest {
  client: string;
  vendorCode: string;
  oldVendorCode: string;
  locationId: string;
  name: string;
  contactLastName: string;
  vatRegistrationNumber: string;
  countryCode: string;
  name2: string;
  name3: string;
  name4: string;
  address: string;
  supplierNameNormalized: string;
  supplierGroupNormalized: string;
  enabled: boolean;
  modify: boolean;
}

export interface RowVendorImportedResult {
  row: number;
  client: string;
  code: string;
  name: string;
  errorMessage: string;
}

export interface SupplierExcelImportResponse {
  deleted: Array<RowVendorImportedResult>;
  error: Array<RowVendorImportedResult>;
  created: Array<RowVendorImportedResult>;
  updated: Array<RowVendorImportedResult>;
}

export interface SupplierResponse {
  content: Array<Supplier>;
  totalElements: number;
  last: boolean;
  totalPages: number;
  size: number;
  number: number;
  numberOfElements: number;
  first: boolean;
  empty: boolean;
}

export interface SupplierPresentResponse {
  isPresent?: boolean;
  isUsed?: boolean;
}

export interface MaterialPlantValuations {
  client: string;
  code: string;
  plantKey?: PlantKey;
  valuationType: string;
  valuationClass: string;
  valuationCategory: string;
  stockQuantity: number;
  stockAmount: number;
  consumptionAmount: number;
  orderedAmount: number;
  consumptionQuantity: number;
  orderedQuantity: number;
  movingAveragePrice: number;
  movingAveragePriceEUR: string;
  standardPrice: number;
  standardPriceEUR: string;
  priceUnit: string;
  totalStockAmountEUR: string;
  totalConsumptionAmountEUR: string;
  totalOrderedAmountEUR: string;
  totalStockAmount: string;
  totalConsumptionAmount: string;
  totalOrderedAmount: string;
  priceControlIndicator: string;
}

export interface MaterialValuation {
  plantKey: PlantKey;
  countryCode: string;
  currency: string;
  materialPlantValuations: Array<MaterialPlantValuations>;
}

export interface MaterialValuations {
  plantValuationContainers: Array<MaterialValuation>;
}

export interface MaterialSnapshot {
  materialDetails: MaterialDetails;
  plantDetails: Array<MaterialPlantDetails>;
  materialPlantValuations?: MaterialValuations;
  technicalAttributesMap?: { [key: string]: Attribute };
  technicalAttributes?: { technicalAttributes: Attribute[] };
}
export interface ApproveGRDeleteRequest {
  processId: UUID;
  approvalNote: string;
  page: string;
  language: string;
}

export interface RejectGRRequest {
  processId: UUID;
  moreInformationNeeded: boolean;
  rejectionNote: string;
  language: string;
}


export interface Attribute {
  value: string;
  '@type': string;
  code: string;
  tamFieldCode: string;
  unitOfMeasure?: string;
}

export interface Categories {
  materialGroupCategorization: CategoryKey[];
  enrichedMaterialGroupCategorization: CategoryKey[];
  technicalCategorization: CategoryKey[];
}

export interface MaterialSnapshotWithConfiguration {
  materialActionsAuthorization?: MaterialAuthorizations;
  snapshot: MaterialSnapshot;
  configuration: Array<ItemTab>;
  categories: Categories;
  instances?: Array<MaterialSnapshot>;
  warnings?: Array<WarningDetails>;
  formState?: FormState;
}

export interface SummaryCategories {
  materialGroupCategorization: CategoryKey[];
  enrichedMaterialGroupCategorization: CategoryKey[];
  technicalCategorization: CategoryKey[];
}

export interface SemanticallyAnalyzedPreview {
  masterDataId: UUID;
  changes: ClassificationChanges;
  summaryCategoriesNew: SummaryCategories;
  summaryCategoriesOld: SummaryCategories;

}

export interface CalculateSemanticallyAnalyzedResponse {
  semanticallyAnalyzedPreviews: Array<SemanticallyAnalyzedPreview>;
}

export interface ClassificationChanges {
  normalizedShortDescriptions?: { [lang: string]: Change<string> };
  normalizedLongDescriptions?: { [lang: string]: Change<string> };
  technicalAttributes?: Change<TechnicalAttribute>[];
  completeness?: Change<Completeness>;

}

export interface SimplifiedMaterial {
  materialId: string;
  materialKey: MaterialKey;
}

export interface AttachmentInfo {
  attachmentId: string;
  fileName: string;
  size: number;
  date: Date;
  client: string;
  materialCode: string;
}

export interface AttachmentInstanceDetails {
  uuid: UUID;
  client?: string;
  materialCode: string;
  thumbnail?: AttachmentContentInfo;
  attachments?: Array<AttachmentContentInfo>;
  isGoldenRecord?: boolean;
  selected?: boolean;
  disabled?: boolean;
}

export interface AttachmentsInfoRequest {
  attachmentUUIDs: string[];
}

export interface AttachmentsInfoResponse {
  attachmentsInfo: AttachmentInfo[];
}

export interface ListOfValuesResponse {
  name: string;
  language: string;
  fieldValues: LocalizedFieldValue[];
}

export const DEFAULT_FIELDCONFIG_PLANT_DESCRIPTION: FieldConfiguration = {
  type: FieldType.textfield,
  editable: false,
  attributeName: "plantDescription",
  id: 'plantDescription'
};

export interface AttachmentContentInfo extends AttachmentInfo {
  content: Uint8Array;
}
