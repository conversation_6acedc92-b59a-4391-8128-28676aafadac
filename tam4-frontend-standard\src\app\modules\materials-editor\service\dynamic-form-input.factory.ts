import {
  DocumentData,
  DynamicAutocompleteComponent,
  DynamicBaseInputComponent,
  DynamicComponentWrapper,
  DynamicDateComponent,
  DynamicDescriptionComponent,
  DynamicDropdownComponent,
  DynamicInputComponent,
  DynamicInputComponentProps,
  DynamicInputNumericComponent,
  DynamicInputTableComponent,
  DynamicTableUnitOfMeasureComponent,
  DynamicTextareaComponent,
  DynamicBooleanComponent,
  SmartCreationFieldFetchSuggestionFn
} from 'src/app/modules/materials-editor/dynamic-components';
import {SmartCreationMaterialDetail, SmartFieldConfiguration} from 'src/app/modules/smart-creation/models/smart-creation.types';
import {FormGroup} from '@angular/forms';
import {BaseMaterialEditorTabsKeys, Tam4FormGroup, ViewModeEnum} from 'src/app/modules/materials-editor/models/material-editor.types';
import {EventEmitter, Injectable} from '@angular/core';
import {Tam4ComponentEvent, TAm4StandardFieldNames} from 'src/app/models';
import {DynamicFormEventPayload} from 'src/app/modules/materials-modal/store/materials-modal.action-types';
import {ObjectsUtils} from 'src/app/utils';
import {buildDescription} from 'src/app/modules/materials-editor/common/materials-editor.function';
import {
  AlternativeUnitsOfMeasure,
  Instance,
  MaterialPlantDetails,
  MaterialPlantValuation,
  PlantKey,
  PlantValuationContainer
} from 'src/app/modules/smart-creation/models/smart-creation-validation.types';
import {DynamicActionChangelogBadgeComponent} from '../dynamic-components/dynamic-action-changelog-badge.component';
import {AbstractMultiselectComponent} from "../dynamic-components/abstract-multiselect.component";

export type DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                         page: string,
                                         formGroup: FormGroup,
                                         sheetIndex: number,
                                         tabIndex: number,
                                         viewMode?: ViewModeEnum,
                                         initialData?: SmartCreationMaterialDetail,
                                         dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn) => DynamicComponentWrapper;

@Injectable({
  providedIn: 'root'
})
export class DynamicFormInputFactory {

  methodMap: { [key: string]: string } = {
    'dropdown': 'selectableValueBuilder',
    'warehouse': 'selectableValueBuilder',
    'autocomplete': 'selectableValueBuilder',
    'autocomplete_base': 'selectableValueBuilder',
    'textfieldNumeric': 'genericValueBuilder',
    'textarea': 'genericValueBuilder',
    'textfieldDate': 'genericValueBuilder',
    'textfield': 'genericValueBuilder',
    'checkbox': 'genericValueBuilder',
    'normalizeddescription': 'normalizedDescription',
    'normalizeddescription_textarea': 'normalizedDescriptionTextArea',
    'materialValuations': 'materialValuations',
    'plants': 'plants',
    'storageLocations': 'storageLocations',
    'extended': 'extendedBuilder',
    'alternativeUnitsOfMeasure': 'alternativeUnitsOfMeasure',
    'aggregatedCrossPlantData': 'crossPlantAggregatedDataBuilder',
  };
  componentMap: { [key: string]: typeof DynamicBaseInputComponent | typeof DynamicDescriptionComponent | typeof AbstractMultiselectComponent } = {
    'dropdown': DynamicDropdownComponent,
    'warehouse': DynamicDropdownComponent,
    'autocomplete': DynamicAutocompleteComponent,
    'autocomplete_base': DynamicAutocompleteComponent,
    'textfieldNumeric': DynamicInputNumericComponent,
    'textarea': DynamicTextareaComponent,
    'textfieldDate': DynamicDateComponent,
    'textfield': DynamicInputComponent,
    'checkbox': DynamicBooleanComponent,
    'description_input': DynamicDescriptionComponent,
    'description_textarea': DynamicDescriptionComponent,
    'materialValuations': DynamicInputTableComponent,
    'extended': DynamicInputTableComponent,
    'plants': DynamicInputTableComponent,
    'storageLocations': DynamicInputTableComponent,
    'alternativeUnitsOfMeasure': DynamicTableUnitOfMeasureComponent,
    'aggregatedCrossPlantData': DynamicInputTableComponent,
  };
  private buildDescription = buildDescription;

  public buildDynamicFormInput: DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                                             page: string,
                                                             formGroup: FormGroup,
                                                             sheetIndex: number,
                                                             tabIndex: number,
                                                             viewMode?: ViewModeEnum,
                                                             initialData?: SmartCreationMaterialDetail,
                                                             dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn): DynamicComponentWrapper => {
    const methodNameToCall: string = this.methodMap[v.type] || '';
    try {
      if (this?.[methodNameToCall]) {
        return (this?.[methodNameToCall] as DynamicFormInputBuilderFn)(v, page, formGroup, sheetIndex, tabIndex, viewMode, initialData, dynamicAutocompleteFn);
      }
      return this.genericValueBuilder(v, page, formGroup, sheetIndex, tabIndex, viewMode, initialData, dynamicAutocompleteFn);
    } catch (e) {
      console.error('buildDynamicFormInput', v.id, v.type, this.methodMap[v.type], e);
    }
  }

  private generateRowsForMaterialPlantValuation(details: MaterialPlantValuation, tableFields: string[]) {
    const plantValuationContainers = details?.plantValuationContainers;

    if (!plantValuationContainers) {
      return [];
    }

    return Array.prototype.concat.apply(
      [],
      plantValuationContainers.map((plantDetail: PlantValuationContainer) =>
        Array.prototype.concat.apply(
          [],
          plantDetail.materialPlantValuations.map((plant: any) => {
            const {plantKey, ...restOfPlant} = plant;
            const row = {plantKey: this.getPlantKey(plantDetail.plantKey), ...restOfPlant};

            tableFields
              .filter((column) => column !== 'plantKey')
              .forEach((column) => (row[column] = plant[column]));

            return row;
          })
        )
      )
    );
  }

  private baseFieldInit: DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                                      page: string,
                                                      formGroup: FormGroup<any> = new Tam4FormGroup<any>({}),
                                                      sheetIndex: number,
                                                      tabIndex: number,
                                                      viewMode: ViewModeEnum = ViewModeEnum.DETAILS,
                                                      initialData?: SmartCreationMaterialDetail,
                                                      dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn): DynamicComponentWrapper => {
    const component = this.componentMap[v.type] || DynamicInputComponent;

    const _editable = v?.editable !== false/* && !(ObjectsUtils.isNotNoU(initialData?.additionalMaterialInformation?.goldenRecord) && v.goldenRecordAttribute)*/;
    const componentParams: DynamicInputComponentProps = {
      ...v,
      editable: _editable,
      page,
      sheetIndex,
      tabIndex,
      formControlName: v.id,
      formGroup,
      defaultValue: v.defaultValue,
      viewMode,
      componentProps: {
        ...v,
        editable: _editable,
        sheetIndex,
        tabIndex
      },
      formComponentEvent: new EventEmitter<Tam4ComponentEvent<any, DynamicFormEventPayload>>,
    };

    return {
      component,
      componentParams
    };
  }

  private genericValueBuilder: DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                                            page: string,
                                                            formGroup: FormGroup<any> = new Tam4FormGroup<any>({}),
                                                            sheetIndex: number,
                                                            tabIndex: number,
                                                            viewMode: ViewModeEnum = ViewModeEnum.DETAILS,
                                                            initialData?: SmartCreationMaterialDetail,
                                                            dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn): DynamicComponentWrapper => {
    return this.baseFieldInit(v, page, formGroup, sheetIndex, tabIndex, viewMode);
  }


  private extendedBuilder: DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                                        page: string,
                                                        formGroup: FormGroup<any> = new Tam4FormGroup<any>({}),
                                                        sheetIndex: number,
                                                        tabIndex: number,
                                                        viewMode: ViewModeEnum = ViewModeEnum.DETAILS,
                                                        initialData?: SmartCreationMaterialDetail,
                                                        dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn): DynamicComponentWrapper => {

    if (!v?.clientId && !!v) {
      v.clientId = initialData?.additionalMaterialInformation?.materialKey?.client;
    }
    return this.baseFieldInit(v, page, formGroup, sheetIndex, tabIndex, viewMode);
  }

  private selectableValueBuilder(v: SmartFieldConfiguration,
                                 page: string,
                                 formGroup: FormGroup,
                                 sheetIndex: number,
                                 tabIndex: number,
                                 viewMode?: ViewModeEnum,
                                 initialData?: SmartCreationMaterialDetail,
                                 dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn): DynamicComponentWrapper {
    const cWrapper = this.baseFieldInit(v, page, formGroup, sheetIndex, tabIndex, viewMode, initialData, dynamicAutocompleteFn);
    const additionalDropdownComponentProps: any = {};

    if (!ObjectsUtils.isArrayEmpty(v.dropdownValues)) {
      additionalDropdownComponentProps.items = v.dropdownValues;
    } else {
      additionalDropdownComponentProps.dropdownListSource = v.dropdownListSource;
      additionalDropdownComponentProps.loadItems = (source, id, query, clientId?: string, page?: string) => dynamicAutocompleteFn(source,
        id,
        query,
        clientId, page);
      additionalDropdownComponentProps.fetchSuggestionsFn = (source: string,
                                                             id: string,
                                                             query: string,
                                                             documentData: DocumentData,
                                                             clientId?: string,
                                                             page?: string) => dynamicAutocompleteFn(source,
        id,
        query,
        documentData,
        clientId,
        page);
    }

    cWrapper.componentParams.componentProps = {
      ...cWrapper.componentParams.componentProps,
      ...additionalDropdownComponentProps,
    };

    return cWrapper;
  }

  private normalizedDescription: DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                                              page: string,
                                                              formGroup: FormGroup<any> = new Tam4FormGroup<any>({}),
                                                              sheetIndex: number,
                                                              tabIndex: number,
                                                              viewMode: ViewModeEnum = ViewModeEnum.DETAILS,
                                                              initialData?: SmartCreationMaterialDetail,
                                                              dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn): DynamicComponentWrapper => {
    return this.buildDescription(false, page, v, formGroup);
  }

  private normalizedDescriptionTextArea: DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                                                      page: string,
                                                                      formGroup: FormGroup<any> = new Tam4FormGroup<any>({}),
                                                                      sheetIndex: number,
                                                                      tabIndex: number,
                                                                      viewMode: ViewModeEnum = ViewModeEnum.DETAILS,
                                                                      initialData?: SmartCreationMaterialDetail,
                                                                      dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn): DynamicComponentWrapper => {
    return this.buildDescription(true, page, v, formGroup);
  }

  private materialValuations: DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                                           page: string,
                                                           formGroup: FormGroup<any> = new Tam4FormGroup<any>({}),
                                                           sheetIndex: number,
                                                           tabIndex: number,
                                                           viewMode: ViewModeEnum = ViewModeEnum.DETAILS,
                                                           initialData?: SmartCreationMaterialDetail,
                                                           dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn): DynamicComponentWrapper => {
    const out: DynamicComponentWrapper = this.baseFieldInit(v, page, formGroup, sheetIndex, tabIndex, viewMode);
    out.componentParams.componentProps = {
      ...out.componentParams.componentProps,
      details: this.generateRowsForMaterialPlantValuation(initialData?.additionalMaterialInformation?.materialPlantValuations,
        v.tableFields)
    };

    return out;
  }

  private plants: DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                               page: string,
                                               formGroup: FormGroup<any> = new Tam4FormGroup<any>({}),
                                               sheetIndex: number,
                                               tabIndex: number,
                                               viewMode: ViewModeEnum = ViewModeEnum.DETAILS,
                                               initialData?: SmartCreationMaterialDetail,
                                               dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn): DynamicComponentWrapper => {
    const out: DynamicComponentWrapper = this.baseFieldInit(v, page, formGroup, sheetIndex, tabIndex, viewMode);
    out.componentParams.componentProps = {
      ...out.componentParams.componentProps,
      details: this.generateRowsForPlant(initialData?.additionalMaterialInformation?.plantDetails, v.tableFields)
    };

    return out;
  }

  private storageLocations: DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                                         page: string,
                                                         formGroup: FormGroup<any> = new Tam4FormGroup<any>({}),
                                                         sheetIndex: number,
                                                         tabIndex: number,
                                                         viewMode: ViewModeEnum = ViewModeEnum.DETAILS,
                                                         initialData?: SmartCreationMaterialDetail,
                                                         dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn): DynamicComponentWrapper => {
    const out: DynamicComponentWrapper = this.baseFieldInit(v, page, formGroup, sheetIndex, tabIndex, viewMode);
    out.componentParams.componentProps = {
      ...out.componentParams.componentProps,
      details: this.generateRowsForStorageLocation(initialData?.additionalMaterialInformation?.plantDetails,
        v.tableFields)
    };

    return out;
  }

  private alternativeUnitsOfMeasure: DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                                                  page: string,
                                                                  formGroup: FormGroup<any> = new Tam4FormGroup<any>({}),
                                                                  sheetIndex: number,
                                                                  tabIndex: number,
                                                                  viewMode: ViewModeEnum = ViewModeEnum.DETAILS,
                                                                  initialData?: SmartCreationMaterialDetail,
                                                                  dynamicAutocompleteFn?: SmartCreationFieldFetchSuggestionFn): DynamicComponentWrapper => {
    const out: DynamicComponentWrapper = this.selectableValueBuilder(v,
      page,
      formGroup,
      sheetIndex,
      tabIndex,
      viewMode,
      initialData,
      dynamicAutocompleteFn);

    const uomValue = this.getStandardUom(v.relatedAttribute, initialData);

    out.componentParams.componentProps = {
      ...out.componentParams.componentProps,
      standardUom: uomValue,
      uomDropdownValues: [],
      details: this.generateRowsForAlternativeUom(initialData?.additionalMaterialInformation?.alternativeUnitsOfMeasure,
        v.tableFields,
        v.relatedAttribute,
        uomValue)
    };

    return out;
  }

  private getPlantKey(plantKey: PlantKey): string {
    return `${plantKey.client} / ${plantKey.code}`;
  }

  private generateRowsForStorageLocation(details: MaterialPlantDetails[], tableFields: string[]) {
    const plantDetails = details;
    if (!plantDetails) {
      return;
    }

    return Array.prototype.concat.apply(
      [],
      plantDetails.map((plantDetail: any) =>
        Array.prototype.concat.apply(
          [],
          plantDetail.storageLocations.map((storageLocation: any) => {
            const row: any = {plantKey: this.getPlantKey(plantDetail.plantKey), ...plantDetail.plantKey};
            tableFields
              .filter((column) => column !== 'plantKey')
              .forEach((column) => (row[column] = storageLocation[column]));
            return row;
          })
        )
      )
    );
  }

  private generateRowsForPlant(details: MaterialPlantDetails[], tableFields: string[]) {
    const plantDetails = details;
    if (!plantDetails) {
      return;
    }

    return plantDetails.map((plantDetail: any) => {
      const row: any = {plantKey: this.getPlantKey(plantDetail.plantKey), ...plantDetail.plantKey};
      tableFields
        .filter((column) => column !== 'plantKey')
        .forEach((column) => (row[column] = plantDetail[column]));
      return row;
    });
  }

  private generateRowsForAlternativeUom(details: AlternativeUnitsOfMeasure[], tableFields: string[],
                                        relatedAttribute: string, standardUom?: string) {
    if (!details) {
      return [];
    }
    const umaKey: string = TAm4StandardFieldNames.ALTERNATIVE_UOM_ROW;
    const auom = details.map((alternativeUom: any) => {
      const row: any = {};

      row.x = alternativeUom.numerator;
      row.y = alternativeUom.denominator;
      row[relatedAttribute] = standardUom || '';
      row[umaKey] = alternativeUom.alternativeUnitOfMeasurement;

      return row;
    });

    return auom;
  }

  private getStandardUom(relatedAttribute: string, initialData?: SmartCreationMaterialDetail): string | undefined {
    return (
      initialData?.materialSheets
        .find(sheet => sheet.tabKey === BaseMaterialEditorTabsKeys.BASIC_DATA)
        ?.requests
        .find(request => request.id === relatedAttribute)
        ?.value
    );
  }

  private crossPlantAggregatedDataBuilder: DynamicFormInputBuilderFn = (v: SmartFieldConfiguration,
                                                                        page: string,
                                                                        formGroup: FormGroup<any> = new Tam4FormGroup<any>({}),
                                                                        sheetIndex: number,
                                                                        tabIndex: number,
                                                                        viewMode: ViewModeEnum = ViewModeEnum.DETAILS,
                                                                        initialData?: SmartCreationMaterialDetail): DynamicComponentWrapper => {
    const out: DynamicComponentWrapper = this.baseFieldInit(v, page, formGroup, sheetIndex, tabIndex, viewMode);
    const instances = initialData?.additionalMaterialInformation?.instances?.filter(i => i.materialActionsAuthorizations?.canAudit?.enabled) || [];
    if (instances.length > 0) {
      out.componentParams.componentProps = {
        ...out.componentParams.componentProps,
        actionColumnTable: {
          showActionColumn: true,
          actionColumnComponent: DynamicActionChangelogBadgeComponent,
          actionColumnData: this.transformInstancesToActionData(instances)
        }
      };
    }

    return out;
  }

  private transformInstancesToActionData(instances: Instance[]): Instance[] {
    return instances.map(instance => ({
      materialId: instance.materialId,
      inProgressProcessCount: instance.inProgressProcessCount,
      ...instance
    }));
  }

}
