import { FormGroup } from '@angular/forms';
import { SelectorMap } from '@creactives/models';
import { Tam4TranslationService, TranslationPipeFactory } from '@creactives/tam4-translation-core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import {
  BehaviorSubject,
  combineLatest,
  distinctUntilChanged,
  EMPTY,
  first,
  Observable,
  switchMap,
  withLatestFrom
} from 'rxjs';
import {
  CommonMessageWrapper,
  MessageSeverityType,
  ResponseOutcomeType,
  Tam4ComponentEvent,
  TamApePageType,
  TamApePageUtils
} from 'src/app/models';
import {
  BaseMaterialEditorTabsKeys,
  DynamicFormGroup,
  SOURCE_TYPE,
  ValidateDataResponse,
  ViewModeEnum
} from 'src/app/modules/materials-editor/models/material-editor.types';
import { EventUtils, HttpClientUtils, ObjectsUtils } from 'src/app/utils';
import { getCurrentUserId, getFallbackLanguages, getOpenTextEnabled } from '../../layout/store/profile/profile.state';
import { DynamicFormInputService } from '../../materials-editor/service/dynamic-form-input.service';
import { NoteFlagMandatory, RejectionPossibility } from '../../smart-creation/models/smart-creation-validation.types';
import {
  AdditionalMaterialInformation,
  ProcessResponse,
  SmartCreationApprovalRejectPayload,
  SmartCreationMaterialDetail,
  SmartCreationSelectedCategories
} from '../../smart-creation/models/smart-creation.types';
import { SmartCreationService } from '../../smart-creation/smart-creation.service';
import { MaterialsModalService } from '../materials-modal.service';


import { Component, computed, effect, Injector, OnDestroy, OnInit, Signal, signal, WritableSignal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { GlobalError, GlobalWarning } from "@creactives/tam4-common-ui";
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { debounceTime, map, takeUntil } from 'rxjs/operators';
import { Tam4SelectorConfig, TamAbstractReduxComponent } from 'src/app/components';
import { getOtherPermissionsByName } from 'src/app/modules/layout/store/reducers';
import { flattenCategories } from 'src/app/modules/materials-editor/common/materials-editor.function';
import {
  attachmentTab,
  classificationTab,
  grInstancesTab,
  NavigationMenuItem,
  relationshipsTab,
  thumbnailTab
} from 'src/app/modules/materials-editor/components/sc-material-navigation-menu.component';
import { MaterialUpdateService } from 'src/app/modules/smart-creation/service/material-update.service';
import { defaultModalConfig } from 'src/app/utils/common.constants';
import { CommonUtils } from 'src/app/utils/common.utils';
import { getCurrentLanguage } from '../../bulk-upload/store/profile.reducer';
import { OpenPlantUpdatePopup } from "../../layout/store/actions/popup.actions";
import { MaterialAuthorizations } from '../../search/endpoint/search.model';
import { getCanEditClassification } from '../../smart-creation/commons/smart-creation.constants';
import { GRConfirmDeleteDialog } from '../../smart-creation/components/gr-confirm-delete.dialog';
import { GRDeleteRejectApprovalDialog } from '../../smart-creation/components/gr-delete-reject-approval.dialog';
import {
  SmartCreationConfirmCreationDialog
} from '../../smart-creation/components/smart-creation-confirm-creation.dialog';
import { SmartCreationRejectApprovalDialog } from '../../smart-creation/components/smart-creation-reject-approval.dialog';
import { defaultModalCreationConfig } from '../commons/materials-modal.constants';
import {
  ActionMenuItem,
  AttachmentInstanceCleanRequest,
  AttachmentInstanceUploadRequest,
  ConfirmDeletionPayload
} from '../models/modals-modal.types';
import { MaterialsModalSelectors } from '../store/materials-modal.selectors';
import { MaterialCategoriesTreeDialog } from './material-categories-tree.dialog';


const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'modalEditOpened', selector: MaterialsModalSelectors.getModalEditOpened},
  {key: 'modalDeleteOpened', selector: MaterialsModalSelectors.getModalDeleteOpened},
  {key: 'loading', selector: MaterialsModalSelectors.getLoading},
  {key: 'attachmentsInfo', selector: MaterialsModalSelectors.getAttachmentsInfo},
  {key: 'getAttachmentsInstances', selector: MaterialsModalSelectors.getAttachmentsInstances},
  {key: 'getImageInstances', selector: MaterialsModalSelectors.getImagesInstances},
    {key: 'material', selector: MaterialsModalSelectors.getMaterial},
  {key: 'materialId', selector: MaterialsModalSelectors.getMaterialId},
  {key: 'imageUUID', selector: MaterialsModalSelectors.getImageUUID},
  {key: 'attachmentsUUID', selector: MaterialsModalSelectors.getAttachmentsUUID},
  {key: 'plantSheet', selector: MaterialsModalSelectors.getPlantSheet},
  {key: 'completeness', selector: MaterialsModalSelectors.getCompleteness},
  {key: 'categoriesSheet', selector: MaterialsModalSelectors.getCategoriesSheet},
  {key: 'selectedCategories', selector: MaterialsModalSelectors.getSelectedCategories},
  {key: 'validateMaterialResult', selector: MaterialsModalSelectors.getValidateMaterialResult},
  {key: 'alternativeUomList', selector: MaterialsModalSelectors.getAlternativeUomList},
  {key: 'saveStatus', selector: MaterialsModalSelectors.getSaveStatus},
  {key: 'setup', selector: MaterialsModalSelectors.getSetup},
  {key: 'process', selector: MaterialsModalSelectors.getProcess},
  {key: 'hasChanges', selector: MaterialsModalSelectors.hasApproverChanges},
  {key: 'changeDocClassificationEnabled', selector: getOtherPermissionsByName('change-doc-classification')},
  {key: 'currentPage', selector: MaterialsModalSelectors.getPage},
  {key: 'processId', selector: MaterialsModalSelectors.getProcessId},
  {key: 'currentUserId', selector: getCurrentUserId},
  {key: 'imageFromInstances', selector: MaterialsModalSelectors.getImageFromInstances}
];

const ACTION_EVENTS = {
  EDIT: {id: 'edit', actionName: 'canEdit'},
  EDIT_GOLDEN_RECORD: {id: 'edit-golden-record', actionName: 'canManageGoldenRecord'},
  DELETE_GOLDEN_RECORD: {id: 'delete-golden-record', actionName: 'canDeleteGoldenRecord'},
  REQUEST_EDIT: {id: 'request-edit', actionName: 'canAssign'},
  REQUEST_EXTENSION: {id: 'plant-extension', actionName: 'canExtendMaterial'},
  REQUEST_PLANT_EDIT: {id: 'plant-edit', actionName: 'canEditMaterialPlant'},
  COPY_MATERIAL: {id: 'copy-material', actionName: 'canCopyMaterial'}
};

const ALL_ACTION_MENU_ITEMS: ActionMenuItem[] = [
  {
    id: ACTION_EVENTS.EDIT.id,
    label: `search.menu.${ACTION_EVENTS.EDIT.id}`,
    icon: 'fas fa-pen',
    disabled: false,
    showTooltip: false,
    tooltip: null
  },
  {
    id: ACTION_EVENTS.EDIT_GOLDEN_RECORD.id,
    label: `search.menu.${ACTION_EVENTS.EDIT_GOLDEN_RECORD.id}`,
    icon: 'fas fa-pen',
    disabled: false,
    showTooltip: false,
    tooltip: null
  },
  {
    id: ACTION_EVENTS.DELETE_GOLDEN_RECORD.id,
    label: `search.menu.${ACTION_EVENTS.DELETE_GOLDEN_RECORD.id}`,
    icon: 'fas fa-trash',
    disabled: false,
    showTooltip: false,
    tooltip: null
  },
  {
    id: ACTION_EVENTS.REQUEST_EDIT.id,
    label: `search.menu.${ACTION_EVENTS.REQUEST_EDIT.id}`,
    icon: 'fas fa-share',
    disabled: false,
    showTooltip: false,
    tooltip: null
  },
  {
    id: ACTION_EVENTS.REQUEST_EXTENSION.id,
    label: `search.menu.${ACTION_EVENTS.REQUEST_EXTENSION.id}`,
    icon: 'fas fa-arrows-h',
    disabled: false,
    showTooltip: false,
    tooltip: null
  },
  {
    id: ACTION_EVENTS.REQUEST_PLANT_EDIT.id,
    label: `search.menu.${ACTION_EVENTS.REQUEST_PLANT_EDIT.id}`,
    icon: 'fas fa-pen',
    disabled: false,
    showTooltip: false,
    tooltip: null
  },
  {
    id: ACTION_EVENTS.COPY_MATERIAL.id,
    label: `search.menu.${ACTION_EVENTS.COPY_MATERIAL.id}`,
    icon: 'fas fa-clone',
    disabled: false,
    showTooltip: false,
    tooltip: null
  }

];

@Component({
  selector: 'div[materialDetailsModalComponent]',
  host: {
    '[class]': "'fixed-hf-container modal bg-secondary'"
  },
  styles: [
    `
        div.disabled {
            color: #5759627e;
            position: relative;
        }

        .error-tooltip {
            background-color: #fd1361;
            color: #fff;
            text-align: center;
        }
    `
  ],


  template: `
    <tam-progress-spinner [active]="selMap?.loading | async"></tam-progress-spinner>
    @if (selMap?.material | async; as materialDetails) {
      <div scHeaderModalSticky class="fixed-hf-header bg-white"
           [completeness]="selMap?.completeness | async"
           [materialDetails]="materialDetails"
           [viewMode]="viewMode"
           [disabled]="!isEditEnabled()"
           [categories]="flattenCategories(materialDetails?.categoriesSheet)"
           [materialImageSrc]="materialImageSrc"
           (materialImageUpload)="handleMaterialImageUpload($event)"
           (materialImageRemove)="handleMaterialImageRemove()"
           (materialImageDownload)="handleMaterialImageDownload()"
           (openGrDetails)="openGrDetails($event)"
           [processId]="signals?.processId()"
           [currentUserId]="signals?.currentUserId()"
      >

      </div>

      <div class="fixed-hf-content flex gap-2">
        <div class="w-20rem"
             scMaterialNavigationMenuComponent
             [showGrFromScratch]="false"
             [goldenRecordSheetIndex]="goldenRecordSheetIndex"
             [dynamicFormGroup]="dynamicFormGroup()"
             [topListItems]="topDocNav | async"
             [isEditEnabled]="isEditEnabled()"
             [bottomListItems]="bottomDocNav | async"
        >

        </div>
        <div class="flex-1 nice-scroll autoscroll-target"
             [style]="{ width: '100%', height: 'calc(100% - 1px)'}"
        >
          <material-summary
              [domainDescription]="selMap?.domainDescription | async"
              [disabled]="!isEditEnabled()"
              [materialImageSrc]="materialImageSrc"
              [completeness]="selMap?.completeness | async"
              [warnings]="materialDetails?.warningsByMaterialId"
              [viewMode]="viewMode"
              [materialDetails]="materialDetails"
              [materialId]="selMap?.materialId | async"
              [materialChanges]="materialDetails.appliedChanges"
          ></material-summary>
          <div scMaterialDetails
               [page]="page"
               [showFieldWithNoValue]="showFieldWithNoValue | async"
               [dynamicFormGroup]="filteredDynamicFormGroup()"
               [descriptionSheetIndex]="descriptionSheetIndex"
               [plantSheetIndex]="plantSheetIndex"
               [plantSheet]="selMap?.plantSheet | async"
               [attachments]="selMap?.attachmentsInfo | async"
               [initialData]="materialDetails"
               [attachmentsInstances]="selMap?.getAttachmentsInstances | async"
               [imageInstances]="selMap?.getImageInstances | async"
               [categoriesFormGroup]="categoriesFormGroup"
               [suggestedAttributes]="materialDetails?.additionalMaterialInformation?.suggestGrAttributes"
               [selectedCategoriesNotSuggested]="categoriesNotSuggested"
               [viewMode]="viewMode"
               [disabled]="!isEditEnabled()"
               (onUpdateAlternativeUom)="handleUpdateAlternativeUom($event)"
               (inputChange)="handleReloadInitialData($event)"
               (categoryResetClicked)="handleCategoriesResetClicked($event)"
               (categorySelectManuallyClicked)="handleSelectManuallyClicked($event)"
               (categorySelectedChanged)="handleSelectedCategoryChanged($event)"
               (attachmentUpload)="handleAttachmentUpload($event)"
               (attachmentInstanceUpload)="handleAttachmentInstanceUpload($event)"
               (attachmentRemove)="handleAttachmentRemove($event)"
               (attachmentInstanceRemove)="handleAttachmentInstanceRemove($event)"
               (attachmentDownload)="handleAttachmentDownload($event)"
               (onSelectThumbnailImage)="handleSelectImageThumbnail($event)"
               [changeClassificationEnabled]="canEditClassification()"
               (relationshipDeletion)="handleRelationshipDeletion($event)"
            ></div>
            </div>
            </div>
            <div class="fixed-hf-footer bg-white flex justify-content-between gap-1 ">
                <div class="flex-0">
                    <button pButton class="p-button-fixed-size" type="button"
                            severity="secondary"
                            outlined="true"
                            (click)="close()" icon="fas fa-delete-left"
                            [label]="'smartCreation.modalDetails.closeButton' | translate">
                    </button>
                </div>
                <div class="flex-0 flex gap-2">
                    @if (isViewModeApprove()) {
                        <button pButton class="p-button-fixed-size" type="button"
                                (click)="handleReject()"
                                severity="danger"
                                outlined="true"
                                icon="fas fa-comment-xmark text-2xl"
                                [label]="'smartCreation.approval.rejectLabel' | translate">
                        </button>
                        @if (this.viewMode !== ViewModeEnum.GR_APPROVE && this.viewMode !== ViewModeEnum.APPROVAL_GR_DELETION) {
                            <button pButton class="p-button-fixed-size" type="button"
                                    severity="warning"
                                    outlined="true"
                                    icon="fas fa-comment-question text-2xl"
                                    (click)="handleAdditionalInfo()"
                                    [label]="'smartCreation.approval.additionalInfoLabel' | translate">
                            </button>
                        }
                    }
                </div>
                <div class="flex-0">

          @if (viewMode === ViewModeEnum.ENRICHMENT || viewMode === ViewModeEnum.EDIT || viewMode === ViewModeEnum.GR_EDIT) {
            <button pButton class="p-button-fixed-size" type="button"
                    severity="primary"
                    icon="fas fa-save"
                    [label]="(viewMode === ViewModeEnum.ENRICHMENT ? 'smartCreation.modalDetails.updateButton' : 'smartCreation.modalDetails.saveButton') | translate"
                    (click)="saveChanges()">
            </button>
          }

          @if (viewMode === ViewModeEnum.GR_DELETION) {
            <button pButton class="p-button-fixed-size" type="button"
                    severity="danger"
                    icon="fas fa-trash"
                    [label]="'smartCreation.modalDetails.deleteButton' | translate"
                    (click)="deleteGR()">
            </button>
          }

          @if (viewMode === ViewModeEnum.DETAILS || viewMode === ViewModeEnum.SUMMARY || viewMode === ViewModeEnum.PROCESS_DETAILS_VIEW || viewMode === ViewModeEnum.PROCESS_DETAILS_EDIT || viewMode === ViewModeEnum.GR_EDIT_DETAILS) {
            <button pButton class="p-button-fixed-size" type="button"
                    severity="secondary"
                    [icon]="(showFieldWithNoValue|async) ? 'fas fa-eye-slash' : 'fas fa-eye'"
                    [label]="('smartCreation.modalDetails.' + ((showFieldWithNoValue|async) ? 'hideFieldNoValueButton' : 'showFieldNoValueButton'))| translate"
                    (click)="toggleShowFieldWithNoValue()">
            </button>
          }

          @if (isViewModeApprove() && stepApprobation() === 0) {
            <button pButton class="p-button-fixed-size" type="button"
                    severity="primary"
                    icon="fas fa-save"
                    [label]="'smartCreation.modalDetails.updateButton'| translate"
                    (click)="saveChanges()">
            </button>
          }
          @if (isViewModeApprove() && stepApprobation() === 1) {
            <button pButton class="p-button-fixed-size" type="button"
                    severity="primary"
                    icon="fas fa-check text-2xl"
                    (click)="saveChanges()"
                    [label]="'smartCreation.approval.approveLabel' | translate"
            >
            </button>
          }

          @if (viewMode === ViewModeEnum.DETAILS || viewMode === ViewModeEnum.SUMMARY) {
            <p-overlayPanel #selectedItemsActionsMenu>
              <div class="w-full w-max-25rem">
                <div *ngFor="let item of actionMenuItems" [pTooltip]="actions">
                  <div class="p-menuitem-link flex gap-2 px-3 py-1 w-min-15rem"
                       (click)="!item?.disabled && doAction($event, item?.id)"
                       [class.disabled]="item?.disabled"
                  >
                    <span class="w-2rem-fixed" [class]="item.icon"></span>
                    <span> {{ item.label | translate }}</span>
                  </div>
                  <ng-template #actions>
                    <div class="flex align-items-center error-tooltip" *ngIf="item?.showTooltip">
                      <span>{{ 'search.menu.authorizations.' + item?.tooltip | translate:messageParam }}</span>
                    </div>
                    <div class="flex align-items-center" *ngIf="!item?.showTooltip">
                      <span>{{ item.label | translate }}</span>
                    </div>
                  </ng-template>
                </div>
              </div>
            </p-overlayPanel>
            <p-button
                class="p-button-fixed-size"
                severity="primary"
                type="button"
                icon="fa-solid fa-wrench"
                (click)="selectedItemsActionsMenu.toggle($event)"
                [label]="'role-management.filters.actions.title' | translate">
            </p-button>
          }
        </div>
      </div>
    }
  `,
  // changeDetection: ChangeDetectionStrategy.OnPush
})

export class MaterialDetailsModalComponent extends TamAbstractReduxComponent<SelectorMap> implements OnInit, OnDestroy {

  page: string = TamApePageType.EDIT;
  // materialDetails: SmartCreationMaterialDetail;
  viewMode: ViewModeEnum = ViewModeEnum.CREATE;

  dynamicFormGroup: WritableSignal<DynamicFormGroup[]> = signal<DynamicFormGroup[]>([]);
  descriptionSheetIndex = 0;
  plantSheetIndex: number;
  goldenRecordSheetIndex: number;
  categoriesNotSuggested: any;
  categoriesFormGroup: FormGroup;
  // warnings: WarningDetails[];
  materialImageSrc: string = null;
  materialId: string;
  treeCategoryDialogRef: DynamicDialogRef | undefined;
  validateForm = false;
  // suggestGrAttributes: SuggestGrAttributes;
  title: string;
  subTitle: string;
  rejectionPossibilities: Array<RejectionPossibility>;
  noteFlagMandatory: NoteFlagMandatory;
  rejectDialogRef: DynamicDialogRef | undefined;
  additionalInfoDialogRef: DynamicDialogRef | undefined;
  approveDialogRef: DynamicDialogRef | undefined;
  // materialChanges: any;
  confirmCreationDialogRef: DynamicDialogRef | undefined;
  processCreateDialogRef: DynamicDialogRef | undefined;
  showFieldWithNoValue: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  stepApprobation = computed(() => {
    if (!TamApePageUtils.isApproval(this.page)) {
      return 1;
    }
    return this.hasErrors() || this.signals?.hasChanges() ? 0 : 1;
  });

  isManuallyClicked = signal<boolean>(false);
  topDocNav: Observable<NavigationMenuItem[]>;
  bottomDocNav: Observable<NavigationMenuItem[]>;
  flattenCategories = flattenCategories;
  protected readonly ViewModeEnum = ViewModeEnum;
  // protected readonly getCanEditClassification = getCanEditClassification;
  canEditClassification: Signal<boolean>;
  authorizations: MaterialAuthorizations;
  reasonCode = 'action-not-enabled';
  actionMenuItems: ActionMenuItem[] = [];
  firstValidation = signal<boolean>(false);
  isGrGlobalViewLoaded = signal<boolean>(false);

  hasErrors = computed(() => {
    return this.dynamicFormGroup()?.some(frm => frm.errors?.length > 0) === true;
  });

  filteredDynamicFormGroup: Signal<DynamicFormGroup[]> = computed(() => {
    return this.dynamicFormGroup()?.filter(f => ObjectsUtils.isNotNoU(f));
  });
  source: string;

  constructor(public ref: DynamicDialogRef, public config: DynamicDialogConfig,
              private smartService: SmartCreationService,
              private service: MaterialsModalService,
              private mService: MaterialUpdateService,
              protected translate: TranslateService,
              protected tamTranslate: Tam4TranslationService,
              private tamTranslatePipe: TranslationPipeFactory,
              protected store: Store,
              private dialogService: DialogService,
              private materialsModalService: MaterialsModalService,
              private dynamicFormInputService: DynamicFormInputService,
              private injector: Injector) {
    super(translate, tamTranslate, store, storeSelectors);
    this.viewMode = this.config.data.type;
    this.source = this.config.data.source;


    this.canEditClassification = toSignal<boolean>(
      this.selMap?.material?.pipe(
        withLatestFrom(
          this.selMap?.changeDocClassificationEnabled,
          this.selMap?.currentPage
        ),
        map(([md, canChangeDocClassification, page]: [SmartCreationMaterialDetail, boolean, TamApePageType]) => {
          return getCanEditClassification(canChangeDocClassification,
            page,
            md?.additionalMaterialInformation?.domain,
            !ObjectsUtils.isStringBlank(md?.additionalMaterialInformation?.goldenRecord));
        })
      )
    );
  }

  get messageParam() {
    const requester = this.authorizations.canEdit?.params.requester || this.authorizations.canManageGoldenRecord?.params.requester;
    return {
      ...this.authorizations.canEdit?.params,
      ...this.authorizations.canManageGoldenRecord?.params,
      requester: this.tamTranslatePipe.transform(requester, 'user')
    };
  }

  ngOnInit() {
    super.ngOnInit();
    this.page = this.service.getPage(this.viewMode);
    this.service.action_setPage(this.page);

    if (this.viewMode === ViewModeEnum.APPROVE
      || this.viewMode === ViewModeEnum.GR_APPROVE
      || this.viewMode === ViewModeEnum.ENRICHMENT
      || this.viewMode === ViewModeEnum.EDIT
      || this.viewMode === ViewModeEnum.GR_ENRICHMENT) {
      this.service.action_doSetupInit();
    }

        // if (this.viewMode === ViewModeEnum.APPROVE || this.viewMode === ViewModeEnum.GR_APPROVE) {
        //     this.stepApprobation = 0;
        // }
    this.selMap?.materialId
        .subscribe(materialId => {
            this.materialId = materialId;
        });

    this.selMap?.process.subscribe(value => {
      if (value) {
        this.handleCreated(value);
      }
    });


    this.selMap?.material.subscribe((value) => {
            // this.materialDetails = value;
      this.refreshMe.next('need-refresh');
      this.initFormUsingSmartCreationMaterialDetail(value);
      this.initGrGlobalView(value?.additionalMaterialInformation);
    });

    this.selMap?.imageUUID.pipe(
      withLatestFrom(this.store.select(getOpenTextEnabled)),
      switchMap((value) => {
        return this.service.getImageBuffer(value[0], value[1]);
      })
    ).subscribe(value => {
      if (value) {
        this.materialImageSrc = this.smartService.getImageSrc(value.body, value.headers.get('Content-Type'));
      } else {
        this.materialImageSrc = null;
      }
    });

    this.selMap?.attachmentsUUID
      .pipe(distinctUntilChanged((previous, current) => previous?.length === current?.length))
      .subscribe(value => {
        if (value && value.length > 0) {
          // se GR non fare questa chiamata
                    this.service.action_doAttachmentInfoGet(value);
                }
            });

    this.selMap?.categoriesSheet.pipe(debounceTime(200)).subscribe(value => {
      if (value) {
        this.categoriesNotSuggested = {categoriesNotSuggested: []};
        this.categoriesFormGroup = this.smartService.getOrCreateSuggestedCategoriesFormGroup(value, value,
          this.categoriesFormGroup);
      }
    });

    this.selMap?.setup.subscribe(value => {
      if (value && value.rejectionPossibilities) {
        if (value.rejectionPossibilities) {
          this.rejectionPossibilities = value.rejectionPossibilities;
        }
      }

      if (value?.noteFlagMandatory) {
        this.noteFlagMandatory = value.noteFlagMandatory;
      }
    });
    combineLatest(
      [
        this.store.select(MaterialsModalSelectors.getOriginalSuggestedCategories),
        this.store.select(MaterialsModalSelectors.getSelectedCategories)
      ])
      .pipe(takeUntil(this.ngDestroy$), debounceTime(50))
      .subscribe(
        ([originalSuggestedCategories, selectedCategories]) => {
          if (ObjectsUtils.isObjectEmpty(selectedCategories)) {
            return;
          }

          this.categoriesFormGroup = this.smartService.getOrCreateSuggestedCategoriesFormGroup(
            (originalSuggestedCategories ?? {}),
            selectedCategories,
            this.categoriesFormGroup
          );
        });

    this.selMap?.modalDeleteOpened.pipe(
        withLatestFrom(this.selMap?.saveStatus)
      )
      .subscribe(([modalDeleteOpened, saveStatus]) => {
        if (modalDeleteOpened === false && saveStatus === 'COMPLETED') {
          this.ref.close();
        }
      });

    this.topDocNav = this.selMap?.material?.pipe(debounceTime(200), map((value) => {
      const nav: NavigationMenuItem[] = [];
      nav.push(attachmentTab);
      if (this.viewMode === ViewModeEnum.GR_APPROVE) {
        nav.push(thumbnailTab);
      }
      nav.push(classificationTab);

      return nav;
    }));

    this.bottomDocNav = this.selMap?.material.pipe(debounceTime(200), map(md => {
      const materialDetail: SmartCreationMaterialDetail = ObjectsUtils.forceCast<SmartCreationMaterialDetail>(md);
      const nav: NavigationMenuItem[] = [];

      if (materialDetail?.additionalMaterialInformation?.relationshipsDetails?.length) {
        nav.push(relationshipsTab);
      }

      if (CommonUtils.isGoldenRecord(materialDetail?.additionalMaterialInformation?.domain) && materialDetail?.additionalMaterialInformation?.instances?.length) {
        nav.push(grInstancesTab);
      }

      return nav;
    }));


    this.store.select(MaterialsModalSelectors.getMaterial).subscribe(
      material => {
        this.actionMenuItems = [];
        this.authorizations = material?.materialActionsAuthorization;
        const isGoldenRecord = CommonUtils.isGoldenRecord(material?.additionalMaterialInformation?.domain);
        const isDisabled = this.source === SOURCE_TYPE.COPY;
        this.populateActions(isDisabled, !isGoldenRecord, ACTION_EVENTS.EDIT.actionName, ACTION_EVENTS.EDIT.id);
        this.populateActions(isDisabled, isGoldenRecord, ACTION_EVENTS.EDIT_GOLDEN_RECORD.actionName, ACTION_EVENTS.EDIT_GOLDEN_RECORD.id);
        this.populateActions(isDisabled, isGoldenRecord, ACTION_EVENTS.DELETE_GOLDEN_RECORD.actionName, ACTION_EVENTS.DELETE_GOLDEN_RECORD.id);
        this.populateActions(isDisabled, !isGoldenRecord, ACTION_EVENTS.REQUEST_EDIT.actionName, ACTION_EVENTS.REQUEST_EDIT.id);
        this.populateActions(isDisabled, true, ACTION_EVENTS.REQUEST_EXTENSION.actionName, ACTION_EVENTS.REQUEST_EXTENSION.id);
        this.populateActions(isDisabled, true, ACTION_EVENTS.REQUEST_PLANT_EDIT.actionName, ACTION_EVENTS.REQUEST_PLANT_EDIT.id);
        this.populateActions(isDisabled, true, ACTION_EVENTS.COPY_MATERIAL.actionName, ACTION_EVENTS.COPY_MATERIAL.id);
      }
    );


    effect(() => {
      if (this.firstValidation() === false && !!this.dynamicFormGroup() && this.viewMode === ViewModeEnum.APPROVE) {
        this.firstValidation.set(true);
        setTimeout(() => {
          this.saveChanges(false);
        }, 500);
      }
    }, {injector: this.injector, allowSignalWrites: true});

  }

  initGrGlobalView(additionalInformation: AdditionalMaterialInformation) {
    if (!CommonUtils.isGoldenRecord(additionalInformation?.domain) || this.isGrGlobalViewLoaded()) { return; }
    const uuidInstances = additionalInformation?.instances?.map(i => i.materialId) ?? [];
    if (uuidInstances.length > 0) {
      this.service.action_doGetInstancesWithRelationships(uuidInstances);
      if (this.viewMode === ViewModeEnum.GR_APPROVE) {
        this.service.action_doGetInstancesImage(uuidInstances);
      }
    }
    if (this.viewMode === ViewModeEnum.GR_EDIT || this.viewMode === ViewModeEnum.DETAILS) {
      this.service.action_doGetInstancesAttachmentsAndImage(additionalInformation.materialId);
    }
    this.isGrGlobalViewLoaded.set(true);
  }

  ngOnDestroy() {
    super.ngOnDestroy();
    this.service.action_doDestroy();
  }

  isEditEnabled(): boolean {
    return this.viewMode === ViewModeEnum.EDIT || this.viewMode === ViewModeEnum.GR_EDIT ||
      this.viewMode === ViewModeEnum.ENRICHMENT || this.viewMode === ViewModeEnum.APPROVE || this.viewMode === ViewModeEnum.GR_APPROVE;
  }

  saveValidate() {
    switch (this.viewMode) {
      case ViewModeEnum.EDIT:
      case ViewModeEnum.GR_EDIT:
        this.handleSave();
        break;
      case ViewModeEnum.GR_ENRICHMENT:
        this.handleConfirmGrEnrichment();
        break;
      case ViewModeEnum.APPROVE:
      case ViewModeEnum.GR_APPROVE:
      case ViewModeEnum.APPROVAL_GR_DELETION:
        this.handleApprove();
        break;
      case ViewModeEnum.ENRICHMENT:
        this.handleConfirmUpdateCreation();
        break;
    }
  }

  handleRelationshipDeletion(event: { relationshipId: string, successfulOperation: boolean }) {
    const message: CommonMessageWrapper = {
      severity: event.successfulOperation ? MessageSeverityType.SUCCESS : MessageSeverityType.ERROR
    };
    this.service.showToaster(message);
    this.close();
  }

  initFormUsingSmartCreationMaterialDetail(smartCreationMaterialDetail: SmartCreationMaterialDetail) {
    this.dynamicFormGroup.update((_dynamicFormGroup) => {
      const errorMap = _dynamicFormGroup?.reduce((errorMap, sheet) => {
        return {...errorMap, [sheet?.key]: sheet?.errors};
      }, {});

      if (smartCreationMaterialDetail?.materialSheets) {
        const suggestGrAttributes = ObjectsUtils.isObjectEmpty(smartCreationMaterialDetail.additionalMaterialInformation?.suggestGrAttributes) ? null : smartCreationMaterialDetail.additionalMaterialInformation?.suggestGrAttributes;

        const newDynamicFormGroups = smartCreationMaterialDetail.materialSheets.map((sheet, index) => {
          let dfg: DynamicFormGroup = null;
          const smartItemTab = smartCreationMaterialDetail.materialSheets[index];
          const dynamicFormGroupElement = _dynamicFormGroup?.find(dfg => dfg?.key === sheet?.tabKey);

          if (sheet.tabKey === BaseMaterialEditorTabsKeys.DESCRIPTIONS) {
            dfg = this.smartService.getOrCreateSmartCreationDescriptionsFormGroup(
              smartItemTab, this.page, dynamicFormGroupElement, this.viewMode,
              (source: string, value: string) => this.onDescriptionInputChange(source, value),
              suggestGrAttributes);
            this.descriptionSheetIndex = index;
          } else {
            dfg = this.smartService.getOrCreateSmartCreationDynamicFormGroup(
              smartItemTab, this.page, dynamicFormGroupElement, this.viewMode,
              smartCreationMaterialDetail,
              null);
          }

          return dfg;
        });

        newDynamicFormGroups.forEach(tab => {
          tab.formGroup?.markAsPristine();
          tab.errors = errorMap[tab?.key];
          tab.hasMandatoryAttributes = tab.items.some(item => item.mandatory);
        });
        return newDynamicFormGroups.sort((a, b) => a?.order - b?.order);
      }
      return _dynamicFormGroup;
    });

  }

  close() {
    this.ref?.close();
    this.ref?.destroy();
  }

  saveChanges(manualClick: boolean = true) {
    this.validateForm = true;
    this.isManuallyClicked.set(manualClick);
    this.doValidate();
    // this.saveValidate();
  }

  doValidate() {
    this.store.select(MaterialsModalSelectors.getMaterial).pipe(
      withLatestFrom(this.store.select(getCurrentLanguage),
        this.store.select(getFallbackLanguages),
        this.store.select(MaterialsModalSelectors.getMaterialId),
        this.store.select(MaterialsModalSelectors.getPage)),
      first(),
      switchMap(([material, currentLanguage, fallbackLanguages, materialId, page]) =>
        this.validateMaterialData(material, currentLanguage, fallbackLanguages, materialId, page)
      )
    ).subscribe((validate: ValidateDataResponse) => {
      if (validate) {
        const msg = this.parseValidate(validate);
        if (msg && msg?.severity !== MessageSeverityType.SUCCESS) {
          // this.service.showToaster(msg);
          let action;
          if (msg?.severity === MessageSeverityType.WARNING) {
            action = new GlobalWarning(msg.messageBody);
          } else if (msg?.severity === MessageSeverityType.ERROR) {
            action = new GlobalError(msg.messageBody);
          }
          if (action) {
            this.store.dispatch(action);
          } else {
            this.store.dispatch(new GlobalError(msg.messageBody));
          }
        }
      }
      if (validate?.valid) {
        if (TamApePageUtils.isApproval(this.page) && this.stepApprobation() === 0) {
          this.service.action_approvalValidate();
        } else if (this.isManuallyClicked()) {
          this.saveValidate();
        }
      }

    });
  }

  // approveValidate(mode: 'approve' | 'reject' | 'additionalInformation') {
  //   this.approveMode = mode;
  //   this.saveChanges();
  // }

  deleteGR() {
    const modalCfg: DynamicDialogConfig = {
      ...defaultModalConfig,
      showHeader: false,
      closable: false,
      data: {
        noteFlagMandatory: this.noteFlagMandatory,
        headerTitle: 'worklists.grDelete.modalDetails.title'
      }
    };
    this.confirmCreationDialogRef = this.dialogService.open(GRConfirmDeleteDialog, modalCfg);

    this.confirmCreationDialogRef.onClose.subscribe((confirmPayload: ConfirmDeletionPayload) => {
      if (confirmPayload.note || confirmPayload.note === '') {
        this.service.action_doGRDeleteRequest(this.materialId, confirmPayload.note);
      } else {
        this.service.action_doEmptyValidateMaterialData();
      }
    });
  }

  handleSave() {
    const modalCfg: DynamicDialogConfig = {
      ...defaultModalConfig,
      showHeader: false,
      closable: false,
      // header: this.translate.instant('smartCreation.modalDetails.title'),
      data: {
        noteFlagMandatory: this.noteFlagMandatory,
        headerTitle: 'smartCreation.modalDetails.title'
      }
    };
    this.confirmCreationDialogRef = this.dialogService.open(SmartCreationConfirmCreationDialog, modalCfg);

    this.confirmCreationDialogRef.onClose.subscribe((approverComment: string) => {
      if (approverComment || approverComment === '') {
        this.service.action_doEditMaterial(this.materialId, approverComment);
      } else {
        this.service.action_doEmptyValidateMaterialData();
      }
    });
  }

  handleCreated(process: ProcessResponse) {
    this.close();
    this.service.saveNewMaterialSuccess(process);
  }

  onDescriptionInputChange(source: string, value: string) {

    const itemIndex = this.dynamicFormGroup()[this.descriptionSheetIndex].items.findIndex(x => x.id === source);
    if (itemIndex >= 0) {
      const {
        component,
        componentParams,
        ...currentControl
      } = this.dynamicFormGroup()[this.descriptionSheetIndex].items[itemIndex];
      currentControl.value = value;

      this.handleReloadInitialData(currentControl);
    }
  }

  handleUpdateAlternativeUom(payload: Tam4ComponentEvent<any, any>) {
    this.service.action_doUpdateAlternativeUom(payload);
  }

  handleReloadInitialData(event$: any) {
    EventUtils.stopPropagation(event$);
    // this.service.action_doReloadInitialData(event$);
  }

  handleMaterialImageUpload(file: File) {
    this.service.action_doImageUpload(file);
  }

  handleMaterialImageRemove() {
    this.service.action_doImageClean();
  }

  handleMaterialImageDownload() {
    this.service.action_doImageDownload();
  }

  handleAttachmentUpload(file: File) {
    this.service.action_doAttachmentUpload(file);
  }

  handleAttachmentInstanceUpload(request: AttachmentInstanceUploadRequest) {
        this.service.action_doAttachmentInstanceUpload(request);
    }

    handleAttachmentRemove(uuid: string) {
        this.service.action_doAttachmentClean(uuid);
    }

  handleAttachmentInstanceRemove(request: AttachmentInstanceCleanRequest) {
        this.service.action_doAttachmentInstanceClean(request);
    }

    handleAttachmentDownload(uuid: string) {
        this.service.action_doAttachmentDownload(uuid);
    }

  handleSelectImageThumbnail(uuid: string) {
        this.service.action_doSelectedThumbnails(uuid);
    }

    handleCategoriesResetClicked(taxonomy: string) {
        this.service.action_doSelectCategoryClean(taxonomy);
    }

  handleSelectManuallyClicked(taxonomy: string) {

    const modalCfg: DynamicDialogConfig = {
      ...defaultModalCreationConfig,
      header: null,
      showHeader: false,
      closable: false,
      data: taxonomy,
      width: "80vw",
      baseZIndex: 5000
    };

    this.treeCategoryDialogRef = this.dialogService.open(MaterialCategoriesTreeDialog, modalCfg);

    this.treeCategoryDialogRef.onClose.subscribe((selectedCategory: SmartCreationSelectedCategories) => {
      if (selectedCategory) {
        this.service.action_doSelectCategory(selectedCategory);
      }
    });
  }

  handleSelectedCategoryChanged(taxonomy: string) {
    const selectedCategory: SmartCreationSelectedCategories = {[taxonomy]: this.categoriesFormGroup.get(taxonomy).value};
    this.service.action_doSelectCategory(selectedCategory);
  }

  handleReject() {
    if (this.viewMode === ViewModeEnum.APPROVAL_GR_DELETION) {
      this.handleGRDeleteReject();
    } else {
      this.handleDefaultReject();
    }
  }

  handleGRDeleteReject() {
    const modalCfg: DynamicDialogConfig = {
      ...defaultModalConfig,
      showHeader: true,
      closable: false,
      data: {
        noteFlagMandatory: this.noteFlagMandatory,
        additionalInformation: false,
        headerTitle: 'smartCreation.approval.reject.dialog.title'
      }
    };

    this.rejectDialogRef = this.dialogService.open(GRDeleteRejectApprovalDialog, modalCfg);

    this.rejectDialogRef.onClose.subscribe((approverComment: string) => {
      if (approverComment) {
        this.service.action_doRejectGRDelete(approverComment);
      }
    });
  }

  handleDefaultReject() {
    const modalCfg: DynamicDialogConfig = {
      ...defaultModalConfig,
      showHeader: false,
      closable: false,
      data: {
        rejectionPossibilities: this.rejectionPossibilities,
        additionalInformation: false,
        noteFlagMandatory: this.noteFlagMandatory,
        header: 'smartCreation.approval.reject.dialog.title'

      }
    };

    this.rejectDialogRef = this.dialogService.open(SmartCreationRejectApprovalDialog, modalCfg);

    this.rejectDialogRef.onClose.subscribe((rejectedPayload: SmartCreationApprovalRejectPayload) => {
      if (rejectedPayload) {
        switch (this.viewMode) {
          case ViewModeEnum.GR_ENRICHMENT:
            this.service.action_doRejectGrEnrichment(rejectedPayload);
            break;
          default :
            this.service.action_doRejectDraft(rejectedPayload);
        }
        this.close();
      }
    });
  }

  handleConfirmUpdateCreation() {
    const modalCfg: DynamicDialogConfig = {
      ...defaultModalConfig,
      showHeader: false,
      closable: false,
      // header: this.translate.instant('smartCreation.confirmedEdit.dialog.title'),
      data: {
        noteFlagMandatory: this.noteFlagMandatory,
        headerTitle: 'smartCreation.confirmedEdit.dialog.title'
      }
    };

    this.confirmCreationDialogRef = this.dialogService.open(SmartCreationConfirmCreationDialog, modalCfg);

    this.confirmCreationDialogRef.onClose.subscribe((approverComment: string) => {
      if (approverComment || approverComment === '') {
        this.service.action_confirmUpdateEdit(approverComment);
        this.close();
      } else {
        this.service.action_doEmptyValidateMaterialData();
      }
    });
  }

  handleConfirmGrEnrichment() {
    const modalCfg: DynamicDialogConfig = {
      ...defaultModalConfig,
      showHeader: false,
      closable: false,
      data: {
        noteFlagMandatory: false,
        headerTitle: 'smartCreation.confirmedEdit.dialog.title'
      }
    };

    this.confirmCreationDialogRef = this.dialogService.open(SmartCreationConfirmCreationDialog, modalCfg);
    this.confirmCreationDialogRef.onClose.subscribe((approverComment: string) => {
      if (approverComment || approverComment === '') {
        this.service.action_confirmGrEnrichment(approverComment);
        this.close();
      }
    });
  }

  handleAdditionalInfo() {
    const modalCfg: DynamicDialogConfig = {
      ...defaultModalConfig,
      showHeader: false,
      closable: false,
      data: {
        rejectionPossibilities: this.rejectionPossibilities,
        additionalInformation: true,
        noteFlagMandatory: this.noteFlagMandatory,
        header: 'smartCreation.approval.additionalInfo.dialog.title',
      }
    };

    this.additionalInfoDialogRef = this.dialogService.open(SmartCreationRejectApprovalDialog, modalCfg);

    this.additionalInfoDialogRef.onClose.subscribe((rejectedPayload: SmartCreationApprovalRejectPayload) => {
      if (rejectedPayload) {
        this.service.action_doAdditionalInfoDraft(rejectedPayload);
        this.close();
      }
    });
  }

  handleApprove() {
    const modalCfg: DynamicDialogConfig = {
      ...defaultModalConfig,
      showHeader: false,
      closable: false,
      // header: this.translate.instant('smartCreation.approval.approve.dialog.title'),
      data: {
        noteFlagMandatory: this.noteFlagMandatory,
        headerTitle: 'smartCreation.approval.approve.dialog.title'
      }
    };

    this.approveDialogRef = this.dialogService.open(SmartCreationConfirmCreationDialog, modalCfg);

    this.approveDialogRef.onClose.subscribe((approverComment: string) => {
      if (approverComment) {
        if (this.viewMode === ViewModeEnum.APPROVAL_GR_DELETION) {
          this.service.action_doApproveGRDelete(approverComment);
        } else {
          this.service.action_doApproveDraft(approverComment);
          this.close();
        }
      }
    });
  }

  openGrDetails(materialId: string) {
    this.mService.loadDetailsModal(materialId, true);
  }


  toggleShowFieldWithNoValue(): void {
    const newValue = !this.showFieldWithNoValue.getValue();
    this.showFieldWithNoValue.next(newValue);
  }

  doAction($event: MouseEvent, id: any) {

    switch (id) {
      case ACTION_EVENTS.EDIT.id:
        this.edit();
        break;
      case ACTION_EVENTS.EDIT_GOLDEN_RECORD.id:
        this.editGoldenRecord();
        break;
      case ACTION_EVENTS.DELETE_GOLDEN_RECORD.id:
        this.deleteGR();
        break;
      case ACTION_EVENTS.REQUEST_EDIT.id:
        this.assign();
        break;
      case ACTION_EVENTS.REQUEST_EXTENSION.id:
        this.requestPlantExtension();
        break;
      case ACTION_EVENTS.REQUEST_PLANT_EDIT.id:
        this.requestPlantEdit();
        break;
      case ACTION_EVENTS.COPY_MATERIAL.id:
        this.copyMaterial();
        break;
      default:
        console.error("Action not allowed!! id: '" + id + "'");
    }
  }

  edit() {
    if (this.authorizations?.canEdit?.enabled) {
      this.service.action_doOpenMaterialEdit(this.materialId);
    } else {
      console.error('Operation not allowed');
    }
  }

  editGoldenRecord() {
    if (this.authorizations?.canManageGoldenRecord?.enabled) {
      this.service.action_doOpenMaterialEditGR(this.materialId, TamApePageType.GR_EDIT);
    } else {
      console.error('Operation not allowed');
    }
  }

  assign() {
    if (this.authorizations?.canAssign?.enabled) {
      this.store.dispatch({
        type: 'popups/assignment/open',
        payload: {
          materials: [this.materialId]
        }
      });
      this.close();
    } else {
      console.error('Operation not allowed');
    }
  }

  requestPlantExtension() {
    if (this.authorizations?.canExtendMaterial?.enabled) {
      this.store.dispatch({
        type: 'popups/plant-extension/open',
        payload: {
          materialId: this.materialId
        }
      });
      this.close();
    } else {
      console.error('Operation not allowed');
    }
  }

  requestPlantEdit() {
    if (this.authorizations?.canEditMaterialPlant?.enabled) {
      this.store.dispatch(new OpenPlantUpdatePopup({materialId: this.materialId}));
      this.close();
    } else {
      console.error('Operation not allowed');
    }
  }

  copyMaterial() {
    if (this.authorizations?.canCopyMaterial?.enabled) {
      this.materialsModalService.openCopyMaterialPage(this.materialId);
      this.close();
    } else {
      console.error('Operation not allowed');
    }
  }

  isViewModeApprove() {
    return this.viewMode === ViewModeEnum.APPROVE || this.viewMode === ViewModeEnum.GR_APPROVE || this.viewMode === ViewModeEnum.GR_ENRICHMENT || this.viewMode === ViewModeEnum.APPROVAL_GR_DELETION;
  }

  private populateActions(isDisabled: boolean, flagGoldenRecord: boolean, actionName: string, actionId: string) {
    if (flagGoldenRecord && this.authorizations && this.authorizations[actionName]?.reasonCode !== this.reasonCode) {
      const item = ALL_ACTION_MENU_ITEMS.find(menuItem => menuItem.id === actionId);
      item.disabled = isDisabled || !this.authorizations[actionName]?.enabled;
      item.showTooltip = !ObjectsUtils.isStringBlank(this.authorizations[actionName]?.reasonCode);
      item.tooltip = this.authorizations[actionName]?.reasonCode;
      this.actionMenuItems.push(item);
    }
  }

  private validateMaterialData(material: SmartCreationMaterialDetail, currentLanguage: string,
                               fallbackLanguages: string[], materialId: string, page: string = TamApePageType.EDIT): Observable<ValidateDataResponse> {
    const flattenedCategories = flattenCategories(material?.categoriesSheet);
    return material ? this.service.doValidateMaterialData(material, materialId,
      currentLanguage,
      fallbackLanguages,
      flattenedCategories,
      page) : EMPTY;
  }

  private parseValidate(validateInitialDataResult: ValidateDataResponse): CommonMessageWrapper {
    let messageOutput = null;
    this.checkErrorsOnLocalTabs(validateInitialDataResult);
    if (validateInitialDataResult.outcome === ResponseOutcomeType.SUCCESS) {
      this.dynamicFormGroup?.update(_dynamicFormGroup => {
        const validateBE = this.dynamicFormInputService.setValidateErrorsOnCreation(validateInitialDataResult, _dynamicFormGroup);
        if (validateBE.hasErrors) {
          messageOutput = validateBE.genericMessage ? validateBE.genericMessage : {
            messageBody: validateBE.genericMessage ? validateBE.genericMessage : this.translate.instant(
              'smartCreation.validation.checkFields')
          };
        }
        return [..._dynamicFormGroup];
      });
      return messageOutput;
    } else {
      if (validateInitialDataResult?.messages && validateInitialDataResult.messages?.length > 0) {
        return {
          severity: MessageSeverityType.ERROR,
          messageBody: validateInitialDataResult.messages[0]
        };
      } else {
        return HttpClientUtils.getErrorMessage();
      }
    }
  }

  private checkErrorsOnLocalTabs(validate: ValidateDataResponse) {
    if (!validate?.materialFormState?.tabStatuses || !Array.isArray(validate.materialFormState.tabStatuses)) {
      return;
    }
    this.topDocNav?.pipe(first()).subscribe(topNavItems => {
      this.updateTabErrors(validate, topNavItems);
    });
    this.bottomDocNav?.pipe(first()).subscribe(bottomNavItems => {
      this.updateTabErrors(validate, bottomNavItems);
    });
  }

  private updateTabErrors(validate: ValidateDataResponse, navItems: NavigationMenuItem[]) {
    navItems.forEach(item => item.errors = []);
    validate.materialFormState.tabStatuses.forEach(tabStatus => {
      const navItem = navItems.find(item => item.hook.toLowerCase() === tabStatus.tabKey.toLowerCase());
      if (navItem) {
        navItem.errors = validate.messages || [];
      }
    });
  }

}
