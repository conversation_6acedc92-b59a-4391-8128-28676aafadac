import { HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { AttachmentInstanceDetails, AttachmentsInfoResponse, CategoryKey } from '@creactives/models';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { saveAs } from 'file-saver';
import { Message, MessageService, TreeNode } from 'primeng/api';
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Observable, of } from 'rxjs';
import { map, switchMap, take, withLatestFrom } from 'rxjs/operators';
import {
  CommonMessageWrapper,
  CommonResponseWrapper,
  MessageSeverityType,
  ResponseOutcomeType,
  SearchMessage
} from 'src/app/models';
import { MaterialDetailsViewComponent } from 'src/app/modules/materials-modal/details/materials-details-view.component';
import { DynamicFormEventPayload } from 'src/app/modules/materials-modal/store/materials-modal.action-types';
import { HttpClientUtils, ObjectsUtils } from 'src/app/utils';
import { defaultModalStyleFullScreen } from 'src/app/utils/common.constants';
import { Tam4BaseConstants, Tam4ComponentEvent, TamApePageType } from '../../models';
import {
  extractGoldenRecordDetails,
  flattenMaterialFormControlList,
  flattenPlantFormControlList
} from '../materials-editor/common/materials-editor.function';
import { MaterialsEditorDao } from '../materials-editor/dao/materials-editor.dao';
import { BaseMaterialEditorTabsKeys, ReloadInitialDataPayload, ValidateDataResponse, ViewModeEnum } from '../materials-editor/models/material-editor.types';
import { SearchLinkRequest, SearchResults } from '../search/endpoint/search.model';
import { GetListsBrowseRequest, LinkInstancesGrSuccessfull, RefreshSearch } from '../search/store/search.actions';
import { Instance } from '../smart-creation/models/smart-creation-validation.types';
import {
  CategoryTreeLoadChildrenActionPayload,
  InstancesWithRelationshipsRequest,
  LinkUnlinkInstancesRequest,
  SaveApprovalRequest,
  SaveMaterialDetailPayload,
  SearchTreeCategoriesModal,
  SmartCreationApprovalRejectPayload,
  SmartCreationMaterialDetail,
  SmartCreationSelectedCategories,
  SmartCreationSuggestedCategories,
  SmartMaterialRejectRequest
} from '../smart-creation/models/smart-creation.types';
import { DeleteResponse } from '../worklists/endpoint/worklists.model';
import { MaterialDetailsModalComponent } from './edit-materials/materials-details-modal.component';
import { MaterialsModalDao } from './materials-modal.dao';
import {AttachmentInstanceCleanRequest, AttachmentInstanceUploadRequest, EditMaterialPayload} from './models/modals-modal.types';
import { MATERIALS_MODAL_ACTIONS } from './store/materials-modal.actions';
import { MaterialsModalSelectors } from './store/materials-modal.selectors';
import { MaterialsModalState } from './store/materials-modal.state';

@Injectable({
    providedIn: 'root'
})
export class MaterialsModalService {
    private readonly TIMEOUT_HACK = 200;

    constructor(
        private store: Store<MaterialsModalState>,
        private editorDao: MaterialsEditorDao,
        private toastService: MessageService,
        private dialogService: DialogService,
        protected translate: TranslateService,
        protected dao: MaterialsModalDao,
        protected ref: DynamicDialogRef,
        private messageService: MessageService,
        private router: Router
    ) {
    }

    action_doInit() {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.INIT());
    }

    action_doDestroy() {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.DESTROY());
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.INIT());
    }

    action_doOpenMaterialEdit(materialId: string) {
        this.closeMaterialModal();

        //  HACK!

        setTimeout(() => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_SET_PAGE(TamApePageType.EDIT));
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT(materialId));
        }, this.TIMEOUT_HACK);

    }

    action_doOpenMaterialDetails(materialId: string, isGoldenRecord: boolean, source?: string) {

        this.closeMaterialModal();
        setTimeout(() => {
            if (isGoldenRecord) {
                // fixme: the item_detail for GR doesn't exist so for now it's ITEM_DETAILS
                // this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_SET_PAGE(TamApePageType.GR_ITEM_DETAILS))
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_SET_PAGE(TamApePageType.ITEM_DETAILS));
            } else {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_SET_PAGE(TamApePageType.ITEM_DETAILS));
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DETAILS({materialId, source}));
        }, this.TIMEOUT_HACK);

    }

    action_doUnLoadMaterialDetails() {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.UNLOAD_DETAILS());
    }

    action_doLoadMaterialDetails(materialId: string) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.LOAD_DETAILS(materialId));
    }

    action_doSearchForLink(searchRequest: SearchLinkRequest) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.SEARCH_FOR_LINK(searchRequest));
    }

    action_doOpenSearchForLinkDialog() {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_SEARCH_FOR_LINK_DIALOG());
    }

    action_doLinkInstances(linkInstances: LinkUnlinkInstancesRequest) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.LINK_INSTANCES(linkInstances));
    }

    action_doUnlinkInstances(linkInstances: LinkUnlinkInstancesRequest) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.UNLINK_INSTANCES(linkInstances));
    }

    action_doSetupInit() {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT());
    }

    action_doSetupInitSuccess() {
        return (resp: CommonResponseWrapper<any>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_SUCCESS(resp));
        };
    }

    action_doSetupInitFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doApproveDraft(note: string) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.APPROVE_DRAFT(note));
    }

    action_doApproveDraftSuccess() {
        return (resp: CommonResponseWrapper<void>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.APPROVE_DRAFT_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.APPROVE_DRAFT_SUCCESS(resp));
        };
    }

    action_doApproveDraftFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.APPROVE_DRAFT_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_confirmUpdateEdit(approverComment: string) {
        const payload: SaveMaterialDetailPayload = {
            note: approverComment,
            startSummaryInitTime: null
        };
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.CONFIRM_UPDATE_EDIT(payload));
    }

    action_doConfirmUpdateEditSuccess() {
        return (resp: CommonResponseWrapper<void>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.CONFIRM_UPDATE_EDIT_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.CONFIRM_UPDATE_EDIT_SUCCESS(resp));
        };
    }

    action_doConfirmUpdateEditFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.APPROVE_DRAFT_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doRejectDraft(rejectPayload: SmartCreationApprovalRejectPayload) {
        const payload: SmartMaterialRejectRequest = {
            note: rejectPayload?.approverComment,
            cause: rejectPayload?.rejectionPossibility?.key,
            similarMaterialId: rejectPayload?.rejectionPossibility?.selectedOptions?.key
        };
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.REJECT_DRAFT(payload));
    }

    action_doRejectGrEnrichment(rejectPayload: SmartCreationApprovalRejectPayload) {
        const payload: SmartMaterialRejectRequest = {
            note: rejectPayload?.approverComment,
            cause: rejectPayload?.rejectionPossibility?.key,
            similarMaterialId: rejectPayload?.rejectionPossibility?.selectedOptions?.key
        };
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.REJECT_GR_ENRICHMENT(payload));
    }

    action_doRejectDraftSuccess() {
        return (resp: CommonResponseWrapper<void>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.REJECT_DRAFT_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.REJECT_DRAFT_SUCCESS(resp));
        };
    }

    action_doRejectDraftFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.REJECT_DRAFT_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doOpenEditApprovalSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT_APPROVAL_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT_APPROVAL_SUCCESS(resp));
        };
    }


    action_doOpenGRApprovalSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_GR_APPROVAL_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_GR_APPROVAL_SUCCESS(resp));
        };
    }

    action_doOpenEditAdditionalInfoSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT_ADDITIONAL_INFO_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT_ADDITIONAL_INFO_SUCCESS(resp));
        };
    }

    action_doOpenProcessDetailView(materialId: string) {
        this.closeMaterialModal();
        setTimeout(() => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_SET_PAGE(TamApePageType.EDIT));
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT(materialId));
        }, this.TIMEOUT_HACK);
    }

    action_doOpenProcessDetailEdit(processId: string, materialId: string, page: TamApePageType) {
        const payload: any = {
            processId,
            materialId
        };
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_SET_PAGE(page));
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_PROCESS_DETAILS_EDIT(payload));
    }

    action_doOpenProcessDetailViewSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_PROCESS_DETAILS_VIEW_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_PROCESS_DETAILS_VIEW_SUCCESS(resp));
        };
    }

    action_doOpenProcessDetailViewFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_PROCESS_DETAILS_VIEW_FAILURE(HttpClientUtils.getErrorWrapper()));
            this.closeMaterialModal();
        };
    }

    action_doOpenProcessDetailEditSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_PROCESS_DETAILS_EDIT_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_PROCESS_DETAILS_EDIT_SUCCESS(resp));
        };
    }

    action_doOpenProcessDetailEditFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_PROCESS_DETAILS_EDIT_FAILURE(HttpClientUtils.getErrorWrapper()));
            this.closeMaterialModal();
        };
    }

    action_doAdditionalInfoDraft(rejectPayload: SmartCreationApprovalRejectPayload) {
        const payload: SmartMaterialRejectRequest = {
            note: rejectPayload?.approverComment,
            cause: rejectPayload?.rejectionPossibility?.key,
            similarMaterialId: rejectPayload?.rejectionPossibility?.selectedOptions?.key
        };
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.ADDITIONAL_INFO_DRAFT(payload));
    }

    action_doAdditionalInfoDraftSuccess() {
        return (resp: CommonResponseWrapper<void>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.ADDITIONAL_INFO_DRAFT_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.ADDITIONAL_INFO_DRAFT_SUCCESS(resp));
        };
    }

    action_doAdditionalInfoDraftFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.ADDITIONAL_INFO_DRAFT_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doLoadMaterialDetailsSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.LOAD_DETAILS_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.LOAD_DETAILS_SUCCESS(resp));
        };
    }

    action_doOpenMaterialEditGR(materialId: string, page: TamApePageType) {
        this.closeMaterialModal();

        //  HACK!
        setTimeout(() => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_SET_PAGE(page));
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT_GR({
                materialId,
                viewMode: ViewModeEnum.GR_EDIT
            }));
        }, this.TIMEOUT_HACK);

    }

    action_doOpenMaterialDeleteGR(materialId: string, page: TamApePageType) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_SET_PAGE(page));
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DELETE_GR(materialId));
    }

    action_doOpenMaterialDeleteGRApproval(processId: string, materialId: string, page: TamApePageType) {
        const payload: SaveApprovalRequest = {
            processId,
            materialId
        };
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_SET_PAGE(page));
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DELETE_GR_APPROVAL(payload));
    }

    action_doUpdateAlternativeUom(payload: Tam4ComponentEvent<any, DynamicFormEventPayload>) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.UPDATE_ALTERNATIVE_UOM(payload?.payload));
    }

    action_doLoadMaterialSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT_SUCCESS(resp));
        };
    }

    action_doLoadMaterialFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT_FAILURE(HttpClientUtils.getErrorWrapper()));
            this.closeMaterialModal();
        };
    }

    action_doLoadMaterialViewSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DETAILS_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DETAILS_SUCCESS(resp));
        };
    }

    action_doLoadMaterialViewFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DETAILS_FAILURE(HttpClientUtils.getErrorWrapper()));
            this.closeMaterialModal();
        };
    }

    action_doLoadMaterialGRSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT_GR_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT_GR_SUCCESS(resp));
        };
    }

    action_doLoadMaterialGRFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_EDIT_GR_FAILURE(HttpClientUtils.getErrorWrapper()));
            this.closeMaterialModal();
        };
    }

    action_doLoadMaterialGRDeleteSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DELETE_GR_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DELETE_GR_SUCCESS(resp));
        };
    }

    action_doLoadMaterialGRDeleteFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DELETE_GR_FAILURE(HttpClientUtils.getErrorWrapper()));
            this.closeMaterialModal();
        };
    }

    action_doLoadMaterialGRDeleteApprovalSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DELETE_GR_APPROVAL_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DELETE_GR_APPROVAL_SUCCESS(resp));
        };
    }

    action_doLoadMaterialGRDeleteApprovalFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_DELETE_GR_APPROVAL_FAILURE(HttpClientUtils.getErrorWrapper()));
            this.closeMaterialModal();
        };
    }

    action_doSearchForLinkSuccess() {
        return (resp: CommonResponseWrapper<SearchResults>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.SEARCH_FOR_LINK_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SEARCH_FOR_LINK_SUCCESS(resp));
        };
    }

    action_doSearchForLinkFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SEARCH_FOR_LINK_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doLinkInstancesSuccess() {
        return (resp: CommonResponseWrapper<any>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.LINK_INSTANCES_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.LINK_INSTANCES_CONFIRM_CREATION(resp));
            this.store.dispatch(new GetListsBrowseRequest());
            this.store.dispatch(new LinkInstancesGrSuccessfull());
        };
    }

    action_doLinkInstancesFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.LINK_INSTANCES_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doUnlinkInstancesSuccess() {
        return (resp: CommonResponseWrapper<any>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.UNLINK_INSTANCES_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.UNLINK_INSTANCES_SUCCESS(resp));
        };
    }

    action_doUnlinkInstancesFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.UNLINK_INSTANCES_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doAttachmentInfoGet(attachmentUUIDs: string[]) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.ATTACHMENTS_INFO_GET(attachmentUUIDs));
    }

    action_doAttachmentInfoViewGet(attachmentUUIDs: string[], isOpenTextEnabled: boolean) {
        return this.editorDao.getAttachmentsInfoByIds(attachmentUUIDs, isOpenTextEnabled);
    }

    action_doAttachmentInfoGetSuccess() {
        return (resp: AttachmentsInfoResponse): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.ATTACHMENTS_INFO_GET_SUCCESS(resp));
        };
    }

    action_doAttachmentInfoGetFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.ATTACHMENTS_INFO_GET_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doEditMaterial(materialId: string, note: string) {
        const payload: EditMaterialPayload = {
            materialId,
            note
        };
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.SAVE_MATERIAL_EDIT(payload));
    }

    action_doEditMaterialSuccess() {
        return (resp: CommonResponseWrapper<any>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.SAVE_MATERIAL_EDIT_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SAVE_MATERIAL_EDIT_SUCCESS(resp));
        };
    }

    action_doEditMaterialFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SAVE_MATERIAL_EDIT_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doGRDeleteRequest(materialId: string, note: string) {
        const payload: EditMaterialPayload = {
            materialId,
            note
        };
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.REQUEST_GR_DELETE(payload));
    }

    action_doGRDeleteRequestSuccess() {
        return (resp: CommonResponseWrapper<DeleteResponse>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.REQUEST_GR_DELETE_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.REQUEST_GR_DELETE_SUCCESS(resp));
        };
    }

    action_doGRDeleteRequestFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.REQUEST_GR_DELETE_FAILURE(HttpClientUtils.getErrorWrapper(err.error.error, err.error.message)));
        };
    }

    action_doApproveGRDelete(note: string) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.APPROVE_GR_DELETE(note));
    }

    action_doApproveGRDeleteSuccess() {
        return (resp: CommonResponseWrapper<void>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.APPROVE_GR_DELETE_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.APPROVE_GR_DELETE_SUCCESS(resp));
        };
    }

    action_doApproveGRDeleteFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.APPROVE_GR_DELETE_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doRejectGRDelete(note: string) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.REJECT_GR_DELETE(note));
    }

    action_doRejectGRDeleteSuccess() {
        return (resp: CommonResponseWrapper<void>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.REJECT_GR_DELETE_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.REJECT_GR_DELETE_SUCCESS(resp));
        };
    }

    action_doRejectGRDeleteFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.REJECT_GR_DELETE_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doReloadInitialData(currentControl?: any, selectedCategory?: CategoryKey) {
        const payload: ReloadInitialDataPayload = {
            control: currentControl,
            category: selectedCategory,
        };
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.RELOAD_DOCUMENT(payload));
    }

    action_doReloadInitialDataSuccess() {
        return (resp: CommonResponseWrapper<any>): void => { // TODO: Replace any
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.RELOAD_INITIAL_DATA_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.RELOAD_INITIAL_DATA_SUCCESS(resp));
        };
    }

    action_doReloadInitialDataFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.RELOAD_INITIAL_DATA_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    showToaster(messageWrapper: CommonMessageWrapper) {
        let message: string;
        let title: string;

        switch (messageWrapper.severity.toString()) {
            case MessageSeverityType.SUCCESS:
                message = messageWrapper.messageBody ? messageWrapper.messageBody : 'layout.success.success-compited';
                title = messageWrapper.title ? messageWrapper.title : 'layout.success.take-in-charge';
                this.toastService.add({
                    ...Tam4BaseConstants.default_success_toast_options,
                    summary: title,
                    detail: message,
                    data: messageWrapper?.params
                });
                break;
            case MessageSeverityType.INFO:
                break;
            case MessageSeverityType.CUSTOM:
                break;
            case MessageSeverityType.WARNING:
                message = messageWrapper.messageBody ? messageWrapper.messageBody : '';
                title = messageWrapper.title ? messageWrapper.title : 'layout.warning.title';
                this.toastService.add({
                    ...Tam4BaseConstants.default_warn_toast_options,
                    summary: title,
                    detail: message,
                    data: messageWrapper?.params
                });
                break;
            case MessageSeverityType.ERROR:
                message = messageWrapper.messageBody ? messageWrapper.messageBody : 'layout.errors.unknown-error';
                title = messageWrapper.title ? messageWrapper.title : 'layout.errors.title';
                this.toastService.add({
                    ...Tam4BaseConstants.default_error_toast_options,
                    summary: title,
                    detail: message,
                    data: messageWrapper?.params
                });
                break;
        }
    }

    // ATTACHMENTS:

    action_doImageUpload(file: File) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.MATERIAL_IMAGE_UPLOAD(file));
    }

    action_doImageUploadSuccess() {
        return (resp: string): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.MATERIAL_IMAGE_UPLOAD_SUCCESS(resp));
        };
    }

    action_doImageUploadFailure(acceptedTypes?: string[]) {
        return (err): void => {
            this.store.dispatch(
                MATERIALS_MODAL_ACTIONS.MATERIAL_IMAGE_UPLOAD_FAILURE(HttpClientUtils.getErrorWrapper(null,
                    'errors.materials.file-type-not-allowed', {acceptedTypes})));
        };
    }

    action_doImageClean() {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.MATERIAL_IMAGE_CLEAN());
    }

    getImageBuffer(uuid: string, isOpenTextEnabled: boolean) {
        if (!uuid) {
            return of(null);
        }
        return this.editorDao.getImageBuffer(uuid, isOpenTextEnabled);
    }

    action_doImageDownload() {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.MATERIAL_IMAGE_DOWNLOAD());
    }

    downloadFile() {
        return (resp: HttpResponse<Blob>): void => {
            const file = resp.body as Blob;
            const fileName: string = resp.headers.get('Content-Disposition').split('=')[1].slice(1, -1);
            saveAs(file, fileName);
        };
    }

    action_doAttachmentUpload(file: File) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.ATTACHMENTS_UPLOAD(file));
    }

    action_doAttachmentInstanceUpload(request: AttachmentInstanceUploadRequest) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.ATTACHMENTS_INSTANCE_UPLOAD(request));
    }

    action_doAttachmentUploadSuccess() {
        return (resp: string): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.ATTACHMENTS_UPLOAD_SUCCESS(resp));
        };
    }

   action_doAttachmentUploadFailure(acceptedTypes?) {
        return (err): void => {
            this.store.dispatch(
          MATERIALS_MODAL_ACTIONS.ATTACHMENTS_UPLOAD_FAILURE(
              HttpClientUtils.getErrorWrapper(
                  null,
                  (acceptedTypes ? 'errors.materials.file-type-not-allowed' : err?.error?.message),
                  (acceptedTypes ? {acceptedTypes} : null)
              )
          )
      );
        };
    }

    action_doAttachmentInstanceUploadSuccess() {
        return (resp: string): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.ATTACHMENTS_INSTANCE_UPLOAD_SUCCESS(resp));
        };
    }

    action_doAttachmentInstanceUploadFailure(acceptedTypes?) {
        return (err): void => {
            this.store.dispatch(
                MATERIALS_MODAL_ACTIONS.ATTACHMENTS_INSTANCE_UPLOAD_FAILURE(
                    HttpClientUtils.getErrorWrapper(
                        null,
                        (acceptedTypes ? 'errors.materials.file-type-not-allowed' : err?.error?.message),
                        (acceptedTypes ? {acceptedTypes} : null)
                    )
                )
            );
        };
    }

    action_doAttachmentClean(uuid: string) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.ATTACHMENTS_CLEAN(uuid));
    }

    action_doAttachmentInstanceClean(request: AttachmentInstanceCleanRequest) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.ATTACHMENTS_INSTANCE_CLEAN(request));
    }

    action_doAttachmentDownload(uuid: string) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.ATTACHMENTS_DOWNLOAD(uuid));
    }

    action_doSelectedThumbnails(uuid: string) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.THUMBNAILS_SELECTED(uuid));
    }

    action_doSuggestCategoriesSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationSuggestedCategories>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.SUGGEST_CATEGORIES_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SUGGEST_CATEGORIES_SUCCESS(resp));
        };
    }

    action_doSuggestCategoriesFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SUGGEST_CATEGORIES_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doTreeCategories(treeCategoriesModal: SearchTreeCategoriesModal) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.TREE_CATEGORIES(treeCategoriesModal));
    }

    action_doTreeCategoriesSuccess() {
        return (resp: CommonResponseWrapper<TreeNode<CategoryKey>>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.TREE_CATEGORIES_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.TREE_CATEGORIES_SUCCESS(resp));
        };
    }

    action_doTreeCategoriesFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.TREE_CATEGORIES_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doTreeCategoriesClean() {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.TREE_CATEGORIES_CLEAN());
    }

    action_doSelectCategory(selectedCategory: SmartCreationSelectedCategories) {
        if (selectedCategory && this.hasNotNullValues(selectedCategory)) {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.SELECT_CATEGORY(selectedCategory));
        }
    }

    action_doSelectCategoryClean(taxonomy: string) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.SELECT_CATEGORY_CLEAN(taxonomy));
    }

    doValidateMaterialData(smartCreationMaterialDetail: SmartCreationMaterialDetail,
                           materialId: string,
                           language: string, fallbackLanguages: string[],
                           categoriesSelected,
                           page: string) {
        const materialDetailsRequests = flattenMaterialFormControlList(smartCreationMaterialDetail);
        const plantFormControlList = flattenPlantFormControlList(smartCreationMaterialDetail, null);
        const goldenRecordEnabled = smartCreationMaterialDetail.additionalMaterialInformation?.domain.includes('GR');
        const validateMaterialDataRequest: any = {
            page,
            description: smartCreationMaterialDetail.additionalMaterialInformation?.description,
            domain: smartCreationMaterialDetail.additionalMaterialInformation?.domain,
            client: smartCreationMaterialDetail.client,
            language,
            fallbackLanguages,
            categoriesSelected,
            materialFormControlList: materialDetailsRequests,
            materialId,
            plantToAddFormControlList: plantFormControlList,
            goldenRecordDetails: goldenRecordEnabled ? extractGoldenRecordDetails(smartCreationMaterialDetail) : null
        };
        return this.doInitialValidationMaterialData(validateMaterialDataRequest, page);
    }

    doInitialValidationMaterialData(validateMaterialDataRequest: SmartCreationMaterialDetail, page: string) {
        switch (page) {
            case TamApePageType.APPROVAL_GR_CREATION:
                return this.doValidateGrApprove().pipe(
                    switchMap((validation) => {
                        if (!validation || validation.valid) {
                            return this.dao.onValidateMaterialDataGet(validateMaterialDataRequest);
                        } else {
                            return of(validation);
                        }
                    })
                );
            default:
                return this.dao.onValidateMaterialDataGet(validateMaterialDataRequest);
        }
    }

    doValidateGrApprove(): Observable<ValidateDataResponse> {
        return this.store.select(MaterialsModalSelectors.getImagesInstances).pipe(
            withLatestFrom(this.store.select(MaterialsModalSelectors.getImageFromInstances)),
            take(1),
            map(([imageInstances, imageFromInstance]) => {
                if (!imageInstances || imageInstances.length === 0) {
                    return null;
                }
                if (!imageFromInstance) {
                    return {
                        valid: false,
                        materialFormState: {
                            tabStatuses: [{
                                tabKey: BaseMaterialEditorTabsKeys.THUMBNAILS,
                                tabName: BaseMaterialEditorTabsKeys.THUMBNAILS,
                                status: 'error',
                                statusMessage: 'Errors',
                                errorCount: 1
                            }],
                            fieldStatus: []
                        },
                        plantsFormStates: null,
                        messages: [this.translate.instant('smartCreation.modalDetails.validation.thumbnailsRequired')],
                        outcome: ResponseOutcomeType.ERROR
                    };
                }
                return null;
            })
        );
    }

    action_doValidateMaterialDataSuccess() {
        return (resp: CommonResponseWrapper<any>): void => { // TODO: Replace any
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.VALIDATE_MATERIAL_DATA_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.VALIDATE_MATERIAL_DATA_SUCCESS(resp));
        };
    }

    action_doValidateMaterialDataFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.VALIDATE_MATERIAL_DATA_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doEmptyValidateMaterialData() {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.EMPTY_VALIDATE_MATERIAL_DATA());
    }

    openMaterialModal(material: SmartCreationMaterialDetail, type?: ViewModeEnum, mode?: string, source?: string) {
        const modalCfg: DynamicDialogConfig = {
            ...defaultModalStyleFullScreen,
            closeOnEscape: false,
            closable: false,
            resizable: false,
            showHeader: false,
            data: {type, material, source}
        };

        const modalComponent: any = mode === 'VIEW' ? MaterialDetailsViewComponent : MaterialDetailsModalComponent;

        if (ObjectsUtils.isNotNoU(this.ref)) {
            this.ref.destroy();
            this.ref = this.dialogService.open(modalComponent, modalCfg);
            this.ref.onClose.subscribe(q => {
                this.ref.destroy();
                this.store.dispatch(new RefreshSearch());
            });
        }

    }

    closeMaterialModal() {
        // this.action_doInit()
        this.ref?.close();
    }

    openCopyMaterialPage(materialId: string) {
        this.router.navigate(['app', 'smart-creation', 'smart-creation', 'copy', materialId]);
    }

    getModalTreeCategoriesRequest(treeCategoriesModal: SearchTreeCategoriesModal,
                                  language: string): SearchMessage {
        return {
            codeText: treeCategoriesModal.code,
            descriptionText: treeCategoriesModal.description,
            node: treeCategoriesModal.taxonomy,
            applyOn: treeCategoriesModal.taxonomy,
            showOnlyOpen: true,
            language
        };
    }

    action_doOpenGrEditApprovalSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_GR_EDIT_APPROVAL_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.OPEN_GR_EDIT_APPROVAL_SUCCESS(resp));
        };
    }

    openGrMaterialModal(material: SmartCreationMaterialDetail, type?: ViewModeEnum, mode?: string) {
        const modalCfg: DynamicDialogConfig = {
            ...defaultModalStyleFullScreen,
            closeOnEscape: false,
            closable: false,
            resizable: false,
            showHeader: false,
            data: {type, material}
        };

        const modalComponent: any = mode === 'VIEW' ? MaterialDetailsViewComponent : MaterialDetailsModalComponent;

        this.dialogService.open(modalComponent, modalCfg);
    }

    action_confirmGrEnrichment(approverComment: string) {
        const payload: SaveApprovalRequest = {
            note: approverComment
        };
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.CONFIRM_GR_ENRICHMENT(payload));
    }

    action_confirmGrEnrichmentSuccess() {
        return (resp: CommonResponseWrapper<void>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.CONFIRM_GR_ENRICHMENT_FAILURE(resp));
                return;
            }
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.CONFIRM_GR_ENRICHMENT_SUCCESS(resp));
        };
    }

    getPage(viewMode: ViewModeEnum): string {
        switch (viewMode) {
            case ViewModeEnum.APPROVE:
                return TamApePageType.APPROVAL_EDIT;
            case ViewModeEnum.GR_APPROVE:
                return TamApePageType.APPROVAL_GR_CREATION;
            case ViewModeEnum.ENRICHMENT:
                return TamApePageType.EDIT;
            case ViewModeEnum.EDIT:
                return TamApePageType.EDIT;
            case ViewModeEnum.GR_EDIT:
                return TamApePageType.GR_EDIT;
            case ViewModeEnum.GR_ENRICHMENT:
                return TamApePageType.APPROVAL_GR_EDIT;
            case ViewModeEnum.GR_DELETION:
                return TamApePageType.GR_DELETION;
            case ViewModeEnum.APPROVAL_GR_DELETION:
                return TamApePageType.APPROVAL_GR_DELETION;
            default:
                return TamApePageType.ITEM_DETAILS;
        }
    }

    action_setPage(page: string) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.SETUP_INIT_SET_PAGE(page));
    }

    action_approvalValidate() {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.APPROVE_DRAFT_VALIDATE());
    }

    action_doTreeCategoriesChildren(treeCategoriesModal: SearchTreeCategoriesModal) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.TREE_CATEGORIES_LOADCHILDREN(treeCategoriesModal));
    }

    action_doTreeCategoriesChildrenSuccess(node: string, parents: string[]) {
        return (resp: CommonResponseWrapper<TreeNode<CategoryKey>>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MATERIALS_MODAL_ACTIONS.TREE_CATEGORIES_LOADCHILDREN_FAILURE(resp));
                return;
            }

            const payload: CategoryTreeLoadChildrenActionPayload = {
                node,
                parents,
                response: resp
            };

            this.store.dispatch(MATERIALS_MODAL_ACTIONS.TREE_CATEGORIES_LOADCHILDREN_SUCCESS(payload));
        };
    }

    action_doTreeCategoriesChildrenFailure() {
        return (err): void => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.TREE_CATEGORIES_LOADCHILDREN_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

  action_doGetInstancesWithRelationships(uuidInstances: string[]) {
      this.store.dispatch(MATERIALS_MODAL_ACTIONS.INSTANCES_WITH_RELATIONSHIPS(uuidInstances));
  }

  action_doGetInstancesAttachmentsAndImage(goldenRecordId: string) {
      this.store.dispatch(MATERIALS_MODAL_ACTIONS.INSTANCES_ATTACHMENTS_AND_IMAGE(goldenRecordId));
  }

  action_doGetInstancesAttachmentsAndImageSuccess() {
    return (resp: CommonResponseWrapper<Array<AttachmentInstanceDetails>>): void => {
      if (resp.outcome === ResponseOutcomeType.ERROR) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.INSTANCES_ATTACHMENTS_AND_IMAGE_FAILURE(resp));
        return;
      }
      this.store.dispatch(MATERIALS_MODAL_ACTIONS.INSTANCES_ATTACHMENTS_AND_IMAGE_SUCCESS(resp));
    };
  }

  action_doGetInstancesAttachmentsAndImageFailure() {
    return (err): void => {
      this.store.dispatch(MATERIALS_MODAL_ACTIONS.INSTANCES_ATTACHMENTS_AND_IMAGE_FAILURE(HttpClientUtils.getErrorWrapper()));
    };
  }

  action_doGetInstancesImage(instances: string[]) {
    this.store.dispatch(MATERIALS_MODAL_ACTIONS.INSTANCES_IMAGE(instances));
  }

  getInstancesWithRelationshipsRequest(uuidInstances: string[], language: string, fallbackLanguages: string[]): InstancesWithRelationshipsRequest {
    return {
      uuidInstances,
      language,
      fallbackLanguages
    };
  }

  action_doGetInstancesWithRelationshipsForGRSuccess() {
    return (resp: CommonResponseWrapper<Instance[]>): void => {
      if (resp.outcome === ResponseOutcomeType.ERROR) {
        this.store.dispatch(MATERIALS_MODAL_ACTIONS.INSTANCES_WITH_RELATIONSHIPS_FAILURE(resp));
        return;
      }
      this.store.dispatch(MATERIALS_MODAL_ACTIONS.INSTANCES_WITH_RELATIONSHIPS_SUCCESS(resp));
    };
  }

  action_doGetInstancesWithRelationshipsForGRFailure() {
    return (err): void => {
      this.store.dispatch(MATERIALS_MODAL_ACTIONS.INSTANCES_WITH_RELATIONSHIPS_FAILURE(HttpClientUtils.getErrorWrapper()));
    };
  }

    getSmartCreationTreeCategoriesRequest(treeCategoriesModal: SearchTreeCategoriesModal,
                                          node: string,
                                          language: string,
                                          onlyOpen: boolean): SearchMessage {
        return {
            codeText: treeCategoriesModal.code,
            descriptionText: treeCategoriesModal.description,
            node,
            applyOn: treeCategoriesModal.taxonomy,
            showOnlyOpen: onlyOpen,
            language
        };
    }

    private hasNotNullValues(selectedCategory: SmartCreationSelectedCategories) {
        return Object.values(selectedCategory).some(value => value !== null && typeof value !== 'undefined');
    }

    public saveNewMaterialSuccess(data: any) {

        const confirmToaster: Message = {
            ...Tam4BaseConstants.default_success_toast_options,
            key: "process-created-toaster",
            life: 5 * 60 * 1000,
            closable: true,
            sticky: true,
            summary: 'smartCreation.createdMaterial.dialog.title',
            detail: data ? 'smartCreation.createdMaterial.dialog.confirmationMessage' : 'smartCreation.createdMaterial.dialog.confirmationMessageProcessCode',
            data
        };
        this.messageService.add(confirmToaster);
        this.action_doInit();

        this.store.dispatch(new RefreshSearch());
    }
}
