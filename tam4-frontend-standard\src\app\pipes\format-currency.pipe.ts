import {Pipe, PipeTransform} from '@angular/core';
import {Store} from '@ngrx/store';
import {getCurrency, getLanguageForNumbers} from '../modules/layout/store/profile/profile.state';
import {Observable} from 'rxjs/internal/Observable';
import {map, withLatestFrom} from 'rxjs/operators';
import {DecimalPipe, getCurrencySymbol} from '@angular/common';

@Pipe({
    name: 'formatCurrency',
})
export class FormatCurrencyPipe implements PipeTransform {
    private currency$: Observable<string>;
    private languageForNumbers$: Observable<string>;

    constructor(store: Store<any>, private numberFormatter: DecimalPipe) {
        this.currency$ = store.select(getCurrency);
        this.languageForNumbers$ = store.select(getLanguageForNumbers);
    }

    transform(value: number, format: 'narrow' | 'wide'): Observable<string> {
    format = format || 'wide';
    console.log(value, format);
    return this.currency$.pipe(
            withLatestFrom(this.languageForNumbers$),
            map(([currency, language]) => {
                console.log("ON PIPE ... ", currency, language);
                const symbol = getCurrencySymbol(currency, 'narrow');
                if (format == 'narrow') {
                    const numb = this.roundNumber(value || 0, language);
                    return `${symbol} ${numb}`;
                } else {
                    const numb = this.numberFormatter.transform(
                        value || 0,
                        '1.0-2',
                        language
                    );
                    return `${symbol} ${numb}`;
                }
            })
        );
    }

    roundNumber(numberWithCommas: number, lang) {
        if (Math.abs(numberWithCommas) > 1000) {
            if (Math.abs(numberWithCommas) > 1000000) {
                const newNumber = numberWithCommas / 1000000;
                return `${this.numberFormatter.transform(newNumber, '1.0-2', lang)}M`;
            }
            const newNumber = numberWithCommas / 1000;
            return `${this.numberFormatter.transform(newNumber, '1.0-0', lang)}K`;
        }
        return this.numberFormatter.transform(numberWithCommas, '1.0-2', lang);
    }
}
