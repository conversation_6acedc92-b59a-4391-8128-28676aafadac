@import "../../../../styles/variables.scss";

.image-select-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  border: 1px solid var(--gray-200, $gray-200);
  box-shadow: none;
}

.image-select-container.image-selected {
  border: 3px solid var(--cre-success-dark, $cre-success-dark);
  box-shadow: 0 0 10px var(--cre-success-dark, $cre-success-dark);
}

.image-select-container.image-disabled {
   border: 3px solid var(--cre-text-muted, $cre-text-muted);
   box-shadow: 0 0 10px var(--cre-text-muted, $cre-text-muted);
 }

.image-select-carousel {
  max-height: 150px;
  object-fit: contain;
  margin-bottom: 1rem;
}
