import { Injectable } from '@angular/core';
import { ApproveGRDeleteRequest, RejectGRRequest } from '@creactives/models';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { DialogService, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { filter, tap, withLatestFrom } from 'rxjs';
import { CommonResponseWrapper, TAm4StandardFieldNames, TamApePageType } from 'src/app/models';
import {
  SmartCreationAction_Tree_Categories_Loadchildren
} from "src/app/modules/smart-creation/store/smart-creation.actions";
import { HttpClientUtils, ReduxUtils } from 'src/app/utils';
import { getCurrentLanguage } from '../../bulk-upload/store/profile/profile.state';
import {
  getAuthorizedClients,
  getCurrentUserId,
  getFallbackLanguages,
  getOpenTextEnabled
} from '../../layout/store/profile/profile.state';
import { LayoutSelectors } from "../../layout/store/reducers";
import { AttachmentConfig } from "../../layout/store/reducers/layout.reducer";
import {
  flattenMaterialFormControlList,
  flattenPlantFormControlList
} from "../../materials-editor/common/materials-editor.function";
import { MaterialsEditorDao } from '../../materials-editor/dao/materials-editor.dao';
import { ViewModeEnum } from '../../materials-editor/models/material-editor.types';
import { getGlobalFilters, GlobalFilterValues } from '../../materials/store/global-filter/global-filter.state';
import {
  SmartCreationCreatedMaterialDialog
} from '../../smart-creation/components/smart-creation-created-material.dialog';
import { SmartCreationLinkUnlinkConfirm } from '../../smart-creation/components/smart-creation-link-unlink-confirm';
import {
  AlternativeUnitsOfMeasure,
  EditApprovalSetupResponse
} from '../../smart-creation/models/smart-creation-validation.types';
import {
  SaveApprovalRequest,
  SmartCreationMaterialDetail,
  SmartCreationSelectedCategories,
  SmartCreationSuggestedCategories,
  SmartFieldConfiguration
} from '../../smart-creation/models/smart-creation.types';
import { MaterialsModalDao } from '../materials-modal.dao';
import { MaterialsModalService } from '../materials-modal.service';
import {
    AttachmentInstanceUpdateRequest,
    MaterialDetailsEditSaveRequest,
    MaterialDetailsReloadLocalRequest,
    MaterialDetailsReloadRequest,
    MaterialsEditLoadRequest
} from '../models/modals-modal.types';
import {
    MATERIALS_MODAL_ACTION_NAMES,
    MATERIALS_MODAL_ACTIONS,
    MaterialsModalAction_Approve_Draft,
    MaterialsModalAction_ApproveGR_Delete,
    MaterialsModalAction_Attachments_Download, MaterialsModalAction_Attachments_Instance_Upload,
    MaterialsModalAction_AttachmentsInfo_Get,
    MaterialsModalAction_Confirm_Gr_Enrichment,
    MaterialsModalAction_Confirm_Update_Edit,
    MaterialsModalAction_Instances_Attachments_And_Image,
    MaterialsModalAction_Instances_Image,
    MaterialsModalAction_Instances_With_Relationships,
    MaterialsModalAction_LinkInstances_Success,
    MaterialsModalAction_LinkInstancesConfirmCreation,
    MaterialsModalAction_LinkInstancesConfirmOpen,
    MaterialsModalAction_LoadDetails,
    MaterialsModalAction_Material_Image_Download,
    MaterialsModalAction_Material_Image_Upload,
    MaterialsModalAction_OpenDeleteGR,
    MaterialsModalAction_OpenDeleteGRApproval,
    MaterialsModalAction_OpenDetails,
    MaterialsModalAction_OpenEdit,
    MaterialsModalAction_OpenEditApproval,
    MaterialsModalAction_OpenEditApproval_Success,
    MaterialsModalAction_OpenEditGR,
    MaterialsModalAction_OpenGrApproval,
    MaterialsModalAction_OpenGrEditApproval,
    MaterialsModalAction_OpenProcessDetailsEdit,
    MaterialsModalAction_OpenProcessDetailsEdit_Success,
    MaterialsModalAction_OpenProcessDetailsView,
    MaterialsModalAction_Reject_Draft,
    MaterialsModalAction_Reject_Gr_Enrichment,
    MaterialsModalAction_RejectGR_Delete,
    MaterialsModalAction_Reload_Initial_Data,
    MaterialsModalAction_RequestGR_Delete,
    MaterialsModalAction_SaveMaterial_Edit,
    MaterialsModalAction_SearchForLink,
    MaterialsModalAction_Select_Category,
    MaterialsModalAction_Tree_Categories,
    MaterialsModalAction_UnlinkInstances,
    MaterialsModalActionTypes
} from './materials-modal.actions';
import { MaterialsModalSelectors } from './materials-modal.selectors';
import { MaterialsModalState, TAM_MATERIALS_MODAL_FEATURE_NAME } from './materials-modal.state';

@Injectable({providedIn: 'root'})
export class MaterialsModalEffects {

    onLoadMaterialDetails = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getPage)
        ),
        tap((inputs: [MaterialsModalAction_OpenEdit, string, string[], string]) => {
            this.service.openMaterialModal({materialId: inputs[0]?.payload}, ViewModeEnum.EDIT);
            this.dao.loadMaterial(
                {materialId: inputs[0].payload, language: inputs[1], fallbackLanguages: inputs[2], page: inputs[3]},
                this.service.action_doLoadMaterialSuccess(),
                this.service.action_doLoadMaterialFailure());
        })
    ), ReduxUtils.noDispatch());
    onSetupEditApproval = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.SETUP_INIT),
        tap(a$ => {
            this.dao.setupEditApprovalData(
                this.service.action_doSetupInitSuccess(),
                this.service.action_doSetupInitFailure());
        })
    ), ReduxUtils.noDispatch());
    onApproveDraft = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.APPROVE_DRAFT),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getCurrentUserId),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getMaterial),
            this.store.select(MaterialsModalSelectors.getImageUUID),
            this.store.select(MaterialsModalSelectors.getAttachmentsUUID),
            this.store.select(MaterialsModalSelectors.getAlternativeUomList),
            this.store.select(MaterialsModalSelectors.getSelectedCategories),
            this.store.select(MaterialsModalSelectors.getProcessId),
            this.store.select(MaterialsModalSelectors.getMaterialId),
            this.store.select(MaterialsModalSelectors.getPage),
            this.store.select(MaterialsModalSelectors.getImageFromInstances)),
        tap((inputs: [
            MaterialsModalAction_Approve_Draft, string, number,
            string[], SmartCreationMaterialDetail, string, string[],
            AlternativeUnitsOfMeasure[], SmartCreationSelectedCategories, string, string, string, string
        ]) => {
            const request = this.getRequest(inputs);

            this.dao.approveEdit(inputs[2], request,
                this.service.action_doApproveDraftSuccess(),
                this.service.action_doApproveDraftFailure());
        })
    ), ReduxUtils.noDispatch());
    onConfirmUpdateEdit = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.CONFIRM_UPDATE_EDIT),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getCurrentUserId),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getMaterial),
            this.store.select(MaterialsModalSelectors.getImageUUID),
            this.store.select(MaterialsModalSelectors.getAttachmentsUUID),
            this.store.select(MaterialsModalSelectors.getAlternativeUomList),
            this.store.select(MaterialsModalSelectors.getSelectedCategories),
            this.store.select(MaterialsModalSelectors.getProcessId),
            this.store.select(MaterialsModalSelectors.getMaterialId),
            this.store.select(MaterialsModalSelectors.getPage)),
        tap((inputs: [
            MaterialsModalAction_Confirm_Update_Edit, string, number,
            string[], SmartCreationMaterialDetail, string, string[],
            AlternativeUnitsOfMeasure[], SmartCreationSelectedCategories, string, string, string
        ]) => {

            const request = this.getRequest(inputs);

            this.dao.saveApproveEdit(inputs[2], request,
                this.service.action_doConfirmUpdateEditSuccess(),
                this.service.action_doConfirmUpdateEditFailure());
        })
    ), ReduxUtils.noDispatch());
    onRejectDraft = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.REJECT_DRAFT),
        withLatestFrom(this.store.select(MaterialsModalSelectors.getProcessId)),
        tap((inputs: [MaterialsModalAction_Reject_Draft, string]) => {
            const request = inputs[0].payload;
            request.processId = inputs[1];
            this.dao.onRejectDraft(request,
                this.service.action_doRejectDraftSuccess(),
                this.service.action_doRejectDraftFailure());
        })
    ), ReduxUtils.noDispatch());
    onAdditionalInformationDraft = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.ADDITIONAL_INFO_DRAFT),
        withLatestFrom(this.store.select(MaterialsModalSelectors.getProcessId),
            this.store.select(getCurrentUserId),
            this.store.select(MaterialsModalSelectors.getMaterialId)),
        tap((inputs: [MaterialsModalAction_Reject_Draft, string, number, string]) => {
            const request = inputs[0].payload;
            request.processId = inputs[1];
            request.materialId = inputs[3];
            this.dao.additionalInfoEdit(inputs[2], request,
                this.service.action_doAdditionalInfoDraftSuccess(),
                this.service.action_doAdditionalInfoDraftFailure());
        })
    ), ReduxUtils.noDispatch());
    onOpenEditApproval = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_APPROVAL),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getPage)
        ),
        tap(([action, currentLang, fallbackLangs, page]: [MaterialsModalAction_OpenEditApproval, string, string[], string]) => {
            this.dao.getEditApprovalData(
                action.payload.materialId, action.payload.processId, currentLang, action.payload.page,
                this.service.action_doOpenEditApprovalSuccess(),
                this.service.action_doLoadMaterialFailure());

        })
    ), ReduxUtils.noDispatch());
    onOpenEditApprovalSuccess = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_APPROVAL_SUCCESS),
        tap((input: MaterialsModalAction_OpenEditApproval_Success) => {
            this.service.openMaterialModal(input.payload?.data, ViewModeEnum.APPROVE);
        })
    ), ReduxUtils.noDispatch());
    onOpenGrApproval = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_GR_APPROVAL),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages)
        ),
        tap((inputs: [MaterialsModalAction_OpenGrApproval, string, string[]]) => {
            this.dao.getEditGRApprovalData(
                inputs[0].payload.materialId, inputs[0].payload.processId, inputs[1],
                this.service.action_doOpenGRApprovalSuccess(),
                this.service.action_doLoadMaterialFailure());

        })
    ), ReduxUtils.noDispatch());
    onOpenGrApprovalSuccess = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_GR_APPROVAL_SUCCESS),
        tap((input: MaterialsModalAction_OpenEditApproval_Success) => {
            this.service.openMaterialModal(input.payload?.data, ViewModeEnum.GR_APPROVE);
        })
    ), ReduxUtils.noDispatch());
    onOpenEditAdditionalInfo = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_ADDITIONAL_INFO),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getPage)
        ),
        tap(([action, currentLang, fallbackLangs, page]: [MaterialsModalAction_OpenEditApproval, string, string[], string]) => {
            this.dao.getEditApprovalData(
                action.payload.materialId, action.payload.processId, currentLang, action.payload.page,
                this.service.action_doOpenEditAdditionalInfoSuccess(),
                this.service.action_doLoadMaterialFailure());

        })
    ), ReduxUtils.noDispatch());
    onOpenEditAdditionalInfoSuccess = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_ADDITIONAL_INFO_SUCCESS),
        tap((input: MaterialsModalAction_OpenEditApproval_Success) => {
            this.service.openMaterialModal(input.payload?.data, ViewModeEnum.ENRICHMENT);
        })
    ), ReduxUtils.noDispatch());
    onOpenProcessDetailsView = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_PROCESS_DETAILS_VIEW),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getPage)
        ),
        tap((inputs: [MaterialsModalAction_OpenProcessDetailsView, string, string[], string]) => {
            this.dao.loadMaterial(
                {
                    materialId: inputs[0].payload,
                    language: inputs[1],
                    fallbackLanguages: inputs[2],
                    page: inputs[3]
                } as MaterialsEditLoadRequest,
                this.service.action_doOpenProcessDetailEditSuccess(),
                this.service.action_doOpenProcessDetailEditFailure());

        })
    ), ReduxUtils.noDispatch());
    onOpenProcessDetailsEdit = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_PROCESS_DETAILS_EDIT),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getPage)
        ),
        tap((inputs: [MaterialsModalAction_OpenProcessDetailsEdit, string, string[], string]) => {
            this.dao.getEditApprovalData(
                inputs[0].payload.materialId,
                inputs[0].payload.processId,
                inputs[1],
                inputs[0].payload.page,
                this.service.action_doOpenProcessDetailEditSuccess(),
                this.service.action_doOpenProcessDetailEditFailure());

        })
    ), ReduxUtils.noDispatch());
    onOpenProcessDetailsEditSuccess = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_PROCESS_DETAILS_EDIT_SUCCESS),
        tap((input: MaterialsModalAction_OpenProcessDetailsEdit_Success) => {
            this.service.openMaterialModal(input.payload?.data, ViewModeEnum.PROCESS_DETAILS_EDIT);
        })
    ), ReduxUtils.noDispatch());
    onLoadMaterialView = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.LOAD_DETAILS),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getPage)
        ),
        tap((inputs: [MaterialsModalAction_LoadDetails, string, string[], string]) => {
            this.service.openMaterialModal({materialId: inputs[0].payload}, ViewModeEnum.DETAILS);
            this.dao.loadMaterialView(
                {
                    materialId: inputs[0].payload,
                    language: inputs[1],
                    fallbackLanguages: inputs[2],
                    page: inputs[3] || TamApePageType.ITEM_DETAILS
                },
                this.service.action_doLoadMaterialDetailsSuccess(),
                this.service.action_doLoadMaterialFailure());

        })
    ), ReduxUtils.noDispatch());
    onLoadMaterialDetailsView = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_DETAILS),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getPage)
        ),
        tap((inputs: [MaterialsModalAction_OpenDetails, string, string[], string]) => {
            this.service.openMaterialModal({materialId: inputs[0].payload.materialId}, ViewModeEnum.DETAILS, undefined, inputs[0].payload.source);
            this.dao.loadMaterialView(
                {
                    materialId: inputs[0].payload.materialId,
                    language: inputs[1],
                    fallbackLanguages: inputs[2],
                    page: inputs[3] || TamApePageType.ITEM_DETAILS
                },
                this.service.action_doLoadMaterialViewSuccess(),
                this.service.action_doLoadMaterialViewFailure());

        })
    ), ReduxUtils.noDispatch());
    onLoadMaterialEditGR = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_GR),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getPage)
        ),
        tap((inputs: [MaterialsModalAction_OpenEditGR, string, string[], string]) => {
            this.service.openMaterialModal({materialId: inputs[0].payload.materialId}, inputs[0].payload.viewMode);
            this.dao.loadMaterial(
                {
                    materialId: inputs[0].payload.materialId,
                    language: inputs[1],
                    fallbackLanguages: inputs[2],
                    page: inputs[3] || TamApePageType.GR_EDIT
                },
                this.service.action_doLoadMaterialGRSuccess(),
                this.service.action_doLoadMaterialGRFailure());

    })
  ), ReduxUtils.noDispatch());

  onSearchForLink = createEffect(() => this.actions$.pipe(
    ofType(MATERIALS_MODAL_ACTION_NAMES.SEARCH_FOR_LINK),
    withLatestFrom(
      this.store.select(getCurrentLanguage),
      this.store.select(getFallbackLanguages),
      this.store.select(getGlobalFilters),
      this.store.select(getAuthorizedClients),
      this.store.select(MaterialsModalSelectors.getMaterial)
    ),
    tap((inputs: [MaterialsModalAction_SearchForLink, string, string[], GlobalFilterValues, string[], SmartCreationMaterialDetail]) => {
      const mdDomain = inputs[3]?.mdDomain || 'M';

      let instancesClients: string[] = [];
      if (inputs[5]?.additionalMaterialInformation?.instances) {
        instancesClients = inputs[5].additionalMaterialInformation.instances.map(i => i.materialKey.client);
      }

      const payload = {
        ...inputs[0].payload,
        language: inputs[1],
        fallbackLanguages: inputs[2],
        mdDomain: mdDomain,
        authorizedClients: inputs[4],
        instancesClients: instancesClients
      };
      this.dao.searchForLink(payload,
        this.service.action_doSearchForLinkSuccess(),
        this.service.action_doSearchForLinkFailure());
    })
  ), ReduxUtils.noDispatch());

  onLinkInstances = createEffect(() => this.actions$.pipe(
    ofType(MATERIALS_MODAL_ACTION_NAMES.LINK_INSTANCES),
    withLatestFrom(this.store.select(getCurrentLanguage),
      this.store.select(getFallbackLanguages)),
    tap((input: [MaterialsModalAction_UnlinkInstances, string, string[]]) => {
      this.dao.linkUnlinkInstances({...input[0].payload, language: input[1], fallbackLanguages: input[2]},
        this.service.action_doLinkInstancesSuccess(),
        this.service.action_doLinkInstancesFailure());
    })
  ), ReduxUtils.noDispatch());

  onLinkInstancesConfirmCreation = createEffect(() => this.actions$.pipe(
    ofType(MATERIALS_MODAL_ACTION_NAMES.LINK_INSTANCES_CONFIRM_CREATION),
    tap((input: MaterialsModalAction_LinkInstancesConfirmCreation) => {
      this.openLinkedUnlinkedInstancesDialog(input.payload?.data, 'LINK');
    })
  ), ReduxUtils.noDispatch());

  onUnlinkInstances = createEffect(() => this.actions$.pipe(
    ofType(MATERIALS_MODAL_ACTION_NAMES.UNLINK_INSTANCES),
    withLatestFrom(this.store.select(getCurrentLanguage),
      this.store.select(getFallbackLanguages)),
    tap((input: [MaterialsModalAction_UnlinkInstances, string, string[]]) => {
      this.dao.linkUnlinkInstances({...input[0].payload, language: input[1], fallbackLanguages: input[2]},
        this.service.action_doUnlinkInstancesSuccess(),
        this.service.action_doUnlinkInstancesFailure());
    })
  ), ReduxUtils.noDispatch());

  onUnlinkInstancesSuccess = createEffect(() => this.actions$.pipe(
    ofType(MATERIALS_MODAL_ACTION_NAMES.UNLINK_INSTANCES_SUCCESS),
    tap((input: MaterialsModalAction_LinkInstances_Success) => {
      this.openLinkedUnlinkedInstancesDialog(input.payload.data, 'UNLINK');
        })
    ), ReduxUtils.noDispatch());
    onLoadMaterialDeleteGR = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_DELETE_GR),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getPage)
        ),
        tap((inputs: [MaterialsModalAction_OpenDeleteGR, string, string[], string]) => {
            this.service.openMaterialModal(null, ViewModeEnum.GR_DELETION);
            this.dao.loadMaterial(
                {
                    materialId: inputs[0].payload,
                    language: inputs[1],
                    fallbackLanguages: inputs[2],
                    page: inputs[3] || TamApePageType.GR_DELETION
                },
                this.service.action_doLoadMaterialGRDeleteSuccess(),
                this.service.action_doLoadMaterialGRDeleteFailure());

        })
    ), ReduxUtils.noDispatch());
    onLoadMaterialDeleteGRApproval = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_DELETE_GR_APPROVAL),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getPage)
        ),
        tap((inputs: [MaterialsModalAction_OpenDeleteGRApproval, string, string[], string]) => {
            this.service.openMaterialModal(null, ViewModeEnum.APPROVAL_GR_DELETION);
            this.dao.loadMaterial(
                {
                    materialId: inputs[0].payload.materialId,
                    language: inputs[1],
                    fallbackLanguages: inputs[2],
                    page: inputs[3] || TamApePageType.APPROVAL_GR_DELETION
                },
                this.service.action_doLoadMaterialGRDeleteSuccess(),
                this.service.action_doLoadMaterialGRDeleteFailure());

        })
    ), ReduxUtils.noDispatch());
    onAttachmentInfoGet =
        createEffect(() => this.actions$.pipe(
            ofType(MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_INFO_GET),
            withLatestFrom(this.store.select(getOpenTextEnabled)),
            tap((inputs: [MaterialsModalAction_AttachmentsInfo_Get, boolean]) => {
                this.dao.onAttachmentInfoGet(inputs[0]?.payload, inputs[1],
                    this.service.action_doAttachmentInfoGetSuccess(),
                    this.service.action_doAttachmentInfoGetFailure()
                );
            })
        ), ReduxUtils.noDispatch());
    onRequestGrDelete =
        createEffect(() => this.actions$.pipe(
            ofType(MATERIALS_MODAL_ACTION_NAMES.REQUEST_GR_DELETE),
            tap((input: MaterialsModalAction_RequestGR_Delete) => {
                this.dao.onGRDeleteRequest(
                    input.payload,
                    this.service.action_doGRDeleteRequestSuccess(),
                    this.service.action_doGRDeleteRequestFailure()
                );
            })
        ), ReduxUtils.noDispatch());
    onApproveGRDelete =
        createEffect(() => this.actions$.pipe(
            ofType(MATERIALS_MODAL_ACTION_NAMES.APPROVE_GR_DELETE),
            withLatestFrom(
                this.store.select(getCurrentLanguage),
                this.store.select(MaterialsModalSelectors.getProcessId)
            ),
            tap((inputs: [MaterialsModalAction_ApproveGR_Delete, string, string]) => {
                const payload: ApproveGRDeleteRequest = {
                    processId: inputs[2],
                    approvalNote: inputs[0].payload,
                    page: TamApePageType.APPROVAL_GR_DELETION,
                    language: inputs[1]
                }
                this.dao.onApproveGRDelete(
                    payload,
                    this.service.action_doApproveGRDeleteSuccess(),
                    this.service.action_doApproveGRDeleteFailure()
                );
            })
        ), ReduxUtils.noDispatch());
    onRejectGRDelete =
        createEffect(() => this.actions$.pipe(
            ofType(MATERIALS_MODAL_ACTION_NAMES.REJECT_GR_DELETE),
            withLatestFrom(
                this.store.select(getCurrentLanguage),
                this.store.select(MaterialsModalSelectors.getProcessId)
            ),
            tap((inputs: [MaterialsModalAction_RejectGR_Delete, string, string]) => {
                const payload: RejectGRRequest = {
                    processId: inputs[2],
                    rejectionNote: inputs[0].payload,
                    moreInformationNeeded: false,
                    language: inputs[1]
                }
                this.dao.onRejectGRDelete(
                    payload,
                    this.service.action_doRejectGRDeleteSuccess(),
                    this.service.action_doRejectGRDeleteFailure()
                );
            })
        ), ReduxUtils.noDispatch());
    onSaveMaterialEdit =
        createEffect(() => this.actions$.pipe(
            ofType(MATERIALS_MODAL_ACTION_NAMES.SAVE_MATERIAL_EDIT),
            withLatestFrom(this.store.select(getCurrentLanguage),
                this.store.select(getFallbackLanguages),
                this.store.select(MaterialsModalSelectors.getMaterial),
                this.store.select(MaterialsModalSelectors.getImageUUID),
                this.store.select(MaterialsModalSelectors.getAttachmentsUUID),
                this.store.select(MaterialsModalSelectors.getAlternativeUomList),
                this.store.select(MaterialsModalSelectors.getSelectedCategories),
                this.store.select(MaterialsModalSelectors.getPage),
                this.store.select(MaterialsModalSelectors.getAttachmenstInstancesUpdate)),
            tap((inputs: [
                MaterialsModalAction_SaveMaterial_Edit, string, string[],
                SmartCreationMaterialDetail, string, string[], AlternativeUnitsOfMeasure[],
                SmartCreationSelectedCategories, string, Array<AttachmentInstanceUpdateRequest>
            ]) => {
                const materialDetails = inputs[3];
                const categoriesSelected = this.getCategories(materialDetails.categoriesSheet, inputs[7]);

                const request = {
                    ...inputs[0].payload,
                    materialFormControlList: flattenMaterialFormControlList(materialDetails),
                    plantToAddFormControlList: flattenPlantFormControlList(materialDetails, null),
                    categoriesSelected,
                    language: inputs[1],
                    domain: materialDetails.additionalMaterialInformation?.domain,
                    client: materialDetails.client,
                    imageId: inputs[4],
                    attachmentsId: inputs[5],
                    page: inputs[8],
                    alternativeUnitsOfMeasure: this.mapAlternativeUnitsOfMeasurement(inputs[6]),
                    attachmentsInstancesUpdate: inputs[9]
                } as MaterialDetailsEditSaveRequest;
                request.materialId = request.materialId['materialId'] ?? request.materialId; // TODO: REMOVE THIS!!!
                this.dao.onSaveMaterialEdit(request,
                    this.service.action_doEditMaterialSuccess(),
                    this.service.action_doEditMaterialFailure()
                );
            })
        ), ReduxUtils.noDispatch());
    onReloadDocument = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.RELOAD_DOCUMENT),
        withLatestFrom(this.store.select(MaterialsModalSelectors.getAttributeChanged)),
        tap((inputs: [MaterialsModalAction_Reload_Initial_Data, SmartFieldConfiguration]) => {
            this.store.dispatch(MATERIALS_MODAL_ACTIONS.RELOAD_INITIAL_DATA(inputs[0].payload));
        })
    ), ReduxUtils.noDispatch());
    onReloadInitialData = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.RELOAD_INITIAL_DATA),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getMaterial),
            this.store.select(MaterialsModalSelectors.getAttributeChanged),
            this.store.select(MaterialsModalSelectors.getAlternativeUomList),
            this.store.select(MaterialsModalSelectors.getMaterialId),
            this.store.select(MaterialsModalSelectors.getPage),
            this.store.select(MaterialsModalSelectors.getDomain)),
        tap((inputs: [
            MaterialsModalAction_Reload_Initial_Data, string, string[],
            SmartCreationMaterialDetail, SmartFieldConfiguration, AlternativeUnitsOfMeasure[], string, string, string
        ]) => {
            const auom: AlternativeUnitsOfMeasure[] = this.mapAlternativeUnitsOfMeasurement(inputs[5]);
            let smartCreationMaterialDetail = inputs[3];
            const materialDetailsRequests = flattenMaterialFormControlList(smartCreationMaterialDetail);
            const plantToAddFormControlList = flattenPlantFormControlList(smartCreationMaterialDetail, null);
            const categoriesSelected = this.flattenCategories(smartCreationMaterialDetail.categoriesSheet);
            const materialId = inputs[6]['materialId'] ?? inputs[6]; // TODO: REMOVE THIS!!!
            const doReloadInitialDataRequest: MaterialDetailsReloadRequest = {
                materialId: materialId,
                client: smartCreationMaterialDetail.client,
                language: inputs[1],
                categoriesSelected,
                materialFormControlList: materialDetailsRequests,
                plantToAddFormControlList: plantToAddFormControlList,
                attributeChanged: inputs[4],
                categoryChanged: inputs[0].payload?.category,
                alternativeUnitsOfMeasure: auom,
                fallbackLanguages: inputs[2],
                page: inputs[7],
                grMaterialId: inputs[3]?.additionalMaterialInformation?.goldenRecord,
                domain: inputs[3]?.additionalMaterialInformation?.domain
            };

            this.dao.onReloadInitialData(
                doReloadInitialDataRequest,
                this.service.action_doReloadInitialDataSuccess(),
                this.service.action_doReloadInitialDataFailure());
        })
    ), ReduxUtils.noDispatch());

    onCalculateCompleteness = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.CALCULATE_COMPLETENESS),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getMaterial),
            this.store.select(MaterialsModalSelectors.getAttributeChanged),
            this.store.select(MaterialsModalSelectors.getAlternativeUomList),
            this.store.select(MaterialsModalSelectors.getMaterialId),
            this.store.select(MaterialsModalSelectors.getPage)),
        tap((inputs: [
            MaterialsModalAction_Reload_Initial_Data, string, string[],
            SmartCreationMaterialDetail, SmartFieldConfiguration, AlternativeUnitsOfMeasure[], string, string
        ]) => {
            const auom: AlternativeUnitsOfMeasure[] = this.mapAlternativeUnitsOfMeasurement(inputs[5]);
            const smartCreationMaterialDetail = inputs[3];
            const materialDetailsRequests = flattenMaterialFormControlList(smartCreationMaterialDetail);
            const plantToAddFormControlList = flattenPlantFormControlList(smartCreationMaterialDetail, null);
            const categoriesSelected = this.flattenCategories(smartCreationMaterialDetail.categoriesSheet);
            console.log(inputs[6])
            const doReloadInitialDataRequest: MaterialDetailsReloadRequest = {
                materialId: inputs[6],
                client: smartCreationMaterialDetail.client,
                language: inputs[1],
                categoriesSelected,
                materialFormControlList: materialDetailsRequests,
                plantToAddFormControlList: plantToAddFormControlList,
                attributeChanged: inputs[4],
                categoryChanged: inputs[0].payload?.category,
                alternativeUnitsOfMeasure: auom,
                fallbackLanguages: inputs[2],
                page: inputs[7]
            };

            const doReloadInitialDataRequestLocal: MaterialDetailsReloadLocalRequest = {
                materialId: inputs[6],
                categoriesSheet: smartCreationMaterialDetail.categoriesSheet,
                materialSheets: smartCreationMaterialDetail.materialSheets,
                alternativeUomList: auom
            };
            this.dao.onCompleteness(doReloadInitialDataRequest,
                (response) => {
                    doReloadInitialDataRequestLocal.completeness = response.data.completeness;
                    this.store.dispatch(MATERIALS_MODAL_ACTIONS.RELOAD_LOCAL_INITIAL_DATA_SUCCESS(doReloadInitialDataRequestLocal));
                },
                () => this.store.dispatch(MATERIALS_MODAL_ACTIONS.COMPLETENESS_FAILURE()));
        })
    ), ReduxUtils.noDispatch());
    onValidateInitialData = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.VALIDATE_MATERIAL_DATA),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getMaterial),
            this.store.select(MaterialsModalSelectors.getPage)),
        tap(([action, currLang, fallbackLangs, materialDetail, page]: [any, string, string[], any, string]) => {

            let materialSheet = materialDetail;
            const materialDetailsRequests = flattenMaterialFormControlList(materialSheet);
            const plantToAddFormControlList = flattenPlantFormControlList(materialSheet, null);
            const categoriesSelected = this.flattenCategories(materialSheet.categoriesSheet);

            const validateMaterialDataRequest = {
                description: materialSheet.description,
                domain: materialSheet.domain,
                client: materialSheet.client,
                language: currLang,
                fallbackLanguages: fallbackLangs,
                categoriesSelected,
                materialFormControlList: materialDetailsRequests,
                plantToAddFormControlList: plantToAddFormControlList,
                page: page
            };

            const isGoldenRecord = materialSheet.additionalMaterialInformation?.domain.includes('GR');
            this.dao.onValidateMaterialData(
                validateMaterialDataRequest,
                this.service.action_doValidateMaterialDataSuccess(),
                this.service.action_doValidateMaterialDataFailure());
        })
    ), ReduxUtils.noDispatch());

    genericFailure = createEffect(() => this.actions$.pipe(
        filter(type => `${type?.type}`.includes(`[${TAM_MATERIALS_MODAL_FEATURE_NAME}]`) && `${type?.type}`.includes('[FAILURE]')),
        tap((a$: MaterialsModalActionTypes) => {
            if (a$?.payload?.messages?.length > 0) {
                (a$.payload as CommonResponseWrapper<any>).messages.forEach(message => this.service.showToaster(message));
            } else {
                this.service.showToaster(HttpClientUtils.getErrorMessage());
            }
        })
    ), ReduxUtils.noDispatch());
    onMaterialImageUpload =
        createEffect(() => this.actions$.pipe(
            ofType(MATERIALS_MODAL_ACTION_NAMES.MATERIAL_IMAGE_UPLOAD),
            withLatestFrom(this.store.select(getCurrentUserId),
                this.store.select(getOpenTextEnabled),
                this.store.select(LayoutSelectors.getAttachmentConfig)
            ),
            tap(([action, userId, openTextEnabled, attachmentConfig]: [MaterialsModalAction_Material_Image_Upload, number, boolean, AttachmentConfig]) => {
                this.materialsEditorDao.onImageUpload(action?.payload, userId, openTextEnabled,
                    this.service.action_doImageUploadSuccess(),
                    this.service.action_doImageUploadFailure(attachmentConfig?.acceptedImagesExtensions)
                );
            })
        ), ReduxUtils.noDispatch());
    onMaterialImageDownload =
        createEffect(() => this.actions$.pipe(
            ofType(MATERIALS_MODAL_ACTION_NAMES.MATERIAL_IMAGE_DOWNLOAD),
            withLatestFrom(this.store.select(MaterialsModalSelectors.getImageUUID),
                this.store.select(getOpenTextEnabled)),
            tap((inputs: [MaterialsModalAction_Material_Image_Download, string, boolean]) => {
                this.materialsEditorDao.downloadFile(inputs[1], inputs[2],
                    this.service.downloadFile(),
                    this.service.action_doImageUploadFailure()
                );
            })
        ), ReduxUtils.noDispatch());
    onAttachmentUpload =
        createEffect(() => this.actions$.pipe(
            ofType(MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_UPLOAD),
          withLatestFrom(
              this.store.select(getCurrentUserId),
              this.store.select(getOpenTextEnabled),
              this.store.select(LayoutSelectors.getAttachmentConfig)
          ),
          tap(([action, userId, openTextEnabled, attachmentConfig]: [MaterialsModalAction_Material_Image_Upload, number, boolean, AttachmentConfig]) => {
            const allowedExtensions = attachmentConfig?.acceptedAttachmentsExtensions || [];
            const allowedFormatted = allowedExtensions.map(ext => ext.toUpperCase());

            this.materialsEditorDao.onImageUpload(action?.payload, userId, openTextEnabled,
                    this.service.action_doAttachmentUploadSuccess(),
                this.service.action_doAttachmentUploadFailure(allowedFormatted)
                );
            })
        ), ReduxUtils.noDispatch());

    onAttachmentInstanceUpload =
        createEffect(() => this.actions$.pipe(
          ofType(MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_INSTANCE_UPLOAD),
          withLatestFrom(
              this.store.select(getCurrentUserId),
              this.store.select(getOpenTextEnabled),
              this.store.select(LayoutSelectors.getAttachmentConfig)
          ),
          tap(([action, userId, openTextEnabled, attachmentConfig]: [MaterialsModalAction_Attachments_Instance_Upload, number, boolean, AttachmentConfig]) => {
            const allowedExtensions = attachmentConfig?.acceptedAttachmentsExtensions || [];
            const allowedFormatted = allowedExtensions.map(ext => ext.toUpperCase());

            this.materialsEditorDao.onImageUpload(action?.payload.file, userId, openTextEnabled,
                this.service.action_doAttachmentInstanceUploadSuccess(),
                this.service.action_doAttachmentInstanceUploadFailure(allowedFormatted)
            );
            })
        ), ReduxUtils.noDispatch());

    onAttachmentDownload =
        createEffect(() => this.actions$.pipe(
            ofType(MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_DOWNLOAD),
            withLatestFrom(
                this.store.select(getOpenTextEnabled)),
            tap((inputs: [MaterialsModalAction_Attachments_Download, boolean]) => {
                this.materialsEditorDao.downloadFile(inputs[0]?.payload, inputs[1],
                    this.service.downloadFile(),
                    this.service.action_doAttachmentUploadFailure()
                );
            })
        ), ReduxUtils.noDispatch());
    onSelectCategory = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.SELECT_CATEGORY),
        withLatestFrom(this.store.select(MaterialsModalSelectors.getMaterial)),
        tap((inputs: [MaterialsModalAction_Select_Category, SmartCreationMaterialDetail]) => {
            if (inputs[1]) {
                const firstKey = Object.keys(inputs[0]?.payload)[0];
                this.service.action_doReloadInitialData(null, inputs[0]?.payload[firstKey]);
            }
        })
    ), ReduxUtils.noDispatch());
    onTreeCategories = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.TREE_CATEGORIES),
        withLatestFrom(this.store.select(getCurrentLanguage), this.store.select(MaterialsModalSelectors.getSetup)),
        tap((inputs: [MaterialsModalAction_Tree_Categories, string, EditApprovalSetupResponse]) => {
            let modalTreeCategoriesRequest = this.service.getModalTreeCategoriesRequest(inputs[0]?.payload, inputs[1]);
            this.dao.treeCategories(
                modalTreeCategoriesRequest,
                this.service.action_doTreeCategoriesSuccess(),
                this.service.action_doTreeCategoriesFailure());
        })
    ), ReduxUtils.noDispatch());


    onTreeCategoriesChildren = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.TREE_CATEGORIES_LOADCHILDREN),
        withLatestFrom(this.store.select(getCurrentLanguage)),
        tap((inputs: [SmartCreationAction_Tree_Categories_Loadchildren, string]) => {
            const a$: SmartCreationAction_Tree_Categories_Loadchildren = inputs[0];
            const req = this.service.getSmartCreationTreeCategoriesRequest(
                a$?.payload,
                a$?.payload?.node || a$?.payload?.taxonomy,
                inputs[1],
                true);

            this.dao.loadCategoryTreeNodeChildren(req,
                this.service.action_doTreeCategoriesChildrenSuccess(a$.payload.node, a$.payload?.parents),
                this.service.action_doTreeCategoriesChildrenFailure()
            )
        })
    ), ReduxUtils.noDispatch());

    openLinkInstancesGRConfirm = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.LINK_INSTANCES_CONFIRM_OPEN),
        tap((action: MaterialsModalAction_LinkInstancesConfirmOpen) => {
            const modalCfg: DynamicDialogConfig = {
                header: this.translate.instant('smartCreation.modalDetails.linkUnlink.confirm.title'),
                data: {request: action.payload}
            };

            this.dialogService.open(SmartCreationLinkUnlinkConfirm, modalCfg);
        })
    ), {dispatch: false});

    onOpenGrEditApproval = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.OPEN_GR_EDIT_APPROVAL),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages)
        ),
        tap((inputs: [MaterialsModalAction_OpenGrEditApproval, string, string[]]) => {
            this.dao.getGrEditApprovalData(
                inputs[0].payload.materialId, inputs[0].payload.processId, inputs[1],
                this.service.action_doLoadMaterialGRSuccess(),
                this.service.action_doLoadMaterialGRFailure());
            this.service.openMaterialModal(null, ViewModeEnum.GR_ENRICHMENT);

        })
    ), ReduxUtils.noDispatch());

    onConfirmGrEnrichment = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.CONFIRM_GR_ENRICHMENT),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getCurrentUserId),
            this.store.select(getFallbackLanguages),
            this.store.select(MaterialsModalSelectors.getMaterial),
            this.store.select(MaterialsModalSelectors.getImageUUID),
            this.store.select(MaterialsModalSelectors.getAttachmentsUUID),
            this.store.select(MaterialsModalSelectors.getAlternativeUomList),
            this.store.select(MaterialsModalSelectors.getSelectedCategories),
            this.store.select(MaterialsModalSelectors.getProcessId),
            this.store.select(MaterialsModalSelectors.getMaterialId),
            this.store.select(MaterialsModalSelectors.getPage)),
        tap((inputs: [
            MaterialsModalAction_Confirm_Gr_Enrichment,
            string,
            number,
            string[],
            SmartCreationMaterialDetail,
            string,
            string[],
            AlternativeUnitsOfMeasure[],
            SmartCreationSelectedCategories,
            string,
            string,
            string
        ]) => {

            const request = this.getRequest(inputs);

            this.dao.saveGrEnrichment(inputs[2], request,
                this.service.action_doConfirmUpdateEditSuccess(),
                this.service.action_doConfirmUpdateEditFailure());
        })
    ), ReduxUtils.noDispatch());

    onRejectGrEnrichment = createEffect(() => this.actions$.pipe(
        ofType(MATERIALS_MODAL_ACTION_NAMES.REJECT_GR_ENRICHMENT),
        withLatestFrom(this.store.select(MaterialsModalSelectors.getProcessId)),
        tap((inputs: [MaterialsModalAction_Reject_Gr_Enrichment, string]) => {
            const request = inputs[0].payload;
            request.processId = inputs[1];
            this.dao.onRejectGrEnrichment(request,
                this.service.action_doRejectDraftSuccess(),
                this.service.action_doRejectDraftFailure());
        })
    ), ReduxUtils.noDispatch());

  onGetInstancesWithRelationshipsForGR = createEffect(() => this.actions$.pipe(
    ofType(MATERIALS_MODAL_ACTION_NAMES.INSTANCES_WITH_RELATIONSHIPS),
    withLatestFrom(this.store.select(getCurrentLanguage),
      this.store.select(getFallbackLanguages)),
    tap((inputs: [MaterialsModalAction_Instances_With_Relationships, string, string[]]) => {
      const request = this.service.getInstancesWithRelationshipsRequest(inputs[0].payload, inputs[1], inputs[2]);
      this.dao.getInstancesWithRelationships(request,
        this.service.action_doGetInstancesWithRelationshipsForGRSuccess(),
        this.service.action_doGetInstancesWithRelationshipsForGRFailure());
    })
  ), ReduxUtils.noDispatch());

  onGetRetrieveInstancesAttachmentsAndThumbnail = createEffect(() => this.actions$.pipe(
    ofType(MATERIALS_MODAL_ACTION_NAMES.INSTANCES_ATTACHMENTS_AND_IMAGE),
    withLatestFrom(this.store.select(getOpenTextEnabled)),
    tap((inputs: [MaterialsModalAction_Instances_Attachments_And_Image, boolean]) => {
      this.dao.getInstancesAttachmentsAndThumbnail({goldenRecordId: inputs[0].payload, openText: inputs[1]},
        this.service.action_doGetInstancesAttachmentsAndImageSuccess(),
        this.service.action_doGetInstancesAttachmentsAndImageFailure());
    })
  ), ReduxUtils.noDispatch());

  onGetRetrieveInstancesThumbnail = createEffect(() => this.actions$.pipe(
    ofType(MATERIALS_MODAL_ACTION_NAMES.INSTANCES_IMAGE),
    withLatestFrom(this.store.select(getOpenTextEnabled)),
    tap((inputs: [MaterialsModalAction_Instances_Image, boolean]) => {
      this.dao.getInstancesThumbnails({instances: inputs[0].payload, openText: inputs[1]},
        this.service.action_doGetInstancesAttachmentsAndImageSuccess(),
        this.service.action_doGetInstancesAttachmentsAndImageFailure());
    })
  ), ReduxUtils.noDispatch());

    /* ******************* PRIVATE ******************* */

    constructor(private actions$: Actions<MaterialsModalActionTypes>,
                private service: MaterialsModalService,
                private dao: MaterialsModalDao,
                private store: Store<MaterialsModalState>,
                private materialsEditorDao: MaterialsEditorDao,
                private dialogService: DialogService,
                private translate: TranslateService) {
    }

    flattenCategories(suggestedCategories: SmartCreationSuggestedCategories): SmartCreationSelectedCategories {
        const normalizedCategories: SmartCreationSelectedCategories = {};
        Object.keys(suggestedCategories).forEach(key => {
            const categoryArray = suggestedCategories[key];
            if (categoryArray && categoryArray.length > 0) {
                const {children, ...normalizedCategory} = categoryArray[0] as any;
                normalizedCategories[key] = normalizedCategory;
            }
        });
        return normalizedCategories;
    }

    private getRequest(inputs: [
        any, string, number, string[], SmartCreationMaterialDetail, string, string[],
        AlternativeUnitsOfMeasure[], SmartCreationSelectedCategories, string, string, string, string?
    ]) {
        const materialDetails = inputs[4];

        const categoriesSelected = this.getCategories(materialDetails.categoriesSheet, inputs[8]);

        const request = {
            description: materialDetails.additionalMaterialInformation?.description,
            materialFormControlList: flattenMaterialFormControlList(materialDetails),
            plantToAddFormControlList: flattenPlantFormControlList(materialDetails, null),
            categoriesSelected,
            language: inputs[1],
            fallbackLanguages: inputs[3],
            domain: materialDetails.additionalMaterialInformation?.domain,
            client: materialDetails.client,
            imageId: inputs[5],
            attachmentsId: inputs[6],
            alternativeUnitsOfMeasure: this.mapAlternativeUnitsOfMeasurement(inputs[7]),
            // MaterialsModalAction_Approve_Draft c'ha la nota dentro al payload secca
            // il SaveMaterialDetailPayload ce l'ha nel sottocampo note
            note: inputs[0].payload?.note || inputs[0].payload || ''
        } as SaveApprovalRequest;

        request.processId = inputs[9];
        request.materialId = inputs[10];
        request.page = inputs[11];
        request.imageFromInstance = inputs[12];
        return request;
    }

    private getCategories(categoriesSheetFromMaterial: any, selectedCategories: SmartCreationSelectedCategories) {
        const categoriesSheet = this.flattenCategories(categoriesSheetFromMaterial);

        if (selectedCategories) {
            Object.keys(selectedCategories).forEach((key) => {
                if (categoriesSheet.hasOwnProperty(key)) {
                    categoriesSheet[key] = {
                        ...categoriesSheet[key],
                        ...selectedCategories[key]
                    };
                }
            });
        }
        return categoriesSheet;
    }

    private mapAlternativeUnitsOfMeasurement(uomsInput: any[]) {
        if (uomsInput?.every(i => (i?.hasOwnProperty(TAm4StandardFieldNames.ALTERNATIVE_UOM_ROW))
            && i?.hasOwnProperty('x') && i?.hasOwnProperty('y'))) {
            return uomsInput?.map(i => ({
                alternativeUnitOfMeasurement: i[TAm4StandardFieldNames.ALTERNATIVE_UOM_ROW],
                numerator: i.x,
                denominator: i.y,
            })) ?? [];
        } else {
            return uomsInput;
        }
    }

    private openLinkedUnlinkedInstancesDialog(params: any, action: string) {
        const modalCfg: DynamicDialogConfig = {
            header: action === 'LINK' ? this.translate.instant('smartCreation.confirmedLink.dialog.title') : this.translate.instant('smartCreation.confirmedUnlink.dialog.title'),
            data: {process: {processCode: params}},
            width: '500px',
            height: '300px'
        };
        this.dialogService.open(SmartCreationCreatedMaterialDialog, modalCfg);
    }
}
