import { Component, Input, Output, EventEmitter } from '@angular/core';
import { BadgeType } from '../../search/endpoint/search.model';
import { Store } from '@ngrx/store';
import {OpenHistoryInProgressPopup} from '../../layout/store/actions/popup.actions';

@Component({
  selector: 'dynamic-action-changelog-badge',
  template: `
    @if (shouldShowBadge()) {
      <app-card-badge
        [badgeType]="badgeType.PROCESSES"
        [material]="{uuid: getMaterialUuid()}"
        *ngIf="shouldShowBadge()"
        [value]="getProcessCount()"
        (click)="onBadgeClick()"
      ></app-card-badge>
    }
  `
})
export class DynamicActionChangelogBadgeComponent {

  @Input() actionData: any = null;
  @Input() rowData: any;
  @Input() rowIndex: 0;
  @Output() badgeClick = new EventEmitter<any>();

  badgeType = BadgeType;

  constructor(private store: Store) {}

  onBadgeClick(): void {
    const dataToUse = this.actionData;

    if (dataToUse) {
      this.badgeClick.emit(dataToUse);
      const materialId = dataToUse.materialId || dataToUse.uuid;
      if (materialId) {
        this.store.dispatch(new OpenHistoryInProgressPopup({materialId}));
      }
    }
  }

  getMaterialUuid(): string | undefined {
    const dataToUse = this.actionData;
    return dataToUse?.materialId || dataToUse?.uuid;
  }

  getProcessCount(): number {
    const dataToUse = this.actionData;
    return dataToUse?.inProgressProcessCount || 0;
  }

  shouldShowBadge(): boolean {
    return this.getProcessCount() > 0;
  }
}
