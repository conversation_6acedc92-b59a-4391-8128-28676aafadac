import { CommonModule, DatePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { Tam4TranslationModule } from '@creactives/tam4-translation-core';

import { PipesModule, TamCommonPipesModule } from 'src/app/pipes';

import { AccordionModule } from 'primeng/accordion';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CarouselModule } from 'primeng/carousel';
import { DropdownModule } from 'primeng/dropdown';
import { FieldsetModule } from 'primeng/fieldset';
import { FileUploadModule } from 'primeng/fileupload';
import { ImageModule } from 'primeng/image';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ListboxModule } from 'primeng/listbox';
import { MessageModule } from 'primeng/message';
import { MessagesModule } from 'primeng/messages';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { PanelModule } from 'primeng/panel';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';

import { MaterialPlantDataComponent } from 'src/app/modules/materials-editor/components/material-plant-data.component';
import { ComponentsModule } from '../components/components.module';
import { CategoriesSelectionComponent } from './components/categories-selection.component';
import { ClientInputComponent } from './components/client.input.component';
import { CompletenessComponent } from './components/completeness.component';
import { DescriptionInputComponent } from './components/description-input.component';
import { DomainInputComponent } from './components/domain-input.component';
import { MaterialAttachmentsComponent } from './components/material-attachments.component';
import { MaterialDetailsComponent } from './components/material-details-component';
import { MaterialImageComponent } from './components/material-image.component';
import { MaterialSummaryComponent } from './components/material-summary.component';

import { CdkCopyToClipboard } from "@angular/cdk/clipboard";
import { ChipModule } from 'primeng/chip';
import { ChipsModule } from 'primeng/chips';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { InputSwitchModule } from 'primeng/inputswitch';
import { KeyFilterModule } from "primeng/keyfilter";
import { MultiSelectModule } from 'primeng/multiselect';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { SelectButtonModule } from 'primeng/selectbutton';
import { SidebarModule } from "primeng/sidebar";
import { TooltipModule } from 'primeng/tooltip';
import { TreeModule } from 'primeng/tree';
import { TamFormInputComponent } from 'src/app/components/tam-form-input.component';
import { TamReadonlyValuesComponent } from 'src/app/components/tam-readonly-values.component';
import { DynamicBooleanComponent } from 'src/app/modules/materials-editor/dynamic-components/dynamic-boolean.component';
import { DynamicErrorMessageComponent } from 'src/app/modules/materials-editor/dynamic-components/dynamic-error-message.component';
import { DynamicValueTranslateComponent } from 'src/app/modules/materials-editor/dynamic-components/dynamic-value-translate.component';
import { TamDefaultDialogTemplateComponent } from '../../components/tam-default-dialog-template.component';
import { TamFieldGroupModule } from '../../components/tam-field-group.component';
import { TamFullHeightPanelComponent } from "../../components/tam-full-height-panel.component";
import { TamLoadingTruckModule } from "../../components/tam-loading-truck.component";
import { TamMessageInterceptor } from "../../directives/tam-message-interceptor.directive";
import { MaterialCodeModule } from '../components/material-code/material-code.module';
import { WorkflowCommentsSidebarWrapperComponent } from "../layout/overlays/workflow-comments-sidebar.wrapper.component";
import { WorkflowCommentsComponent } from "../layout/overlays/workflow-comments.component";
import { SearchModule } from '../search/search.module';
import { SmartCreationLinkUnlinkConfirm } from '../smart-creation/components/smart-creation-link-unlink-confirm';
import { SmartCreationPlantDialog } from '../smart-creation/components/smart-creation-plant-modal.dialog';
import { SmartCreationSearchLinkInstancesDialog } from '../smart-creation/components/smart-creation-search-link-instances-modal.dialog';
import { SmartCreationSearchLinkInstancesResult } from '../smart-creation/components/smart-creation-search-link-instances-results';
import { MaterialUpdateService } from '../smart-creation/service/material-update.service';
import { CategoriesTreeComponent } from './components/categories-tree.component';
import { ChangesJsonViewerComponent } from './components/json-viewer.component';
import { MaterialDetailsDescriptionEditorComponent } from './components/material-details-description-editor.component';
import { MaterialDetailsAccordionHeaderComponent } from './components/material-details.accordion-header.component';
import { MaterialGoldenRecordComponent } from './components/material-golden-record';
import { MaterialImagesCarouselComponent } from "./components/material-image-carousel.component";
import { MaterialInfoComponent } from './components/material-info.component';
import { MaterialPlantDataGrComponent } from './components/material-plant-data-gr-component';
import { SimilarMaterialsComponent } from './components/similar-materials-component';
import { SimilarMaterialsFiltersComponent } from './components/similar-materials.filters.component';
import { SimilarMaterialsHeaderComponent } from './components/similar-materials.header.component';
import { SimilarMaterialsRowComponent } from './components/similar-materials.row.component';
import { StepNumberComponent } from './components/step-number.component';
import { StickyFooterEditorComponent } from './components/sticky-footer-editor.component';
import { StickyFooterModalComponent } from './components/sticky-footer-modal.component';
import { StickyHeaderEditorComponent } from './components/sticky-header-editor.component';
import { MaterialEditorDialog } from './dialog/material-editor-dialog';
import {
  DynamicAutocompleteComponent,
  DynamicBaseInputComponent,
  DynamicDateComponent,
  DynamicDescriptionComponent,
  DynamicDropdownComponent,
  DynamicGroupInputTableComponent,
  DynamicInputComponent,
  DynamicInputNumericComponent,
  DynamicInputTableComponent,
  DynamicTableUnitOfMeasureComponent,
  DynamicTextareaComponent,
  DynamicWarningListComponent
} from './dynamic-components';
import { DynamicChangesComponent } from './dynamic-components/dynamic-changes.component';
import { DynamicContainerTemplateComponent } from './dynamic-components/dynamic-container-template.component';
import { DynamicInputSuggestionAttributeComponent } from './dynamic-components/dynamic-input-suggestion-attribute';
import { DynamicLinkUnlinkComponent } from './dynamic-components/dynamic-link-unlink.component';
import { DynamicTableGrInstanceComponent } from './dynamic-components/dynamic-table-gr-instance.component';
import { DynamicTableRelationsComponent } from './dynamic-components/dynamic-table-relations.component';
import { DynamicActionChangelogBadgeComponent } from './dynamic-components/dynamic-action-changelog-badge.component';
import { MaterialGRAttachmentsComponent } from './components/material-gr-attachments.component';
import {Ripple} from 'primeng/ripple';

const COMPONENTS = [
  MaterialDetailsComponent,
  MaterialPlantDataComponent,
  DomainInputComponent,
  DescriptionInputComponent,
  CategoriesSelectionComponent,
  MaterialSummaryComponent,
  MaterialImageComponent,
  CompletenessComponent,
  MaterialAttachmentsComponent,
  MaterialGRAttachmentsComponent,
  ClientInputComponent,
  SimilarMaterialsComponent,
  SimilarMaterialsFiltersComponent,
  SimilarMaterialsHeaderComponent,
  SimilarMaterialsRowComponent,
  MaterialEditorDialog,
  DynamicTableGrInstanceComponent,
  DynamicTableRelationsComponent,
  ChangesJsonViewerComponent,
  CategoriesTreeComponent,
  StickyHeaderEditorComponent,
  MaterialInfoComponent,
  StickyFooterEditorComponent,
  StickyFooterModalComponent,
  StepNumberComponent,
  MaterialDetailsAccordionHeaderComponent,
  MaterialDetailsDescriptionEditorComponent,
  MaterialGoldenRecordComponent,
  SmartCreationPlantDialog,
  MaterialPlantDataGrComponent,
  SmartCreationSearchLinkInstancesDialog,
  SmartCreationSearchLinkInstancesResult,
  SmartCreationLinkUnlinkConfirm,
  MaterialImagesCarouselComponent
];

const DYNAMIC_COMPONENTS = [
  DynamicBaseInputComponent,
  DynamicInputComponent,
  DynamicInputNumericComponent,
  DynamicDropdownComponent,
  DynamicTextareaComponent,
  DynamicDescriptionComponent,
  DynamicAutocompleteComponent,
  DynamicDateComponent,
  DynamicInputTableComponent,
  DynamicGroupInputTableComponent,
  DynamicWarningListComponent,
  DynamicTableUnitOfMeasureComponent,
  DynamicInputSuggestionAttributeComponent,
  DynamicContainerTemplateComponent,
  DynamicChangesComponent,
  DynamicLinkUnlinkComponent,
  DynamicBooleanComponent,
  DynamicActionChangelogBadgeComponent
];

const PRIMENG_MODULES = [
  AccordionModule,
  MessageModule,
  FieldsetModule,
  OverlayPanelModule,
  ButtonModule,
  RadioButtonModule,
  InputTextModule,
  PanelModule,
  DropdownModule,
  InputNumberModule,
  InputTextareaModule,
  CalendarModule,
  AutoCompleteModule,
  ListboxModule,
  MessagesModule,
  TableModule,
  TagModule,
  CardModule,
  ToastModule,
  FileUploadModule,
  BadgeModule,
  ImageModule,
  InputSwitchModule,
  TreeModule,
  CarouselModule
];

@NgModule({
  imports: [
    CommonModule,
    PipesModule,
    TranslateModule,
    Tam4TranslationModule,
    FormsModule,
    ReactiveFormsModule,
    ComponentsModule,
    ...PRIMENG_MODULES,
    ChipModule,
    MaterialCodeModule,
    MultiSelectModule,
    ChipsModule,
    ScrollPanelModule,
    TamCommonPipesModule,
    TamFieldGroupModule,
    TamFormInputComponent,
    DynamicErrorMessageComponent,
    TamReadonlyValuesComponent,
    DynamicValueTranslateComponent,
    ProgressSpinnerModule,
    ProgressSpinnerModule,
    TooltipModule,
    KeyFilterModule,
    SelectButtonModule,
    CdkCopyToClipboard,
    TamMessageInterceptor,
    TamFullHeightPanelComponent,
    TamLoadingTruckModule,
    ConfirmDialogModule,
    TamDefaultDialogTemplateComponent,
    SidebarModule,
    WorkflowCommentsComponent,
    WorkflowCommentsSidebarWrapperComponent,
    SearchModule,
    Ripple
  ],
  exports: [
    MaterialDetailsComponent,
    MaterialPlantDataComponent,
    DomainInputComponent,
    DescriptionInputComponent,
    CategoriesSelectionComponent,
    MaterialSummaryComponent,
    MaterialImageComponent,
    CompletenessComponent,
    MaterialAttachmentsComponent,
    MaterialGRAttachmentsComponent,
    ClientInputComponent,
    SimilarMaterialsComponent,
    MaterialEditorDialog,
    CategoriesTreeComponent,
    StickyHeaderEditorComponent,
    MaterialInfoComponent,
    StickyFooterEditorComponent,
    StickyFooterModalComponent,
    StepNumberComponent,
    DynamicContainerTemplateComponent,
    MaterialGoldenRecordComponent,
    SmartCreationPlantDialog,
    MaterialPlantDataGrComponent,
    SmartCreationSearchLinkInstancesDialog,
    SmartCreationSearchLinkInstancesResult
  ],
  declarations: [
    ...COMPONENTS,
    ...DYNAMIC_COMPONENTS,
  ],
  providers: [MaterialUpdateService, DatePipe],
})
export class MaterialsEditorModule {
}
