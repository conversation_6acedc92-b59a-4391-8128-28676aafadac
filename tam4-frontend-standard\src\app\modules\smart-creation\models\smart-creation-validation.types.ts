import {FilterAttribute, MaterialAuthorizations} from '../../search/endpoint/search.model';
import {CategoryKey, UUID} from '@creactives/models';
import {
    GoldenRecordDetails,
    MaterialInRelationshipDetails,
    SmartCreationMaterialDetail,
    SmartCreationSelectedCategories,
    SmartFieldConfiguration
} from './smart-creation.types';
import {FormGroup} from '@angular/forms';
import {DynamicFormGroup, SmartEditorDescriptions, SmartItemTab} from '../../materials-editor/models/material-editor.types';
import {RelationshipType} from '../../relationships/endpoint/relationships.model';
import { RelationshipRole } from '../../layout/store/actions/popup.actions';

export interface SmartCreationSetupResponse {
    similarMaterialsFilters?: SimilarMaterialsFilters;
    taxonomies?: Array<TaxonomyResponse>;
    allowOnlyOpenCategories?: boolean;
    attachmentOnOpenText?: boolean;
    rejectionPossibilities?: Array<RejectionPossibility>;
    noteFlagMandatory?: NoteFlagMandatory;
    goldenRecordEnabled?: boolean;
    multiplePlantCreation?: boolean;
}

export interface RejectionPossibility {
    key: string;
    value: string;
    dropdownOptions?: Array<RejectionPossibility>;
    dropdownListSource?: RejectionPossibilitiesSource;
    selectedOptions?: RejectionPossibility;
}

export enum RejectionPossibilitiesSource {
    SIMILAR_MATERIALS = 'SIMILAR_MATERIALS',
}

enum TaxonomyType {
    MATERIAL_GROUP = 'MATERIAL_GROUP',
    ENRICHED_MATERIAL_GROUP = 'ENRICHED_MATERIAL_GROUP',
    TECHNICAL = 'TECHNICAL'
}

type TaxonomyResponse = {
    type: TaxonomyType;
    key: string;
    isPrincipal: boolean;
};

export type SimilarMaterialsFilters = {
    hideGoldenRecord: boolean;
    hideSecondaries: boolean;
    hideObsolete: boolean;
    includeGoldenRecords: boolean;
    includeMaterialsFromOtherClients: boolean;
};

export type SmartCreationAttributesByDescRequest = {
    description: string;
    domain: string;
    client: string;
    language: string;
    fallbackLanguages: string[];
};

export type SmartCreationSuggestCategoriesRequest = {
    description: string;
    domain: string;
    client: string;
    language: string;
    fallbackLanguages: string[];
    hideObsolete: boolean;
    // filtri da utilizzare
    attributes: Array<FilterAttribute>;
};

export interface SmartCreationReloadMaterialDetailRequest {
    page?: string;
    description?: string;
    domain?: string;
    client?: string;
    language?: string;
    fallbackLanguages?: string[];
    categoriesSelected?: SmartCreationSelectedCategories;
    materialFormControlList?: SmartFieldConfiguration[];
    plantToAddFormControlList?: SmartFieldConfiguration[];
    attributeChanged?: SmartFieldConfiguration;
    categoryChanged?: CategoryKey;
    plants?: any[];
    goldenRecordEnabled?: boolean;
    goldenRecordDetails?: GoldenRecordDetails;
}

export interface SmartCreationPlantInstanceReloadRequest extends SmartCreationReloadMaterialDetailRequest {
    instancePlantToAddFormControlList?: SmartFieldConfiguration[];
    instanceClient?: string;
}
export interface SmartReloadPlantInstanceResponse {
    instancePlantTab: SmartItemTab;
}
export interface SmartPlantData {
    attributes: SmartFieldConfiguration[];
}

export interface SmartCreationReloadInitialDataRequest {
    description?: string;
    domain?: string;
    client?: string;
    language?: string;
    fallbackLanguages?: string[];
    categoriesSelected?: SmartCreationSelectedCategories;
    materialFormControlList?: SmartFieldConfiguration[];
    plantToAddFormControlList?: SmartFieldConfiguration[];
    attributeChanged?: SmartFieldConfiguration;
    categoryChanged?: CategoryKey;
    plants?: any[]
    goldenRecordEnabled?: boolean;
}

export type LocalizedFieldValue = {
    key: string;
    text: string;
};

export interface SmartTranslationResponse {
    attributesFilters: SmartFieldConfiguration[];
    materialData: SmartCreationMaterialDetail;
}

export interface SuggestGrAttributes {
    [key: string]: SuggestAttribute[];
}

export interface SuggestAttribute {
    attributeId: string;
    count: number;
    value: string;
}

export interface Instance {
    materialKey: MaterialKey;
    description?: string;
    countries?: string[];
    plants?: PlantKey[];
    materialId?: string;
    inProgressProcessCount?: number;
    relationships?: InstanceRelationship[];
    materialActionsAuthorizations?: MaterialAuthorizations;
}

export interface InstanceRelationship {
  relationshipId: string;
  type: RelationshipType;
  materialsRelationships: InstanceRelationshipMaterial[];
}

export interface InstanceRelationshipMaterial {
  role: RelationshipRole;
  materialKey: MaterialKey;
  description: string;
  materialId: string;
  isInstance: boolean;
}

export interface RelationshipDetails {
    relationshipId: string;
    relationshipType: RelationshipType;
    materialsInRelationship: MaterialInRelationshipDetails[];
}



export interface ReloadFilteredInitialDataPayload {
    control?: any;
    category?: CategoryKey;
}

export interface SmartCreationDropdownDataRequest extends SmartCreationReloadMaterialDetailRequest {
    queryText?: string;
    currentMaterialForm?: any;
}

export interface MaterialPlantValuation {
    materialId: string;
    plantValuationContainers: PlantValuationContainer[];
}

export interface AlternativeUnitsOfMeasure {
    alternativeUnitOfMeasurement: string;
    numerator: number;
    denominator: number;
}

export interface PlantValuationContainer {
    plantKey: PlantKey;
    countryCode: string;
    materialPlantValuations: any[];
}

export interface PlantKey {
    code: string;
    client: string;
}

export interface MaterialPlantDetails {
    plantKey: PlantKey;
    countryCode: string;
    currency: string;
    businessUnit: string;
    status: string;
    leadTimeInDays: number;
    validFromDate: number;
    deletionFlag: boolean;
    lotSize: string;
    minLotSize: number;
    seriable: string;
    reorderPoint: number;
    safetyStock: number;
    minimumSafetyStock: number;
    maximumStockLevel: number;
    mrpType: string;
    mrpGroup: string;
    purchasingGroup: string;
    followUpMaterialCode: string;
    logisticsHandlingGroup: string;
    mrpController: string;
    inHouseProductionTime: number;
    individualColl: string;
    goodReceiptProcessingTimeInDays: number;
    controlKeyForQM: string;
    certificateType: string;
    batchManagementFlag: boolean;
    schedulingMarginKeyForFloats: string;
    supportsMultipleValuations: boolean;
    intrastatCode: string;
    controlCodeConsumptionTaxesForeignTrade: string;
    materialCFOPCategory: string;
    periodIndicator: string;
    procurementType: string;
    checkingGroupAvailabilityCheck: string;
    profitCenter: string;
    plantOldMaterialNumber: string;
    lotSizeForProductCosting: number;
    storageLocations: MaterialStorageLocationDetails[];
    materialPriceDetails: MaterialPriceDetails[];
}

export interface MaterialStorageLocationDetails {
    storageLocation: string;
    valuatedUnrestrictedUseStock: number;
    stockInTransfer: number;
    stockInQualityInspection: number;
    blockedStock: number;
    storageBin: string;
    deletionFlag: boolean;
}

export interface MaterialPriceDetails {
    plantKey: PlantKey;
    valuationType: string;
    standardPrice: number;
    priceUnit: number;
    usageMaterial: string;
    originMaterial: string;
    priceControlIndicator: string;
    valuationClass: string;
    valuationCategory: string;
    movingAveragePrice: number;
}

export interface FilteredInitialData {
    technicalAttributes: DynamicFormGroup,
    normalizedDescriptions: DynamicFormGroup,
    groupedDescriptions?: SmartEditorDescriptions
}

export interface FiltersFormData {
    filterFormGroup: FormGroup,
    filterItems: any[]
}

export interface WarningDetails {
    id: number;
    materialId: UUID;
    warningId: UUID;
    warningType: string;
}

export interface MaterialKey {
    client?: string;
    materialCode?: string;
}

export interface MaterialValuation {
    plantKey: PlantKey;
    countryCode: string;
    materialPlantValuations: Array<MaterialPlantValuations>;
}

export interface MaterialPlantValuations {
    materialId: string;
    plantValuationContainers: MaterialPlantValuation[];
}

export interface EditApprovalSetupResponse {
    rejectionPossibilities?: Array<RejectionPossibility>;
    taxonomies?: Array<TaxonomyResponse>;
    attachmentOnOpenText?: boolean;
    noteFlagMandatory?: NoteFlagMandatory;
}

export interface NoteFlagMandatory {
    request?: boolean;
    approval?: boolean;
    additionalInformation?: boolean;
    reject?: boolean;
}
