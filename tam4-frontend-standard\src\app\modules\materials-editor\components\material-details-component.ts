import { AfterContentInit, ApplicationRef, ChangeDetectorRef, Component, ContentChildren, Do<PERSON>heck, EventEmitter, input, Input, OnDestroy, OnInit, Output, QueryList, Signal, ViewChild } from '@angular/core';

import { FormGroup } from '@angular/forms';
import { AttachmentInfo, AttachmentInstanceDetails, SelectorMap } from '@creactives/models';
import { Tam4TranslationService } from '@creactives/tam4-translation-core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { PrimeTemplate } from "primeng/api";
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { takeUntil } from 'rxjs/operators';
import { Tam4SelectorConfig, TamAbstractReduxComponent } from 'src/app/components';
import { Tam4ComponentEvent, TamApePageType } from "src/app/models";
import { DynamicFormEventPayload } from "src/app/modules/materials-modal/store/materials-modal.action-types";
import { EventUtils, ObjectsUtils } from 'src/app/utils';
import { CommonUtils } from "src/app/utils/common.utils";
import { MDDomain } from '../../components/types';
import { selectEnableDevDiagnostic } from '../../layout/store/profile/profile.state';
import { AttachmentInstanceCleanRequest, AttachmentInstanceUploadRequest } from '../../materials-modal/models/modals-modal.types';
import { MaterialsModalSelectors } from '../../materials-modal/store/materials-modal.selectors';
import { MaterialsModalState } from '../../materials-modal/store/materials-modal.state';
import { SmartCreationLinkUnlinkConfirm } from '../../smart-creation/components/smart-creation-link-unlink-confirm';
import { SmartCreationSearchLinkInstancesDialog } from '../../smart-creation/components/smart-creation-search-link-instances-modal.dialog';
import { SmartCreationFormControl } from '../../smart-creation/models/smart-creation-form.types';
import { Instance, SuggestGrAttributes } from '../../smart-creation/models/smart-creation-validation.types';
import { GoldenRecordInstance, LinkUnlinkInstancesRequest, SmartCreationMaterialDetail, SmartFieldConfiguration } from '../../smart-creation/models/smart-creation.types';
import { DynamicTableGrInstanceComponent } from '../dynamic-components/dynamic-table-gr-instance.component';
import { BaseMaterialEditorTabsKeys, SelectedCategoriesNotSuggested, SmartItemTab, ViewModeEnum } from '../models/material-editor.types';

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'unlinkInstancesDone', selector: MaterialsModalSelectors.getUnlinkInstancesDone},
  {key: 'instancesWithRelationships', selector: MaterialsModalSelectors.getInstancesWithRelationships},
];

@Component({
  selector: 'div[scMaterialDetails]',
  host: {
    "[class]": "'flex flex-column gap-3'"
  },
  template: `
    @if (appendTop) {
      <ng-container *ngTemplateOutlet="appendTop?.template"></ng-container>
    }
    <p-panel class="compat" id="attachments" *ngIf="showAttachments">
      <ng-template pTemplate="header">
        <div class="accordion-header flex gap-3">
          <span class="tab-tile vertical-align-middle font-semibold">
            {{ 'smartCreation.attachments.label' | translate }}
          </span>
        </div>
      </ng-template>
      @if (this.viewMode === ViewModeEnum.GR_EDIT) {
        <div scMaterialGRAttachments
             [disabled]="disabled"
             [instances]="attachmentsInstances"
             [grAttachments]="attachments"
             [viewMode]="viewMode"
             (attachmentUpload)="handleAttachmentUpload($event)"
             (attachmentRemove)="handleAttachmentRemove($event)"
             (attachmentInstanceUpload)="handleAttachmentInstanceUpload($event)"
             (attachmentInstanceRemove)="handleAttachmentInstanceRemove($event)"
             (attachmentDownload)="handleAttachmentDownload($event)"
        >
        </div>
      } @else {
        <material-attachments [disabled]="disabled"
                              [attachments]="attachments"
                              [viewMode]="viewMode"
                              [acceptedAttachmentsMimeTypes]="acceptedAttachmentsMimeTypes()"
                              (attachmentUpload)="handleAttachmentUpload($event)"
                              (attachmentRemove)="handleAttachmentRemove($event)"
                              (attachmentDownload)="handleAttachmentDownload($event)"
        ></material-attachments>
      }

    </p-panel>

    @if (this.viewMode === ViewModeEnum.GR_APPROVE) {
      <p-panel class="compat" id="thumbnails">
        <ng-template pTemplate="header">
          <div class="accordion-header flex gap-3">
            <span class="tab-tile vertical-align-middle font-semibold">
              {{ 'smartCreation.thumbnails.label' | translate }}
            </span>
          </div>
        </ng-template>
        <div scMaterialImageCarousel
              [imageInstances]="imageInstances"
              (onSelectThumbnails)="handleSelectImageThumbnail($event)">
        </div>
      </p-panel>
    }

    @if (categoriesFormGroup()) {
      <p-panel class="compat" id="classification">
        <ng-template pTemplate="header">
          <div class="accordion-header flex gap-3">
        <span class="tab-tile vertical-align-middle font-semibold">
        {{ 'smartCreation.classification.label' | translate }}
        </span>
          </div>
        </ng-template>
        <categories-selection
            [categoriesFormGroup]="categoriesFormGroup()"
            [selectedCategoriesNotSuggested]="selectedCategoriesNotSuggested"
            [disabled]="!changeClassificationEnabled"
            [viewMode]="viewMode"
            [disableReset]="true"
            (resetClicked)="handleCategoriesResetClicked($event)"
            (selectManuallyClicked)="handleSelectManuallyClicked($event)"
            (selectedCategoryChanged)="handleSelectedCategoryChanged($event)"
        ></categories-selection>

      </p-panel>
    }
    @if (dynamicFormGroup()) {
      @for (formData of dynamicFormGroup(); track formData; let i = $index) {
        <p-panel class="compat" [formGroup]="formData?.formGroup" [id]="formData.key">
          <ng-template pTemplate="header">
            <div materialDetailsAccordionHeader [tab]="formData"></div>
          </ng-template>
          @switch (formData.key) {
            @case (BaseMaterialEditorTabsKeys.GOLDEN_RECORD) {
              <div scMaterialGoldenRecord [goldenRecordSheet]="goldenRecordSheet"
                   [page]="page"
                   (selectPlantClicked)="handleSelectPlantClicked($event)"
                   [viewMode]="viewMode"
                   [notEnabled]="!(viewMode === ViewModeEnum.CREATE)"
                   (goldenRecordDetailsChanged)="onGoldenRecordChanged($event)">
              </div>
            }
            @case (BaseMaterialEditorTabsKeys.DESCRIPTIONS) {
              <div scMaterialDetailsDescriptionEditor
                   [viewMode]="viewMode"
                   [page]="page"
                   [viewMode]="viewMode"
                   [descriptionFormData]="formData"></div>
            }
            @case (BaseMaterialEditorTabsKeys.PLANT_DATA) {
              <div scMaterialPlantData [plantSheet]="plantSheet"
                   [dynamicFormData]="formData"
                   [showFieldWithNoValue]="showFieldWithNoValue"
                   [page]="page"
                   [locale]="currentLocale()"
                   [plants]="plants"
                   [initialData]="initialData()"
                   [viewMode]="viewMode"
                   [singlePlantAddOnly]="true"
                   [currentClient]="currentClient"
                   (inputChange)="inputChange.emit($event)"
                   (onUpdatePlantData)="handleUpdatePlantData($event)">
              </div>
            }

            @default { 
              @for (inputComponent of formData?.items; track inputComponent) {
                <span scDynamicContainerTemplate
                      [showEmptyValues]="showFieldWithNoValue"
                      [dynamicComponent]="inputComponent?.component"
                      [dynamicParams]="getParams(formData?.formGroup, inputComponent?.componentParams, i)"
                      [label]="inputComponent?.label | translate"
                      [mandatory]="inputComponent?.mandatory"
                      [coreAttribute]="inputComponent?.componentParams?.coreAttribute"
                      [editable]="inputComponent?.componentParams?.editable"
                      [viewMode]="viewMode"
                      [locale]="currentLocale()"
                      [suggestions]="getSuggestAttributes(inputComponent?.id)"
                      (formComponentEvent)="handleDynamicEvent($event)"
                      [currentClient]="currentClient"
                      [enableDevDiagnostic]="enableDevDiagnostic"
                ></span>
              }

            }
          }
        </p-panel>
      }
    }
    @if (initialData()?.additionalMaterialInformation?.relationshipsDetails?.length > 0) {
      <p-panel id="relations" class="compat">
        <ng-template pTemplate="header">
          <div class="accordion-header flex gap-3">
            <span class="tab-tile vertical-align-middle font-semibold">{{ 'layout.item-details.tabs.relations' | translate }}</span>
          </div>
        </ng-template>
        <relations-table
            [relations]="initialData()?.additionalMaterialInformation?.relationshipsDetails"
            [canDeleteRelationship]="initialData()?.materialActionsAuthorization?.canDeleteRelationship?.enabled"
            (relationshipDeletion)="handleRelationshipDeletion($event)">
        </relations-table>
      </p-panel>
    }
    @if (signals?.instancesWithRelationships()?.length > 0) {
      <p-panel id="instances" class="compat">
        <ng-template pTemplate="header">
          <div class="accordion-header flex gap-3">
            <span class="tab-tile vertical-align-middle font-semibold">{{ 'layout.item-details.tabs.instances' | translate }}</span>
          </div>
        </ng-template>
        <instances-table [instances]="signals.instancesWithRelationships()"
                         [canUnlink]="viewMode === ViewModeEnum.GR_EDIT && initialData()?.materialActionsAuthorization?.canLinkUnlinkInstances?.enabled"
                         (onSelectItem)="selectItem($event)" (onUnselectItem)="unselectItem($event)"
                         (unlinkInstance)="handleUnlinkInstances($event)">
        </instances-table>
        <ng-template pTemplate="footer"
                     *ngIf="viewMode === ViewModeEnum.GR_EDIT && initialData()?.materialActionsAuthorization?.canLinkUnlinkInstances?.enabled">
          <div class="flex align-items-center gap-3">
            <p-button styleClass="p-button-primary p-button-fixed" (click)="handleLinkInstances($event)"
                      [disabled]="(selectedItems && selectedItems.length > 0)">
              <i class="fas fa-link fa-sm"></i> {{ 'smartCreation.modalDetails.linkUnlink.link-instances' | translate }}
            </p-button>
            <p-button styleClass="p-button-primary p-button-fixed" (click)="handleUnlinkInstances($event)"
                      [disabled]="(selectedItems && selectedItems.length === 0)">
              <i class="fas fa-unlink fa-sm"></i> {{ 'smartCreation.modalDetails.linkUnlink.unlink-instances' | translate }}
            </p-button>
          </div>
        </ng-template>
      </p-panel>
    }
    @if (appendBottom) {
      <ng-container *ngTemplateOutlet="appendBottom.template"></ng-container>
    }
  `,
  // changeDetection: ChangeDetectionStrategy.OnPush

})
export class MaterialDetailsComponent extends TamAbstractReduxComponent<SelectorMap> implements OnInit, OnDestroy, AfterContentInit, DoCheck {

  readonly TamApePageType = TamApePageType;
  @ViewChild(DynamicTableGrInstanceComponent) dynamicTableGrInstanceComponent: DynamicTableGrInstanceComponent;

  @Input({required: true}) page: string;
  dynamicFormGroup?: Signal<any[]> = input.required<any[]>();
  @Input() plantSheet?: SmartItemTab;
  @Input() plants?: any;
  @Input() goldenRecordSheet?: SmartItemTab;
  @Input() attachments: AttachmentInfo[];
  initialData?: Signal<SmartCreationMaterialDetail> = input<SmartCreationMaterialDetail>(null);

  @Input() showGrFromScratch: boolean = false;

  categoriesFormGroup: Signal<FormGroup> = input.required<FormGroup>();
  @Input() selectedCategoriesNotSuggested: SelectedCategoriesNotSuggested;
  @Input() disabled: boolean;
  @Input() viewMode: ViewModeEnum;
  @Input() suggestedAttributes: SuggestGrAttributes;
  @Input() showFieldWithNoValue: boolean = true;

  @Input() changeClassificationEnabled: boolean = false;

  @Input() showAttachments: boolean = true;

  acceptedAttachmentsMimeTypes = input<string[]>(null);

  @Output() resetClicked = new EventEmitter<string>();
  @Output() selectManuallyClicked = new EventEmitter<string>();
  @Output() selectedCategoryChanged = new EventEmitter<string>();
  @Output() updatePlantData = new EventEmitter<Tam4ComponentEvent<string, any>>();

  @Output() inputChange = new EventEmitter<SmartFieldConfiguration>();
  @Output() categoryResetClicked = new EventEmitter<string>();
  @Output() categorySelectManuallyClicked = new EventEmitter<string>();
  @Output() categorySelectedChanged = new EventEmitter<string>();
  @Output() onUpdateAlternativeUom = new EventEmitter<Tam4ComponentEvent<any, DynamicFormEventPayload>>();
  @Output() attachmentUpload = new EventEmitter<File>();
  @Output() attachmentInstanceUpload = new EventEmitter<AttachmentInstanceUploadRequest>();
  @Output() attachmentRemove = new EventEmitter<string>();
  @Output() attachmentInstanceRemove = new EventEmitter<AttachmentInstanceCleanRequest>();
  @Output() attachmentDownload = new EventEmitter<string>();
  @Output() onSelectThumbnailImage = new EventEmitter<string>();
  @Output() plantSelectManuallyClicked = new EventEmitter<string>();
  @Output() goldenRecordChanged = new EventEmitter<any>();
  @Output() relationshipDeletion = new EventEmitter<{ relationshipId: string; successfulOperation: boolean }>();
  @Output() onGrInstancePlantEvent = new EventEmitter<Tam4ComponentEvent<any, {
    instance: GoldenRecordInstance,
    view: ViewModeEnum,
    page: string
  }>>();


  @Input() currentClient: string;
  @Input() materialId: string;

  @Input() attachmentsInstances: Array<AttachmentInstanceDetails>;
  @Input() imageInstances: Array<AttachmentInstanceDetails>;

  activeIndex: number[] = [];
  plantsGrDialogRef: DynamicDialogRef | undefined;

  @Input() link = true;
  @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate>;
  appendTop: PrimeTemplate;
  appendBottom: PrimeTemplate;

  enableDevDiagnostic: boolean = false;
  selectedItems: Array<Instance> = [];
  protected readonly ViewModeEnum = ViewModeEnum;
  protected readonly BaseMaterialEditorTabsKeys = BaseMaterialEditorTabsKeys;
  protected readonly MDDomain = MDDomain;

  constructor(private cdRef: ChangeDetectorRef, private appRef: ApplicationRef, private dialogService: DialogService, protected store: Store<MaterialsModalState>,
              protected tamTranslate: Tam4TranslationService, protected translate: TranslateService) {
    super(translate, tamTranslate, store, storeSelectors);

  }

  ngAfterContentInit(): void {
    this.templates.forEach(template => {
      switch (template.name) {
        case 'appendTop':
          this.appendTop = template;
          break;
        case 'appendBottom':
          this.appendBottom = template;
          break;
        default:
          // Trattamento del template di default (content)
          break;
      }
    });
  }

  ngDoCheck(): void {
    // CommonUtils.pingForCd(this.cdRef, this.appRef);
  }

  ngOnInit() {
    CommonUtils.pingForCd(this.cdRef);

    this.store.select(selectEnableDevDiagnostic)
      .pipe(takeUntil(this.ngDestroy$))
      .subscribe(enabled => {
        this.enableDevDiagnostic = enabled || false;
      });
  }

  ngOnDestroy() {
    super.ngOnDestroy();
    this.selMap?.unlinkInstancesDone.subscribe(value => {
      if (value) {
        this.dynamicTableGrInstanceComponent.resetTable();
      }
    });
  }

  onInputValueEvent(id: string, formGroup: FormGroup, sheetIndex: number, suggestedValue?: string) {
    if (formGroup?.controls?.[id] !== null) {
      const itemIndex = this.dynamicFormGroup()[sheetIndex].items.findIndex(x => x.id === id);
      if (itemIndex >= 0) {
        const {
          component,
          componentParams,
          ...currentControl
        } = this.dynamicFormGroup()[sheetIndex].items[itemIndex];

        const currValue = formGroup?.controls?.[id]?.value;
        currentControl.value = ObjectsUtils.isNotNoU(currValue) ? ObjectsUtils.flatArray(currValue) : suggestedValue;

        if (this.dynamicFormGroup()[sheetIndex].items[itemIndex].unitsOfMeasure &&
          this.dynamicFormGroup()[sheetIndex].items[itemIndex].unitsOfMeasure.length > 0) {
          currentControl.unitsOfMeasureSelected = formGroup?.controls[id + '.mu']?.value?.toString();
        }

        // this.dynamicFormGroup()[sheetIndex].errors = [];
        this.inputChange.emit(currentControl);
        if (this.activeIndex.indexOf(sheetIndex + 1) === -1) {
          this.activeIndex.push(sheetIndex + 1);
        }
      }
    }
  }

  onChangeUnitOfMeasureEvent(id: string, selectedUnitOfMeasure: string, formGroup: FormGroup, sheetIndex: number) {

    const itemIndex = this.dynamicFormGroup()[sheetIndex].items.findIndex(x => x.id === id);
    if (itemIndex >= 0) {
      const currentControl = this.dynamicFormGroup()[sheetIndex].items[itemIndex];
      currentControl.value = formGroup.controls[id].value?.toString();
      currentControl.unitsOfMeasureSelected = selectedUnitOfMeasure;

      const currentUomControl: SmartFieldConfiguration = {
        id: id + '.mu',
        attributeName: id + '.mu',
        value: selectedUnitOfMeasure
      };

      this.inputChange.emit(currentUomControl);

      if (this.activeIndex.indexOf(sheetIndex + 1) === -1) {
        this.activeIndex.push(sheetIndex + 1);
      }
    }
  }

  getParams(formGroup: FormGroup | undefined, params: any, sheetIndex: number) {
    if (!params) {
      params = {};
    }

    const _dynamicFormGroup = this.dynamicFormGroup()[sheetIndex];

    if (params?.formControlName === '4_TAM_AlternativeUnitOfMeasure') {
      params.componentProps.standardUom = _dynamicFormGroup.formGroup.controls['4_TAM_UnitOfMeasure'].value;
    }

    params.viewMode = this.viewMode;

    if (ObjectsUtils.isNoU(formGroup)) {
      return {
        ...params
      };
    }

    const fieldErrors = _dynamicFormGroup?.errors?.filter(e => e.field === params.formControlName);
    (formGroup?.controls?.[params.formControlName] as SmartCreationFormControl).validateErrors = fieldErrors;

    return {
      hasError: fieldErrors?.length > 0,
      sheetIndex,
      errors: fieldErrors,
      hasWarning: false,
      onInputValueEvent: (id: string, formGroupInp: FormGroup, sheetIndexInp: number, suggestedValue?: string) =>
        this.onInputValueEvent(id, formGroupInp, sheetIndexInp, suggestedValue),
      onChangeUnitOfMeasureEvent: (id: string,
                                   selectedUnitOfMeasure: string,
                                   formGroupInp: FormGroup,
                                   sheetIndexInp: number) =>
        this.onChangeUnitOfMeasureEvent(id, selectedUnitOfMeasure, formGroupInp, sheetIndexInp),
      ...params
    };
  }


  getSuggestAttributes(id: string) {
    return id && this.suggestedAttributes ? this.suggestedAttributes[id] : [];
  }

  // hasMandatoryFields(tabKey: string): boolean {
  //   const form = this.dynamicFormGroup()?.filter(f => ObjectsUtils.isNotNoU(f)).find(form => tabKey === form?.key);
  //   if (form) {
  //     return form.items.some(item => item.mandatory);
  //   }
  //   return false;
  // }

  open(e) {
    if (this.activeIndex.indexOf(e.index) === -1) {
      this.activeIndex.push(e.index);
    }
  }

  close(e) {
    const i = this.activeIndex.indexOf(e.index);
    if (i >= 0) {
      this.activeIndex.splice(i, 1);
    }
  }

  handleUpdatePlantData(attributes: Tam4ComponentEvent<string, any>) {

    this.updatePlantData.emit(attributes);
  }

  handleCategoriesResetClicked(taxonomy: string) {

  }

  handleSelectManuallyClicked(taxonomy: string) {
    this.categorySelectManuallyClicked.emit(taxonomy);
  }

  handleSelectedCategoryChanged(taxonomy: string) {
    this.categorySelectedChanged.emit(taxonomy);
  }

  handleAttachmentUpload(file: File) {
    this.attachmentUpload.emit(file);
  }

  handleAttachmentInstanceUpload(event: AttachmentInstanceUploadRequest) {
    this.attachmentInstanceUpload.emit(event);
  }

  handleAttachmentRemove(uuid: string) {
    this.attachmentRemove.emit(uuid);
  }

  handleAttachmentInstanceRemove(event: AttachmentInstanceCleanRequest) {
    this.attachmentInstanceRemove.emit(event);
  }

  handleAttachmentDownload(uuid: string) {
    this.attachmentDownload.emit(uuid);
  }

  handleDynamicEvent(event$: Tam4ComponentEvent<any, any>): void {
    switch (event$?.type) {
      case 'onUpdateAlternativeUomFn':
        this.onUpdateAlternativeUom.emit(event$);
        break;
      default:
        break;
    }
  }

  handleRelationshipDeletion(event: { relationshipId: string; successfulOperation: boolean }) {
    this.relationshipDeletion.emit(event);
  }

  onGoldenRecordChanged(event: any) {
    this.goldenRecordChanged.emit(event);
  }

  handleSelectPlantClicked(event: GoldenRecordInstance) {

    this.onGrInstancePlantEvent.emit({
      type: 'OPEN-GR-INSTANCE-PLANT-SELECTION',
      payload: {instance: event, view: this.viewMode, page: this.page}
    });
  }

  selectItem(selectedMaterial: Instance) {
    this.selectedItems.push(selectedMaterial);
  }

  unselectItem(data: Instance) {
    this.selectedItems = this.selectedItems.filter(item => item.materialId !== data.materialId);
  }

  handleLinkInstances(event$: any) {
    EventUtils.stopPropagation(event$);
    const smartCreationMaterialDetail = this.initialData();
    const goldenRecordCode: string = smartCreationMaterialDetail.additionalMaterialInformation.goldenRecordCode;
    const instancesClient: string[] = smartCreationMaterialDetail.additionalMaterialInformation.instances.map(i => i.materialKey.client);
    const goldenRecordMaterialId: string = smartCreationMaterialDetail.additionalMaterialInformation.materialId;
    const description: string = smartCreationMaterialDetail.additionalMaterialInformation.description;
    const modalCfg: DynamicDialogConfig = {
      header: this.translate.instant('smartCreation.modalDetails.linkUnlink.search-instances'),
      data: {goldenRecordCode, materialId: goldenRecordMaterialId, instancesClient, description},
    };

    this.dialogService.open(SmartCreationSearchLinkInstancesDialog, modalCfg);
  }

  handleUnlinkInstances(event$: any) {
    EventUtils.stopPropagation(event$);
    const goldenRecordCode: string = this.initialData().additionalMaterialInformation.goldenRecordCode;
    const goldenRecordMaterialId: string = this.initialData().additionalMaterialInformation.materialId;
    const unlinkInstances = this.selectedItems.map(e => ({
      materialId: e.materialId,
      clientId: e.materialKey.client,
      goldenRecord: false,
      goldenRecordCode: e.materialKey.materialCode,
      description: e.description,
      materialCode: e.materialKey.materialCode
    }));
    const request: LinkUnlinkInstancesRequest = {
      goldenRecordCode,
      goldenRecord: goldenRecordMaterialId,
      unlinkInstances
    };
    const modalCfg: DynamicDialogConfig = {
      header: this.translate.instant('smartCreation.modalDetails.linkUnlink.unlink-instances'),
      data: {request},
    };

    this.plantsGrDialogRef = this.dialogService.open(SmartCreationLinkUnlinkConfirm, modalCfg);
  }

  handleSelectImageThumbnail(imageId: string) {
    this.onSelectThumbnailImage.emit(imageId);
  }

}
