import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {
  AttributeNamesTranslationService,
  AttributeValueTranslationService,
  CategoryTranslationService,
  ClientTranslationService,
  CountryTranslationService, CurrencyTypeTranslateService,
  MetricTranslationService,
  PlantTranslationService,
  TaxonomyTranslationService,
  UserTranslationService,
  WarningTypeTranslateService
} from './tam4-pipe.service';
import {TranslateService} from '@ngx-translate/core';
import {ObjectsUtils} from 'src/app/utils';
import {FormatCurrencyPipe} from '../../pipes';
import {Store} from '@ngrx/store';
import {DecimalPipe} from '@angular/common';

@Pipe({
  name: 'categoryTranslate',
  pure: false
})
export class CategoryTranslatePipe implements PipeTransform {

  constructor(private translationService: CategoryTranslationService) {
  }

  transform(value: string, taxonomy: string): any {
    return this.translationService.translate(value, taxonomy);
  }

}


@Pipe({
  name: 'plantTranslate',
  pure: false
})
export class PlantTranslatePipe implements PipeTransform {

  constructor(private translationService: PlantTranslationService) {
  }

  transform(value: string, client: string, showKey: boolean = false): any {
    return this.translationService.translate(value, client, showKey);
  }

}


@Pipe({
  name: 'clientTranslate',
  pure: false
})
export class ClientTranslatePipe implements PipeTransform {

  constructor(private translationService: ClientTranslationService) {
  }

  transform(value: string): any {
    return this.translationService.translate(value);
  }

}

@Pipe({
  name: 'userTranslate',
  pure: false
})
export class UserTranslatePipe implements PipeTransform {

  constructor(private translationService: UserTranslationService) {
  }

  transform(value: string): any {
    return this.translationService.translate(value);
  }

}

@Pipe({
  name: 'countryTranslate',
  pure: false
})
export class CountryTranslatePipe implements PipeTransform {

  constructor(private translationService: CountryTranslationService) {
  }

  transform(value: string): any {
    return this.translationService.translate(value);
  }
}

@Pipe({
  name: 'taxonomyTranslate',
  pure: false
})
export class TaxonomyTranslatePipe implements PipeTransform {

  constructor(private translationService: TaxonomyTranslationService) {
  }

  transform(value: string): any {
    return this.translationService.translate(value);
  }
}

@Pipe({
  name: 'attributeValueTranslate',
  pure: false
})
export class AttributeValueTranslatePipe implements PipeTransform {

  constructor(private translationService: AttributeValueTranslationService) {
  }

  transform(value: string, attribute: string, defaultWhenMissing?: string): any {
    return this.translationService.translate(value, attribute, defaultWhenMissing);
  }
}

@Pipe({
  name: 'attributeNameTranslate',
  pure: false
})
export class AttributeNameTranslatePipe implements PipeTransform {

  constructor(private translationService: AttributeNamesTranslationService) {
  }

  transform(attribute: string): any {
    return this.translationService.translate(attribute);
  }
}

@Pipe({
  name: 'warningTypeTranslate',
  pure: false
})
export class WarningTypeTranslatePipe implements PipeTransform {

  constructor(private translationService: WarningTypeTranslateService) {
  }

  transform(warningTyp: string): any {
    return this.translationService.translate(warningTyp);
  }
}

@Pipe({
  name: 'metricTranslate',
  pure: false
})
export class MetricTranslatePipe implements PipeTransform {

  constructor(private translationService: MetricTranslationService) {
  }

  transform(metric: string): any {
    return this.translationService.translate(metric);
  }
}


@Injectable({
  providedIn: 'root'
})
export class TranslationPipeFactory {

  private pipeMap: { [key: string]: PipeTransform } = {};

  constructor(private categoryTranslate: CategoryTranslationService,
              private plantTranslate: PlantTranslationService,
              private clientTranslate: ClientTranslationService,
              private userTranslate: UserTranslationService,
              private countryTranslate: CountryTranslationService,
              private taxonomyTranslate: TaxonomyTranslationService,
              private attributeValueTranslate: AttributeValueTranslationService,
              private attributeNameTranslate: AttributeNamesTranslationService,
              private warningTypeTranslate: WarningTypeTranslateService,
              private metricTranslate: MetricTranslationService,
              private decimalPipe: DecimalPipe,
              private translate: TranslateService,
              private store: Store,
              private currencyType: CurrencyTypeTranslateService) {

    this.pipeMap['category'] = new CategoryTranslatePipe(categoryTranslate);
    this.pipeMap['plant'] = new PlantTranslatePipe(plantTranslate);
    this.pipeMap['4_SDM_Plant'] = new PlantTranslatePipe(plantTranslate);
    this.pipeMap['client'] = new ClientTranslatePipe(clientTranslate);
    this.pipeMap['4_SDM_Client'] = new ClientTranslatePipe(clientTranslate);
    this.pipeMap['user'] = new UserTranslatePipe(userTranslate);
    this.pipeMap['country'] = new CountryTranslatePipe(countryTranslate);
    this.pipeMap['taxonomy'] = new TaxonomyTranslatePipe(taxonomyTranslate);
    this.pipeMap['attributeValue'] = new AttributeValueTranslatePipe(attributeValueTranslate);
    this.pipeMap['attributeName'] = new AttributeNameTranslatePipe(attributeNameTranslate);
    this.pipeMap['warningType'] = new WarningTypeTranslatePipe(warningTypeTranslate);
    this.pipeMap['metric'] = new MetricTranslatePipe(metricTranslate);
    this.pipeMap['1_TotalStock'] = new FormatCurrencyPipe(this.store, this.decimalPipe);
    this.pipeMap['1_TotalPO'] = new FormatCurrencyPipe(this.store, this.decimalPipe);
    // this.pipeMap['default'] = new TranslateService;
  }

  public transform(value: any, pipeName: string = 'default', ...args: any): any {

    if (ObjectsUtils.isStringBlank(pipeName) || pipeName === 'default') {
      const out = this.translate.instant(value, args);
      return out !== '' ? out : value;
    }

    if (pipeName === 'autodetect') {
      const attrName = args[0];
      const _args = args.slice(1);
      const pipeToRun = this.pipeMap[attrName];
      return !!pipeToRun ? this.pipeMap[attrName]?.transform(value, ..._args) : this.pipeMap['attributeValue']?.transform(value, ...args);
    }
    return this.pipeMap[pipeName]?.transform(value, args);
  }
}

@Pipe({
  name: 'tam4Translate',
  pure: false
})
export class Tam4TranslatePipe implements PipeTransform {

  constructor(private translationService: TranslationPipeFactory) {
  }

  transform(value: any, attrType: string = 'default', ...args): any {
    return this.translationService.transform(value, attrType, ...args);
  }
}


