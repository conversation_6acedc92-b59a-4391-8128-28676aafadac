import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AttachmentInstanceDetails } from "@creactives/models";

@Component({
    selector: 'div[scMaterialImageCarousel]',
    template: `
        @if (!imageInstances || imageInstances.length == 0) {
            <div class="attachment-view flex align-items-center justify-content-center">
                {{ 'smartCreation.thumbnails.no-thumbnails' |translate }}
            </div>
        } @else {
            <p-carousel [value]="imageInstances" [numVisible]="3" [numScroll]="3" [circular]="false">
                <ng-template let-imageInstance pTemplate="item">
                    <div class="border-1 surface-border border-round m-2 p-3 text-center image-select-container"
                        (click)="!imageInstance.disabled ? handleSelectImage(imageInstance) : null"
                        [ngClass]="{'image-selected': imageInstance.selected, 'image-disabled': imageInstance.disabled}">
                        <div class="font-medium" style="margin-bottom: 0.5rem;">
                            {{ imageInstance.client + "/" + imageInstance.materialCode }}
                        </div>
                        <img [src]="'data:image/jpeg;base64,' + imageInstance.thumbnail.content"
                             [alt]="imageInstance.name"
                             class="w-full border-round mb-2 image-select-carousel"/>
                    </div>
                </ng-template>
            </p-carousel>
        }
    `,
    styleUrls: ['./material-image-carousel.component.scss']
})
export class MaterialImagesCarouselComponent {

    @Input() imageInstances: AttachmentInstanceDetails[] = [];
    @Output() onSelectThumbnails = new EventEmitter<string>();

    handleSelectImage(attachment: AttachmentInstanceDetails) {
      this.onSelectThumbnails.emit(attachment.selected ? null : attachment.thumbnail.attachmentId);
    }

}
