import {AttachmentContentInfo, AttachmentInfo, FieldType, UUID} from '@creactives/models';
import { produce } from "immer";
import { DynamicFormEventPayload } from 'src/app/modules/materials-modal/store/materials-modal.action-types';
import { ObjectsUtils } from 'src/app/utils';
import { TamApePageUtils } from '../../../models';
import { initPlantSheet } from '../../materials-editor/common/materials-editor.function';
import {
  CategoryTreeLoadChildrenActionPayload,
  SmartCreationMaterialDetail,
  SmartCreationSelectedCategories
} from '../../smart-creation/models/smart-creation.types';
import {
    MATERIALS_MODAL_ACTION_NAMES,
    MaterialsModalAction_Attachments_Clean,
    MaterialsModalAction_Attachments_Instance_Clean,
    MaterialsModalAction_Attachments_Instance_Upload,
    MaterialsModalAction_Attachments_Instance_Upload_Success,
    MaterialsModalAction_Attachments_Upload_Success,
    MaterialsModalAction_AttachmentsInfo_Get_Success,
    MaterialsModalAction_Instances_Attachments_And_Image_Success,
    MaterialsModalAction_Instances_With_Relationships_Success,
    MaterialsModalAction_LoadDetails,
    MaterialsModalAction_LoadDetails_Success,
    MaterialsModalAction_Material_Image_Upload_Success,
    MaterialsModalAction_OpenAdditionalInfo,
    MaterialsModalAction_OpenAdditionalInfo_Success,
    MaterialsModalAction_OpenDeleteGR,
    MaterialsModalAction_OpenDeleteGR_Success,
    MaterialsModalAction_OpenDeleteGRApproval,
    MaterialsModalAction_OpenDeleteGRApproval_Success,
    MaterialsModalAction_OpenDetails,
    MaterialsModalAction_OpenDetails_Success,
    MaterialsModalAction_OpenEdit,
    MaterialsModalAction_OpenEdit_Success,
    MaterialsModalAction_OpenEditApproval,
    MaterialsModalAction_OpenEditApproval_Success,
    MaterialsModalAction_OpenGrApproval,
    MaterialsModalAction_OpenGrApproval_Success,
    MaterialsModalAction_OpenGrEditApproval,
    MaterialsModalAction_OpenGrEditApproval_Success,
    MaterialsModalAction_OpenProcessDetailsEdit,
    MaterialsModalAction_OpenProcessDetailsEdit_Success,
    MaterialsModalAction_OpenProcessDetailsView,
    MaterialsModalAction_OpenProcessDetailsView_Success,
    MaterialsModalAction_Process_Id_Set,
    MaterialsModalAction_Reload_Initial_Data,
    MaterialsModalAction_Reload_Initial_Data_Success,
    MaterialsModalAction_Reload_Local_Initial_Data_Success,
    MaterialsModalAction_RequestGR_Delete_Success,
    MaterialsModalAction_SaveEdit_Success,
    MaterialsModalAction_SearchForLink_Success,
    MaterialsModalAction_Select_Category,
    MaterialsModalAction_Select_Category_Clean,
    MaterialsModalAction_Setup_Init_SetPage,
    MaterialsModalAction_Setup_Init_Success,
    MaterialsModalAction_Suggest_Categories,
    MaterialsModalAction_Suggest_Categories_Success,
    MaterialsModalAction_Thumbnails_Selected,
    MaterialsModalAction_Tree_Categories_Success,
    MaterialsModalActionTypes
} from './materials-modal.actions';
import { MaterialsModalState, TAM_MATERIALS_MODAL_INITIAL_STATE } from './materials-modal.state';


export class MaterialsModalReducers {

  public static reduce(state: MaterialsModalState = TAM_MATERIALS_MODAL_INITIAL_STATE,
                       action: MaterialsModalActionTypes = {type: null}): MaterialsModalState {
    switch (action.type) {
      case MATERIALS_MODAL_ACTION_NAMES.INIT:
        return MaterialsModalReducers.doInit(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.DESTROY:
        return null;
      case MATERIALS_MODAL_ACTION_NAMES.SETUP_INIT:
        return MaterialsModalReducers.doActivateLoading(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SETUP_INIT_SET_PAGE:
        return MaterialsModalReducers.doSetPage(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SETUP_INIT_SUCCESS:
        return MaterialsModalReducers.doSetupInitSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SETUP_INIT_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.APPROVE_DRAFT:
        return MaterialsModalReducers.doActivateLoading(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.APPROVE_DRAFT_VALIDATE:
        return MaterialsModalReducers.doSetApprovalValidate(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.APPROVE_DRAFT_SUCCESS:
        return MaterialsModalReducers.doSetApprovalDone(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.APPROVE_DRAFT_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.REJECT_DRAFT:
        return MaterialsModalReducers.doActivateLoading(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.REJECT_DRAFT_SUCCESS:
        return MaterialsModalReducers.doSetApprovalDone(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.REJECT_DRAFT_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.ADDITIONAL_INFO_DRAFT:
        return MaterialsModalReducers.doActivateLoading(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.ADDITIONAL_INFO_DRAFT_SUCCESS:
        return MaterialsModalReducers.doSetApprovalDone(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.ADDITIONAL_INFO_DRAFT_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.PROCESS_ID_SET:
        return MaterialsModalReducers.doProcessIdSet(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT:
        return MaterialsModalReducers.doOpenEdit(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_SUCCESS:
        return MaterialsModalReducers.doOpenEditSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_DETAILS:
        return MaterialsModalReducers.doOpenDetails(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_DETAILS_SUCCESS:
        return MaterialsModalReducers.doOpenDetailsSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_DETAILS_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_PROCESS_DETAILS_VIEW:
        return MaterialsModalReducers.doOpenProcessDetailsView(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_PROCESS_DETAILS_VIEW_SUCCESS:
        return MaterialsModalReducers.doOpenProcessDetailsViewSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_PROCESS_DETAILS_VIEW_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_PROCESS_DETAILS_EDIT:
        return MaterialsModalReducers.doOpenProcessDetailsEdit(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_PROCESS_DETAILS_EDIT_SUCCESS:
        return MaterialsModalReducers.doOpenProcessDetailsEditSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_PROCESS_DETAILS_EDIT_FAILURE:
        return MaterialsModalReducers.doOpenProcessDetailsEditFFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_GR:
        return MaterialsModalReducers.doOpenEditGR(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_GR_SUCCESS:
        return MaterialsModalReducers.doOpenEditGRSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_GR_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_DELETE_GR:
        return MaterialsModalReducers.doOpenDeleteGR(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_DELETE_GR_SUCCESS:
        return MaterialsModalReducers.doOpenDeleteGRSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_DELETE_GR_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_DELETE_GR_APPROVAL:
        return MaterialsModalReducers.doOpenDeleteGRApproval(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_DELETE_GR_APPROVAL_SUCCESS:
        return MaterialsModalReducers.doOpenDeleteGRApprovalSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_DELETE_GR_APPROVAL_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_INFO_GET_SUCCESS:
        return MaterialsModalReducers.doAttachmentsInfoGetSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SAVE_MATERIAL_EDIT:
        return MaterialsModalReducers.doSaveMaterialEdit(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SAVE_MATERIAL_EDIT_SUCCESS:
        return MaterialsModalReducers.doSaveMaterialEditSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SAVE_MATERIAL_EDIT_FAILURE:
        return MaterialsModalReducers.doSaveMaterialEditFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.REQUEST_GR_DELETE:
        return MaterialsModalReducers.doGRDeleteRequest(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.REQUEST_GR_DELETE_SUCCESS:
        return MaterialsModalReducers.doGRDeleteRequestSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.REQUEST_GR_DELETE_FAILURE:
        return MaterialsModalReducers.doGRDeleteRequestFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.APPROVE_GR_DELETE:
        return MaterialsModalReducers.doActivateLoading(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.APPROVE_GR_DELETE_SUCCESS:
        return MaterialsModalReducers.doGRDeleteApprovalRequestSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.APPROVE_GR_DELETE_FAILURE:
        return MaterialsModalReducers.doGRDeleteApprovalRequestFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.REJECT_GR_DELETE:
        return MaterialsModalReducers.doActivateLoading(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.REJECT_GR_DELETE_SUCCESS:
        return MaterialsModalReducers.doGRDeleteApprovalRequestSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.REJECT_GR_DELETE_FAILURE:
        return MaterialsModalReducers.doGRDeleteApprovalRequestFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.UPDATE_ALTERNATIVE_UOM:
        return MaterialsModalReducers.doUpdateAlternativeUom(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.RELOAD_DOCUMENT:
        return MaterialsModalReducers.doReloadInitialData(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.RELOAD_INITIAL_DATA:
        return MaterialsModalReducers.doActivateLoading(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.RELOAD_INITIAL_DATA_SUCCESS:
        return MaterialsModalReducers.doReloadInitialDataSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.RELOAD_LOCAL_INITIAL_DATA_SUCCESS:
        return MaterialsModalReducers.doReloadLocalInitialDataSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.RELOAD_INITIAL_DATA_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.MATERIAL_IMAGE_UPLOAD:
        return MaterialsModalReducers.doActivateLoading(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.MATERIAL_IMAGE_UPLOAD_SUCCESS:
        return MaterialsModalReducers.doMaterialImageUploadSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.MATERIAL_IMAGE_UPLOAD_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.MATERIAL_IMAGE_CLEAN:
        return MaterialsModalReducers.doMaterialImageClean(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_UPLOAD:
        return MaterialsModalReducers.doActivateLoading(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_UPLOAD_SUCCESS:
        return MaterialsModalReducers.doAttachmentsUploadSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_UPLOAD_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_INSTANCE_UPLOAD:
                return MaterialsModalReducers.doAttachmentsInstanceUpload(state, action);
            case MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_INSTANCE_UPLOAD_SUCCESS:
                return MaterialsModalReducers.doAttachmentsInstanceUploadSuccess(state, action);
            case MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_INSTANCE_UPLOAD_FAILURE:
                return MaterialsModalReducers.doAttachmentsInstanceUploadFailure(state, action);
            case MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_CLEAN:
        return MaterialsModalReducers.doAttachmentsClean(state, action);
            case MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_INSTANCE_CLEAN:
                return MaterialsModalReducers.doAttachmentsInstanceClean(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_INFO_GET:
        return MaterialsModalReducers.doActivateLoading(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.ATTACHMENTS_INFO_GET_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.THUMBNAILS_SELECTED:
                return MaterialsModalReducers.doSelectedThumbnails(state, action);
            case MATERIALS_MODAL_ACTION_NAMES.SUGGEST_CATEGORIES:
        return MaterialsModalReducers.doSuggestCategories(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SUGGEST_CATEGORIES_SUCCESS:
        return MaterialsModalReducers.doSuggestCategoriesSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SUGGEST_CATEGORIES_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.TREE_CATEGORIES_SUCCESS:
        return MaterialsModalReducers.doTreeCategoriesSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.TREE_CATEGORIES_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.TREE_CATEGORIES_CLEAN:
        return MaterialsModalReducers.doTreeCategoriesClean(state, action);

      case MATERIALS_MODAL_ACTION_NAMES.TREE_CATEGORIES_LOADCHILDREN_SUCCESS:
        return MaterialsModalReducers.doTreeCategoriesLoadChildrenSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.TREE_CATEGORIES_LOADCHILDREN_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SELECT_CATEGORY:
        return MaterialsModalReducers.doSelectCategory(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SELECT_CATEGORY_CLEAN:
        return MaterialsModalReducers.doSelectCategoryClean(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.VALIDATE_MATERIAL_DATA:
        return MaterialsModalReducers.doValidateMaterialData(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.VALIDATE_MATERIAL_DATA_SUCCESS:
        return MaterialsModalReducers.doValidateMaterialDataSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.VALIDATE_MATERIAL_DATA_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.EMPTY_VALIDATE_MATERIAL_DATA:
        return MaterialsModalReducers.doEmptyValidateMaterialData(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.LOAD_DETAILS:
        return MaterialsModalReducers.doLoadDetails(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.LOAD_DETAILS_SUCCESS:
        return MaterialsModalReducers.doLoadDetailsSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.LOAD_DETAILS_FAILURE:
        return MaterialsModalReducers.doLoadDetailsFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.UNLOAD_DETAILS:
        return MaterialsModalReducers.doUnLoadDetails(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_APPROVAL:
        return MaterialsModalReducers.doOpenEditApproval(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_GR_APPROVAL:
        return MaterialsModalReducers.doOpenGrApproval(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_GR_APPROVAL_SUCCESS:
        return MaterialsModalReducers.doOpenEditGRSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_ADDITIONAL_INFO:
        return MaterialsModalReducers.doOpenAdditionalInfo(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_APPROVAL_SUCCESS:
        return MaterialsModalReducers.doOpenEditApprovalSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_ADDITIONAL_INFO_SUCCESS:
        return MaterialsModalReducers.ddoOpenAdditionalInfoSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_APPROVAL_FAILURE:
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_EDIT_ADDITIONAL_INFO_FAILURE:
        return MaterialsModalReducers.doOpenEditApprovalFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.CONFIRM_UPDATE_EDIT:
        return MaterialsModalReducers.doActivateLoading(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.CONFIRM_UPDATE_EDIT_SUCCESS:
        return MaterialsModalReducers.doSetApprovalDone(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.CONFIRM_UPDATE_EDIT_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.COMPLETENESS_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SEARCH_FOR_LINK:
        return MaterialsModalReducers.doSearchForLink(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SEARCH_FOR_LINK_SUCCESS:
        return MaterialsModalReducers.doSearchForLinkSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.SEARCH_FOR_LINK_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_SEARCH_FOR_LINK_DIALOG:
        return MaterialsModalReducers.doOpenSearchForLinkDialog(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.LINK_INSTANCES:
        return MaterialsModalReducers.doLinkInstances(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.UNLINK_INSTANCES:
        return MaterialsModalReducers.doUnlinkInstances(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.UNLINK_INSTANCES_SUCCESS:
        return MaterialsModalReducers.doUnlinkInstancesSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.LINK_INSTANCES_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.UNLINK_INSTANCES_FAILURE:
        return MaterialsModalReducers.doDefaultFailure(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.LINK_INSTANCES_CONFIRM_CREATION:
        return MaterialsModalReducers.doLinkInstancesConfirmCreation(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_GR_EDIT_APPROVAL:
        return MaterialsModalReducers.doOpenGrEditApproval(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.OPEN_GR_EDIT_APPROVAL_SUCCESS:
        return MaterialsModalReducers.doOpenGrEditApprovalSuccess(state, action);
      case MATERIALS_MODAL_ACTION_NAMES.INSTANCES_WITH_RELATIONSHIPS_SUCCESS:
                return MaterialsModalReducers.doInstancesWithRelationshipsSuccess(state, action);
            case MATERIALS_MODAL_ACTION_NAMES.INSTANCES_ATTACHMENTS_AND_IMAGE_SUCCESS:
                return MaterialsModalReducers.doInstancesAttachmentsAndImageSuccess(state, action);
            case MATERIALS_MODAL_ACTION_NAMES.INSTANCES_IMAGE:
                return MaterialsModalReducers.doInstancesAttachmentsAndImageSuccess(state, action);
            default:
                return ObjectsUtils.deepClone<MaterialsModalState>(state);
        }
    }

  public static doInit(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    return TAM_MATERIALS_MODAL_INITIAL_STATE;
  }

  public static doOpenEdit(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenEdit = ObjectsUtils.forceCast<MaterialsModalAction_OpenEdit>(
      action);
    newState.materialId = a.payload;
    newState.loading = true;
    return newState;
  }

  public static doOpenEditSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenEdit_Success = ObjectsUtils.forceCast<MaterialsModalAction_OpenEdit_Success>(
      action);
    newState.materialId = a.payload?.data?.materialActionsAuthorization?.materialId || null;
    newState.material = a.payload.data;
    newState.loading = false;
    newState.modalEditOpened = true;

    newState.plantSheet = initPlantSheet(newState.material);
    newState.plants = newState.material.plants;
    newState.completeness = newState.material.completeness;
    newState.categoriesSheet = newState.material.categoriesSheet;
    newState.description = newState.material.additionalMaterialInformation?.description;
    newState.imageUUID = a.payload.data.additionalMaterialInformation.imageId;
    newState.attachmentsUUID = a.payload.data.additionalMaterialInformation.attachmentsId;

    if (a.payload?.data?.categoriesSheet) {
      const _selectedCategories: SmartCreationSelectedCategories = {};
      Object.keys(a.payload?.data?.categoriesSheet).forEach(taxonomy => {
        _selectedCategories[taxonomy] = a.payload?.data?.categoriesSheet?.[taxonomy][0];
      });
      newState.selectedCategories = _selectedCategories;
    }

    return newState;
  }

  public static doOpenDetails(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenDetails = ObjectsUtils.forceCast<MaterialsModalAction_OpenDetails>(
      action);
    newState.materialId = a.payload.materialId;
    newState.loading = true;
    return newState;
  }

  public static doOpenDetailsSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenDetails_Success = ObjectsUtils.forceCast<MaterialsModalAction_OpenDetails_Success>(
      action);
    newState.material = a.payload.data;
    newState.loading = false;
    newState.modalEditOpened = true;


    newState.plantSheet = initPlantSheet(newState.material);
    newState.plants = newState.material.plants;
    newState.completeness = newState.material.completeness;
    newState.categoriesSheet = newState.material.categoriesSheet;
    newState.description = newState.material.additionalMaterialInformation?.description;

    newState.imageUUID = newState.material.additionalMaterialInformation.imageId;
    newState.attachmentsUUID = newState.material.additionalMaterialInformation.attachmentsId;

    if (a.payload?.data?.categoriesSheet) {
      const _selectedCategories: SmartCreationSelectedCategories = {};
      Object.keys(a.payload?.data?.categoriesSheet).forEach(taxonomy => {
        _selectedCategories[taxonomy] = a.payload?.data?.categoriesSheet?.[taxonomy][0];
      });
      newState.selectedCategories = _selectedCategories;
    }

    return newState;
  }

  public static doLoadDetails(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_LoadDetails = ObjectsUtils.forceCast<MaterialsModalAction_LoadDetails>(
      action);
    newState.loading = true;
    return newState;
  }

  public static doLoadDetailsSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_LoadDetails_Success = ObjectsUtils.forceCast<MaterialsModalAction_LoadDetails_Success>(
      action);
    newState.material = a.payload.data;
    newState.loading = false;
    newState.modalEditOpened = true;

    return newState;
  }

  public static doLoadDetailsFailure(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);

    newState.material = null;
    newState.loading = false;

    return newState;
  }

  public static doSetupInitSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Setup_Init_Success = ObjectsUtils.forceCast<MaterialsModalAction_Setup_Init_Success>(
      action);
    newState.setup = a.payload?.data;
    newState.loading = false;

    return newState;
  }

  public static doOpenEditApproval(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenEditApproval = ObjectsUtils.forceCast<MaterialsModalAction_OpenEditApproval>(
      action);
    newState.materialId = a.payload.materialId;
    newState.processId = a.payload.processId;
    newState.loading = true;
    return newState;
  }

  public static doOpenEditApprovalSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenEditApproval_Success = ObjectsUtils.forceCast<MaterialsModalAction_OpenEditApproval_Success>(
      action);
    newState.material = a.payload.data;
    newState.loading = false;
    newState.modalEditOpened = true;

    newState.plantSheet = initPlantSheet(newState.material);
    newState.plants = newState.material.plants;
    newState.completeness = newState.material.completeness;
    newState.categoriesSheet = newState.material.categoriesSheet;
    newState.description = newState.material.additionalMaterialInformation?.description;
    newState.imageUUID = newState.material.additionalMaterialInformation.imageId;
    newState.attachmentsUUID = newState.material.additionalMaterialInformation.attachmentsId;
    if (a.payload?.data?.categoriesSheet) {
      const _selectedCategories: SmartCreationSelectedCategories = {};
      Object.keys(a.payload?.data?.categoriesSheet).forEach(taxonomy => {
        _selectedCategories[taxonomy] = a.payload?.data?.categoriesSheet?.[taxonomy][0];
      });
      newState.selectedCategories = _selectedCategories;
    }
    return newState;
  }

  public static doOpenAdditionalInfo(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenAdditionalInfo = ObjectsUtils.forceCast<MaterialsModalAction_OpenAdditionalInfo>(
      action);
    newState.materialId = a.payload.materialId;
    newState.processId = a.payload.processId;
    newState.loading = true;
    return newState;
  }

  public static ddoOpenAdditionalInfoSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenAdditionalInfo_Success = ObjectsUtils.forceCast<MaterialsModalAction_OpenAdditionalInfo_Success>(
      action);
    newState.material = a.payload.data;
    newState.loading = false;
    newState.modalEditOpened = true;

    newState.plantSheet = initPlantSheet(newState.material);
    newState.plants = newState.material.plants;
    newState.completeness = newState.material.completeness;
    newState.categoriesSheet = newState.material.categoriesSheet;
    newState.description = newState.material.additionalMaterialInformation?.description;

    newState.imageUUID = newState.material.additionalMaterialInformation.imageId;
    newState.attachmentsUUID = newState.material.additionalMaterialInformation.attachmentsId;

    return newState;
  }

  public static doProcessIdSet(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Process_Id_Set = ObjectsUtils.forceCast<MaterialsModalAction_Process_Id_Set>(action);

    newState.processId = a.payload;

    return newState;
  }

  public static doOpenEditApprovalFailure(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);

    newState.material = null;
    newState.loading = false;
    newState.modalEditOpened = false;

    return newState;
  }

  public static doSetApprovalDone(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.approvalDone = true;
    newState.loading = false;
    newState.modalEditOpened = false;
    return newState;
  }

  public static doSetApprovalValidate(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.approverChanges = false;
    newState.validateMaterialResult = null;
    newState.attributeChanged = null;
    return newState;
  }

  public static doUnLoadDetails(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);

    newState.material = null;
    newState.loading = false;

    return newState;
  }

  public static doOpenEditGR(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenDetails = ObjectsUtils.forceCast<MaterialsModalAction_OpenDetails>(
      action);
    newState.materialId = a.payload;
    newState.loading = true;
    return newState;
  }

  public static doOpenEditGRSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenDetails_Success = ObjectsUtils.forceCast<MaterialsModalAction_OpenDetails_Success>(
      action);
    newState.material = a.payload.data;
    newState.loading = false;
    newState.modalEditOpened = true;

    newState.plantSheet = initPlantSheet(newState.material);
    newState.plants = newState.material.plants;
    newState.completeness = newState.material.completeness;
    newState.categoriesSheet = newState.material.categoriesSheet;
    newState.description = newState.material.additionalMaterialInformation?.description;

    newState.imageUUID = newState.material.additionalMaterialInformation.imageId;
    newState.attachmentsUUID = newState.material.additionalMaterialInformation.attachmentsId;

    return newState;
  }

  public static doOpenDeleteGR(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenDeleteGR = ObjectsUtils.forceCast<MaterialsModalAction_OpenDeleteGR>(
      action);
    newState.materialId = a.payload;

    newState.loading = true;
    return newState;
  }

  public static doOpenDeleteGRSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenDeleteGR_Success = ObjectsUtils.forceCast<MaterialsModalAction_OpenDeleteGR_Success>(
      action);
    newState.material = a.payload.data;
    newState.loading = false;
    newState.modalDeleteOpened = true;

    newState.plantSheet = initPlantSheet(newState.material);
    newState.plants = newState.material.plants;
    newState.completeness = newState.material.completeness;
    newState.categoriesSheet = newState.material.categoriesSheet;
    newState.description = newState.material.additionalMaterialInformation?.description;

    newState.imageUUID = newState.material.additionalMaterialInformation.imageId;
    newState.attachmentsUUID = newState.material.additionalMaterialInformation.attachmentsId;

    return newState;
  }


  public static doOpenDeleteGRApproval(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenDeleteGRApproval = ObjectsUtils.forceCast<MaterialsModalAction_OpenDeleteGRApproval>(
      action);
    newState.materialId = a.payload.materialId;
    newState.processId = a.payload.processId;

    newState.loading = true;
    return newState;
  }

  public static doOpenDeleteGRApprovalSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenDeleteGRApproval_Success = ObjectsUtils.forceCast<MaterialsModalAction_OpenDeleteGRApproval_Success>(
      action);
    newState.material = a.payload.data;
    newState.loading = false;
    newState.modalDeleteOpened = true;

    newState.plantSheet = initPlantSheet(newState.material);
    newState.plants = newState.material.plants;
    newState.completeness = newState.material.completeness;
    newState.categoriesSheet = newState.material.categoriesSheet;

    return newState;
  }

  public static doAttachmentsInfoGetSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_AttachmentsInfo_Get_Success =
      ObjectsUtils.forceCast<MaterialsModalAction_AttachmentsInfo_Get_Success>(action);

    newState.attachmentsInfo = a.payload.attachmentsInfo;
    newState.loading = false;

    return newState;
  }

  public static doOpenProcessDetailsView(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenProcessDetailsView = ObjectsUtils.forceCast<MaterialsModalAction_OpenProcessDetailsView>(
      action);
    newState.materialId = a.payload;
    newState.loading = true;
    return newState;
  }

  public static doOpenProcessDetailsViewSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenProcessDetailsView_Success = ObjectsUtils.forceCast<MaterialsModalAction_OpenProcessDetailsView_Success>(
      action);
    newState.material = a.payload.data;
    newState.loading = false;
    newState.modalEditOpened = true;
    newState.plantSheet = initPlantSheet(newState.material);
    newState.plants = newState.material.plants;
    newState.completeness = newState.material.completeness;
    newState.categoriesSheet = newState.material.categoriesSheet;
    newState.description = newState.material.additionalMaterialInformation?.description;
    newState.imageUUID = newState.material.additionalMaterialInformation.imageId;
    newState.attachmentsUUID = newState.material.additionalMaterialInformation.attachmentsId;
    return newState;
  }

  public static doOpenProcessDetailsEdit(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenProcessDetailsEdit = ObjectsUtils.forceCast<MaterialsModalAction_OpenProcessDetailsEdit>(
      action);
    newState.materialId = a.payload.materialId;
    newState.processId = a.payload.processId;
    newState.loading = true;
    return newState;
  }

  public static doOpenProcessDetailsEditSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenProcessDetailsEdit_Success = ObjectsUtils.forceCast<MaterialsModalAction_OpenProcessDetailsEdit_Success>(
      action);
    newState.material = a.payload.data;
    newState.loading = false;
    newState.modalEditOpened = true;
    newState.plantSheet = initPlantSheet(newState.material);
    newState.plants = newState.material.plants;
    newState.completeness = newState.material.completeness;
    newState.categoriesSheet = newState.material.categoriesSheet;
    newState.description = newState.material.additionalMaterialInformation?.description;
    newState.imageUUID = newState.material.additionalMaterialInformation.imageId;
    newState.attachmentsUUID = newState.material.additionalMaterialInformation.attachmentsId;

    if (a.payload?.data?.categoriesSheet) {
      const _selectedCategories: SmartCreationSelectedCategories = {};
      Object.keys(a.payload?.data?.categoriesSheet).forEach(taxonomy => {
        _selectedCategories[taxonomy] = a.payload?.data?.categoriesSheet?.[taxonomy][0];
      });
      newState.selectedCategories = _selectedCategories;
    }

    return newState;
  }

  public static doOpenProcessDetailsEditFFailure(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);

    newState.material = null;
    newState.loading = false;
    newState.modalEditOpened = false;

    return newState;
  }

  public static doSaveMaterialEdit(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenEdit = ObjectsUtils.forceCast<MaterialsModalAction_OpenEdit>(
      action);
    newState.loading = true;
    return newState;
  }

  public static doSaveMaterialEditSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_SaveEdit_Success = ObjectsUtils.forceCast<MaterialsModalAction_SaveEdit_Success>(
      action);

    newState.loading = false;
    newState.modalEditOpened = false;
    newState.saveStatus = 'COMPLETED';
    newState.process = a.payload.data;
    return newState;
  }

  public static doSaveMaterialEditFailure(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);

    newState.loading = false;
    newState.modalEditOpened = true;
    newState.validateMaterialResult = null;
    newState.saveStatus = 'FAILURE';

    return newState;
  }

  public static doGRDeleteRequest(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.loading = true;
    return newState;
  }

  public static doGRDeleteRequestSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_RequestGR_Delete_Success = ObjectsUtils.forceCast<MaterialsModalAction_RequestGR_Delete_Success>(
      action);

    newState.loading = false;
    newState.modalDeleteOpened = false;
    newState.saveStatus = 'COMPLETED';
    newState.process = a.payload.data;
    return newState;
  }

  public static doGRDeleteRequestFailure(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);

    newState.loading = false;
    newState.modalDeleteOpened = true;
    newState.saveStatus = 'FAILURE';

    return newState;
  }

  public static doGRDeleteApprovalRequestSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);

    newState.loading = false;
    newState.modalDeleteOpened = false;
    newState.saveStatus = 'COMPLETED';
    return newState;
  }

  public static doGRDeleteApprovalRequestFailure(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);

    newState.loading = false;
    newState.modalDeleteOpened = true;
    newState.saveStatus = 'FAILURE';

    return newState;
  }

  public static doUpdateAlternativeUom(state: MaterialsModalState, action: MaterialsModalActionTypes) {


    const payload: DynamicFormEventPayload = action?.payload as DynamicFormEventPayload;

    return produce(state, (draftState) => {
      draftState.material.alternativeUomList = payload?.auom;
      draftState?.material?.materialSheets[payload?.componentParams.sheetIndex].requests.forEach(f => {
        if (f.id === payload?.componentParams?.id) {
          f.value = JSON.stringify(payload?.auom);
        }
      });
    });
  }

  public static doReloadInitialData(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Reload_Initial_Data = ObjectsUtils.forceCast<MaterialsModalAction_Reload_Initial_Data>(
      action);

    const currentControl: any = a.payload?.control;

    if (currentControl) {
      currentControl.dropdownValues = [];
    }

    let searchForControl = true;
    if (currentControl && newState.material && newState.material.materialSheets && newState.material?.materialSheets.length > 0) {
      for (const [index, element] of newState.material.materialSheets.entries()) {
        if (searchForControl) {
          const currentRequestItemIndex = element.requests.findIndex(x => x.id === currentControl.id);
          if (currentRequestItemIndex >= 0) {
            const initialData = ObjectsUtils.deepClone<SmartCreationMaterialDetail>(newState.material);
            initialData.materialSheets[index].requests[currentRequestItemIndex].value =
              currentControl.value;
            initialData.materialSheets[index].requests[currentRequestItemIndex].unitsOfMeasureSelected =
              currentControl.unitsOfMeasureSelected;

            searchForControl = false;
            newState.material = initialData;
          }
        }

        const relatedControlItemIndex = element.requests.findIndex(x => x.relatedAttribute === currentControl.id);
        if (relatedControlItemIndex >= 0) {
          switch (newState.material.materialSheets[index].requests[relatedControlItemIndex].type) {
            case FieldType.alternativeUnitsOfMeasure: {
              newState.material.additionalMaterialInformation.alternativeUnitsOfMeasure = [];
              break;
            }
            default:
              break;
          }
        }

      }
    }

    newState.attributeChanged = currentControl;
    newState.approverChanges = TamApePageUtils.isApproval(newState.page) && ObjectsUtils.isNotNoU(currentControl);

    return newState;
  }

  public static doReloadInitialDataSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Reload_Initial_Data_Success =
      ObjectsUtils.forceCast<MaterialsModalAction_Reload_Initial_Data_Success>(action);
    newState.material.completeness = a.payload.data.completeness;
    newState.material.materialSheets = a.payload.data.materialSheets ?? newState.material.materialSheets;
    newState.material.categoriesSheet = a.payload.data.categoriesSheet ?? newState.material.categoriesSheet;

    if (a.payload?.data?.categoriesSheet) {
      const _selectedCategories: SmartCreationSelectedCategories = {};
      Object.keys(a.payload?.data?.categoriesSheet).forEach(taxonomy => {
        _selectedCategories[taxonomy] = a.payload?.data?.categoriesSheet?.[taxonomy][0];
      });
      newState.selectedCategories = _selectedCategories;
    }

    // newState.material.additionalMaterialInformation.shortDescription = a.payload.data.additionalMaterialInformation?.shortDescription;
    // newState.material.additionalMaterialInformation.longDescription = a.payload.data.additionalMaterialInformation?.longDescription;
    newState.material.additionalMaterialInformation.description =
      a.payload.data.additionalMaterialInformation.description ?? newState.material.additionalMaterialInformation?.description;

    newState.material.additionalMaterialInformation.alternativeUnitsOfMeasure =
      a.payload.data.additionalMaterialInformation?.alternativeUnitsOfMeasure ?? newState.alternativeUomList;
    newState.alternativeUomList =
      a.payload.data.additionalMaterialInformation?.alternativeUnitsOfMeasure ?? newState.alternativeUomList;
    newState.attributeChanged = null;
    newState.loading = false;
    return newState;
  }

  public static doReloadLocalInitialDataSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Reload_Local_Initial_Data_Success =
      ObjectsUtils.forceCast<MaterialsModalAction_Reload_Local_Initial_Data_Success>(action);
    if (newState.material) {
      newState.material.materialSheets = a.payload.materialSheets;
      newState.material.categoriesSheet = a.payload.categoriesSheet ?? newState.material.categoriesSheet;
    }
    newState.attributeChanged = null;
    newState.loading = false;
    newState.completeness = a.payload.completeness;
    return newState;
  }

  public static doActivateLoading(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.loading = true;
    return newState;
  }

  public static doDefaultFailure(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.loading = false;
    newState.materialResultLoading = false;
    return newState;
  }

  public static doMaterialImageUploadSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Material_Image_Upload_Success =
      ObjectsUtils.forceCast<MaterialsModalAction_Material_Image_Upload_Success>(
        action);

    newState.imageUUID = a.payload;
    newState.loading = false;

    return newState;
  }

  public static doMaterialImageClean(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.imageUUID = null;
    return newState;
  }

  public static doAttachmentsUploadSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Attachments_Upload_Success = ObjectsUtils.forceCast<MaterialsModalAction_Attachments_Upload_Success>(
      action);

    if (!newState.attachmentsUUID) {
      newState.attachmentsUUID = [];
    }
    newState.attachmentsUUID.push(a.payload);
    newState.loading = false;

    return newState;
    }

    public static doAttachmentsInstanceUpload(state: MaterialsModalState, action: MaterialsModalActionTypes) {
        const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
        const a: MaterialsModalAction_Attachments_Instance_Upload = ObjectsUtils.forceCast<MaterialsModalAction_Attachments_Instance_Upload>(
            action);

        newState.attachmentInstanceToUploadDetails = { instance: action.payload.instance, fileName: a.payload.file?.name };
        newState.loading = true;

        return newState;
    }

    public static doAttachmentsClean(state: MaterialsModalState, action: MaterialsModalActionTypes) {
        const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
        const a: MaterialsModalAction_Attachments_Clean = ObjectsUtils.forceCast<MaterialsModalAction_Attachments_Clean>(
            action);

        newState.attachmentsInfo = [];
        if (newState.attachmentsUUID && newState.attachmentsUUID.length > 0) {
      newState.attachmentsUUID = newState.attachmentsUUID.filter(item => item !== a.payload);
    }

        return newState;
    }

    public static doAttachmentsInstanceUploadSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
        const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
        const a: MaterialsModalAction_Attachments_Instance_Upload_Success = ObjectsUtils.forceCast<MaterialsModalAction_Attachments_Instance_Upload_Success>(action);

        if (!newState.attachmentInstanceUpdate) {
            newState.attachmentInstanceUpdate = [];
        }

        if (newState.attachmentInstanceToUploadDetails?.instance) {
            let updateReq = newState.attachmentInstanceUpdate.find(u => u.instance === newState.attachmentInstanceToUploadDetails.instance);
            if (!updateReq) {
                updateReq = { instance: newState.attachmentInstanceToUploadDetails.instance, toAdd: [], toRemove: [] };
                newState.attachmentInstanceUpdate.push(updateReq);
            }
            if (!updateReq.toAdd) { updateReq.toAdd = []; }
            if (!updateReq.toAdd.includes(a.payload as UUID)) {
                updateReq.toAdd.push(a.payload as UUID);
            }

            if (newState.attachmentsInstances) {
                const instance = newState.attachmentsInstances
                    .find(i => i.client === newState.attachmentInstanceToUploadDetails.instance);
                if (instance) {
                    if (!instance.attachments) {
                        instance.attachments = [];
                    }
                    if (!instance.attachments.some(att => att.attachmentId === a.payload)) {
                        instance.attachments.push({
                            attachmentId: a.payload,
                            fileName: newState.attachmentInstanceToUploadDetails.fileName
                        } as AttachmentContentInfo);
                    }
                }
            }
        }

        newState.attachmentInstanceToUploadDetails = null;
        newState.loading = false;
        return newState;
    }

    public static doAttachmentsInstanceUploadFailure(state: MaterialsModalState, action: MaterialsModalActionTypes) {
        const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);

        newState.attachmentInstanceToUploadDetails = null;
        newState.loading = false;
        return newState;
    }

    public static doAttachmentsInstanceClean(state: MaterialsModalState, action: MaterialsModalActionTypes) {
        const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
        const a: MaterialsModalAction_Attachments_Instance_Clean = ObjectsUtils.forceCast<MaterialsModalAction_Attachments_Instance_Clean>(action);

        if (!newState.attachmentInstanceUpdate) {
            newState.attachmentInstanceUpdate = [];
        }

        const instance = a.payload?.instance;
        const attachmentUUID = a.payload?.attachmentUUID;

        if (instance && attachmentUUID) {
            let updateReq = newState.attachmentInstanceUpdate.find(u => u.instance === instance);
            if (!updateReq) {
                updateReq = { instance, toAdd: [], toRemove: [] };
                newState.attachmentInstanceUpdate.push(updateReq);
            }
            if (!updateReq.toRemove) { updateReq.toRemove = []; }
            if (!updateReq.toRemove.includes(attachmentUUID)) {
                updateReq.toRemove.push(attachmentUUID);
            }

            if (newState.attachmentsInstances) {
                const inst = newState.attachmentsInstances.find(i => i.client === instance);
                if (inst?.attachments) {
                    inst.attachments = inst.attachments.filter(att => att.attachmentId !== attachmentUUID);
                }
            }
        }

        newState.loading = false;
        return newState;
    }

    public static doSelectedThumbnails(state: MaterialsModalState, action: MaterialsModalActionTypes) {
        const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
        const a: MaterialsModalAction_Thumbnails_Selected = ObjectsUtils.forceCast<MaterialsModalAction_Thumbnails_Selected>(
            action);
        newState.imagesInstances?.map(el => el.selected = false);
        const image = newState.imagesInstances?.find(el => el.thumbnail.attachmentId === a.payload);
        if (image) {
          image.selected = !image.selected;
        }
        newState.imageFromInstance = a.payload;
        newState.loading = false;
        return newState;
    }

  public static doSuggestCategories(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Suggest_Categories = ObjectsUtils.forceCast<MaterialsModalAction_Suggest_Categories>(
      action);
    newState.loading = true;

    return newState;
  }

  public static doSuggestCategoriesSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Suggest_Categories_Success = ObjectsUtils.forceCast<MaterialsModalAction_Suggest_Categories_Success>(
      action);
    newState.originalSuggestedCategories = a.payload?.data;
    newState.loading = false;

    return newState;
  }

  public static doTreeCategoriesSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Tree_Categories_Success = ObjectsUtils.forceCast<MaterialsModalAction_Tree_Categories_Success>(
      action);
    if (a.payload?.data) {
      if (ObjectsUtils.forceCast<any>(a.payload?.data)?.open !== true) {
        a.payload.data.selectable = false;
      }
      newState.treeCategories = [a.payload?.data];
    } else {
      newState.treeCategories = null;
    }
    newState.loading = false;

    return newState;
  }

  public static doTreeCategoriesClean(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.treeCategories = null;

    return newState;
  }

  public static doSelectCategory(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Select_Category = ObjectsUtils.forceCast<MaterialsModalAction_Select_Category>(
      action);

    newState.selectedCategories = state.selectedCategories ? ObjectsUtils.deepClone(state.selectedCategories) : null;

    if (newState.selectedCategories) {
      for (const key of Object.keys(a.payload)) {
        newState.selectedCategories[key] = a.payload[key];
      }
    } else {
      newState.selectedCategories = a.payload;
    }

    return newState;
  }

  public static doSelectCategoryClean(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Select_Category_Clean = ObjectsUtils.forceCast<MaterialsModalAction_Select_Category_Clean>(
      action);

    newState.selectedCategories = state.selectedCategories;

    if (newState.selectedCategories) {
      delete newState.selectedCategories[a.payload];
    }

    return newState;
  }

  public static doValidateMaterialData(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Reload_Initial_Data = ObjectsUtils.forceCast<MaterialsModalAction_Reload_Initial_Data>(
      action);

    newState.loading = true;
    newState.validateMaterialResult = null;
    newState.attributeChanged = null;

    return newState;
  }

  public static doValidateMaterialDataSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Reload_Initial_Data_Success =
      ObjectsUtils.forceCast<MaterialsModalAction_Reload_Initial_Data_Success>(action);

    newState.validateMaterialResult = a.payload.data;
    newState.loading = false;
    newState.attributeChanged = null;

    return newState;
  }

  public static doEmptyValidateMaterialData(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Reload_Initial_Data = ObjectsUtils.forceCast<MaterialsModalAction_Reload_Initial_Data>(
      action);

    newState.validateMaterialResult = null;

    return newState;
  }

  public static doTreeCategoriesLoadChildrenSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const payload: CategoryTreeLoadChildrenActionPayload = action?.payload as CategoryTreeLoadChildrenActionPayload;

    return produce(state, (draftState) => {
      ObjectsUtils.updateCategorytreeNode(draftState.treeCategories, payload?.response?.data, payload?.parents);
    });
  }

  private static doSetPage(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Setup_Init_SetPage = ObjectsUtils.forceCast<MaterialsModalAction_Setup_Init_SetPage>(
      action);

    newState.page = a.payload;

    return newState;
  }

  public static doSearchForLink(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_SearchForLink_Success =
      ObjectsUtils.forceCast<MaterialsModalAction_SearchForLink_Success>(action);
    newState.materialResultLoading = true;
    return newState;
  }

  public static doSearchForLinkSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_SearchForLink_Success =
      ObjectsUtils.forceCast<MaterialsModalAction_SearchForLink_Success>(action);
    newState.materialResult = a.payload?.data;
    newState.materialResultLoading = false;
    return newState;
  }

  public static doOpenSearchForLinkDialog(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.materialResult = null;
    newState.linkInstancesDone = false;
    newState.loading = false;
    return newState;
  }

  public static doLinkInstancesConfirmCreation(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.linkInstancesDone = true;
    newState.loading = false;
    return newState;
  }

  public static doLinkInstances(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.loading = true;
    newState.linkInstancesDone = false;
    return newState;
  }

  public static doUnlinkInstances(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.unlinkInstancesDone = false;
    newState.loading = true;
    return newState;
  }

  public static doUnlinkInstancesSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    newState.unlinkInstancesDone = true;
    newState.loading = false;
    return newState;
  }

  public static doOpenGrEditApproval(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenGrEditApproval = ObjectsUtils.forceCast<MaterialsModalAction_OpenGrEditApproval>(
      action);
    newState.materialId = a.payload.materialId;
    newState.processId = a.payload.processId;
    newState.loading = true;
    return newState;
  }

  public static doOpenGrEditApprovalSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenGrEditApproval_Success = ObjectsUtils.forceCast<MaterialsModalAction_OpenGrEditApproval_Success>(
      action);
    newState.material = a.payload.data;
    newState.loading = false;
    return newState;
  }

  public static doOpenGrApproval(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenGrApproval = ObjectsUtils.forceCast<MaterialsModalAction_OpenGrApproval>(
      action);
    newState.materialId = a.payload.materialId;
    newState.processId = a.payload.processId;
    newState.loading = true;
    return newState;
  }

  public static doOpenGrApprovalSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_OpenGrApproval_Success = ObjectsUtils.forceCast<MaterialsModalAction_OpenGrApproval_Success>(
      action);
    newState.material = a.payload.data;
    newState.loading = false;
    return newState;
  }

  public static doInstancesWithRelationshipsSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Instances_With_Relationships_Success = ObjectsUtils.forceCast<MaterialsModalAction_Instances_With_Relationships_Success>(
      action);
    newState.instancesWithRelationships = a.payload.data;
    return newState;
  }

  public static doInstancesAttachmentsAndImageSuccess(state: MaterialsModalState, action: MaterialsModalActionTypes) {
    const newState: MaterialsModalState = ObjectsUtils.deepClone<MaterialsModalState>(state);
    const a: MaterialsModalAction_Instances_Attachments_And_Image_Success = ObjectsUtils.forceCast<MaterialsModalAction_Instances_Attachments_And_Image_Success>(
      action);
    newState.attachmentsInstances = a.payload.data;
    this.initImagesInstances(newState);
    return newState;
  }

  private static initImagesInstances(state: MaterialsModalState): MaterialsModalState {
    state.imagesInstances = state.attachmentsInstances?.filter(attachment => attachment.thumbnail !== null);
    if (state.imagesInstances && state.imagesInstances.length === 1) {
      state.imagesInstances[0].selected = true;
      state.imagesInstances[0].disabled = true;
      state.imageFromInstance = state.imagesInstances[0].thumbnail.attachmentId;
    } else {
      state.imagesInstances?.forEach(image => {
        image.selected = false;
        image.disabled = false;
      });
    }
    return state;
  }

}
