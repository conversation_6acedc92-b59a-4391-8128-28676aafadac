import {ChangeDetectionStrategy, Component, input, EventEmitter, Input, Signal, Output} from '@angular/core';

import {DYNAMIC_TABLE_LABELS} from '../../materials-modal/commons/materials-modal.constants';
import {Instance} from '../../smart-creation/models/smart-creation-validation.types';
import {MaterialUpdateService} from '../../smart-creation/service/material-update.service';

@Component({
  selector: 'instances-table',
  template: `
      @if(this.instances()?.length === 0){
        <p-messages *ngIf="this.instances()?.length === 0" severity="info">
            <ng-template pTemplate>
                {{ noInstanceDataLabel | translate }}
            </ng-template>
        </p-messages>
      } @else {
        <div class="mb-3 flex gap-2">
          <button pButton type="button" icon="{{expanded ? 'pi pi-chevron-up' : 'pi pi-chevron-down'}}"
                  severity="primary"
                  [text]="true"
                  label="{{ (expanded?'layout.item-details.instances.collapse-all':'layout.item-details.instances.expand-all') | translate }}"
                  (click)="collapseExpandedRows()"
                  [disabled]="!hasExpandableRows()"></button>
        </div>
        <p-table
          *ngIf="this.instances()?.length > 0"
          [value]="this.instances()"
          [(selection)]="selectedItems"
          [(expandedRowKeys)]="expandedRowKeys"
          dataKey="materialId"
          [tableStyle]="{'min-width': '50rem'}"
          (onRowSelect)="onRowSelect($event)"
          (onRowUnselect)="onRowUnselect($event)"
          >
          <ng-template pTemplate="header">
            <tr>
              <th></th>
              <th *ngIf="canUnlink"></th>
              <th>{{ 'layout.item-details.instances.client' | translate }} / {{ 'layout.item-details.instances.code' | translate }}</th>
              <th>{{ 'layout.item-details.instances.description' | translate }}</th>
              <th>{{ 'layout.item-details.instances.countries.title' | translate }}</th>
              <th>{{ 'layout.item-details.instances.plants.title' | translate }}</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-rowData let-expanded="expanded">
            <tr>
              <td class="w-5rem">
                <button
                  [pRowToggler]="rowData"
                  class="p-button-text p-button-rounded p-button-plain"
                  type="button" pButton pRipple
                  [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
                  [disabled]="!hasRelationships(rowData)">
                </button>
              </td>
              <td *ngIf="canUnlink">
                <p-tableCheckbox [value]="rowData"></p-tableCheckbox>
              </td>
              <td>
                <p-button
                  [link]="true"
                  [text]="true"
                  [label]="rowData.materialKey.client + ' / ' + rowData.materialKey.materialCode"
                  (click)="loadDetailsModal(rowData.materialId)">
                </p-button>
              </td>
              <td>
                <span style="font-weight: bold">{{ rowData.description }}</span>
              </td>
              <td>{{ rowData.countries | smartJoin: ', ':3:'layout.item-details.instances.countries' }}</td>
              <td>{{ rowData.plants | smartJoin: ', ':3:'layout.item-details.instances.plants' }}</td>
            </tr>
          </ng-template>
          <ng-template pTemplate="rowexpansion" let-relationshipData>
            <tr>
              <td>
                <span style="font-weight: bold">{{ 'layout.item-details.instances.relationships' | translate }}</span>
              </td>
              <td>
                <button
                  type="button" pButton pRipple
                  class="p-button-text p-button-rounded p-button-plain"
                  [icon]="nestedExpandedRow ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
                  (click)="expandNestedRows(relationshipData.relationships)"
                  [disabled]="!hasNestedExpandableRows(relationshipData.relationships)">
                </button>
              </td>
              <td colspan="3">
                @if(relationshipData?.relationships.length === 0) {
                  <p-messages *ngIf="relationshipData.relationships.length === 0" severity="info">
                    <ng-template pTemplate>
                        {{ noRelationshipDataLabel | translate }}
                    </ng-template>
                  </p-messages>
                } @else {
                  <p-table
                    [value]="relationshipData?.relationships"
                    dataKey="relationshipId"
                    [(expandedRowKeys)]="nestedExpandedRowKeys">

                    <ng-template pTemplate="header">
                      <tr>
                        <th>{{ 'layout.item-details.instances.relationshipType' | translate }}</th>
                        <th>{{ 'layout.item-details.instances.code' | translate }}</th>
                        <th>{{ 'layout.item-details.instances.description' | translate }}</th>
                        <th>{{ 'layout.item-details.instances.role' | translate }}</th>
                      </tr>
                    </ng-template>

                    <ng-template pTemplate="body" let-relationship let-expanded="expanded">
                      <tr>
                        <td [attr.rowspan]="expanded ? relationship.materialsRelationships.length : 1">{{ relationship.type }}</td>
                        <td>
                          <span style="font-weight: bold">{{getInstanceClientAndMaterialCode(relationship.materialsRelationships)}}</span>
                        </td>
                        <td>{{getInstanceDescription(relationship.materialsRelationships)}}</td>
                        <td>{{getInstanceRole(relationship.materialsRelationships)}}</td>
                      </tr>
                    </ng-template>

                    <ng-template pTemplate="rowexpansion" let-relationshipDetails>
                      @for(materialRelationship of getFilteredMaterialsRelationships(relationshipDetails.materialsRelationships); track materialRelationship) {
                        <tr>
                          <td>
                            {{ materialRelationship.materialKey.client }}/{{ materialRelationship.materialKey.materialCode }}
                          </td>
                          <td>
                            {{ materialRelationship.description }}
                          </td>
                          <td>
                            {{ materialRelationship.role }}
                          </td>
                        </tr>
                      }
                    </ng-template>
                  </p-table>
                }
              </td>
            </tr>
          </ng-template>
        </p-table>
      }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})


export class DynamicTableGrInstanceComponent {

  @Input() canUnlink?: boolean;
  @Output() unlinkInstance = new EventEmitter<any>();
  @Output() onSelectItem = new EventEmitter<Instance>();
  @Output() onUnselectItem = new EventEmitter<any>();

  instances: Signal<Instance[]> = input.required<Instance[]>();

  selectedItems: Array<Instance> = [];
  noInstanceDataLabel: string = DYNAMIC_TABLE_LABELS.ITEM_INSTANCE_LABEL + '.no-data';
  noRelationshipDataLabel: string = DYNAMIC_TABLE_LABELS.ITEM_RELATIONS_LABEL + '.no-data';
  expandedRowKeys: { [key: string]: boolean } = {};
  nestedExpandedRowKeys: { [key: string]: boolean } = {};
  nestedExpandedRow = false;
  expanded: boolean;

  constructor(private mService: MaterialUpdateService) {}

  loadDetailsModal(materialId: string) {
    this.mService.loadDetailsModal(materialId, false);
  }

  onRowSelect(event) {
    this.onSelectItem.emit(event.data);
  }

  onRowUnselect(event) {
    this.onUnselectItem.emit(event.data);
  }

  handleUnlinkInstances(material) {
    this.unlinkInstance.emit({material});
  }

  getInstanceClientAndMaterialCode(relationship: any[]) {
    const instance = this.findInstance(relationship);
    return instance.materialKey.client + "/" + instance.materialKey.materialCode;
  }

  getInstanceDescription(relationship: any[]) {
    return this.findInstance(relationship).description;
  }

  getInstanceRole(relationship: any[]) {
    return this.findInstance(relationship).role;
  }

  findInstance(relationship: any[]) {
    return relationship.find(rel => rel.isInstance === true);
  }

  getFilteredMaterialsRelationships(materialsRelationships: any[]) {
    return materialsRelationships.filter(matRel => matRel.isInstance === false);
  }

  expandNestedRows(relationships: any[]): void {
    const allRelationshipIds = relationships.map(relationship => relationship.relationshipId);

    const newState = !this.nestedExpandedRow;
    this.nestedExpandedRowKeys = allRelationshipIds.reduce((acc, id) => {
      acc[id] = newState;
      return acc;
    }, {});

    this.nestedExpandedRow = newState;
  }

  expandAll(): void {
    const instances = this.instances();
    const allInstanceIds = instances?.map(instance => instance.materialId) ?? [];
    const allRelationshipIds = this.getAllRelationshipIds();

    this.expandedRowKeys = allInstanceIds.reduce((acc, id) => {
      acc[id] = true;
      return acc;
    }, {});

    this.nestedExpandedRowKeys = allRelationshipIds.reduce((acc, id) => {
      acc[id] = true;
      return acc;
    }, {});

    this.nestedExpandedRow = true;
  }

  collapseExpandedRows(): void {
    if (this.expanded) {
      this.collapseAll();
      this.expanded = false;
    } else {
      this.expandAll();
      this.expanded = true;
    }
  }

  collapseAll(): void {
    this.expandedRowKeys = {};
    this.nestedExpandedRowKeys = {};
    this.nestedExpandedRow = false;
  }

  getAllRelationshipIds(): string[] {
    return this.instances()?.reduce((acc, instance) => {
      const relationshipIds = instance.relationships?.map(rel => rel.relationshipId);
      if (relationshipIds) {
        acc.push(...relationshipIds);
      }
      return acc;
    }, []);
  }

  resetTable() {
    this.selectedItems = [];
  }

  hasExpandableRows(): boolean {
    return (this.instances()?.some(instance => this.hasRelationships(instance))) ?? false;
  }

  hasRelationships(instance: Instance): boolean {
    return Array.isArray(instance.relationships) && instance.relationships.length > 0;
  }

  hasNestedExpandableRows(relationships: any[]): boolean {
    return Array.isArray(relationships) && relationships.some(rel => Array.isArray(rel.materialsRelationships) && rel.materialsRelationships.length > 0);
  }

}
