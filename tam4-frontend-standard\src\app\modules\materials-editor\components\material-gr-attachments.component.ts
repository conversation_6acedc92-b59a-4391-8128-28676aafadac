import { Component, EventEmitter, input, Input, Output, ViewChild} from '@angular/core';
import {AttachmentInfo, AttachmentInstanceDetails, UUID} from '@creactives/models';
import { ViewModeEnum } from '../models/material-editor.types';
import { FileUpload } from 'primeng/fileupload';
import {FileSelectEvent, FileUploadHandlerEvent} from 'primeng/fileupload/fileupload.interface';
import {EventUtils} from '../../../utils';
import {AttachmentInstanceCleanRequest, AttachmentInstanceUploadRequest} from '../../materials-modal/models/modals-modal.types';

@Component({
    selector: 'div[scMaterialGRAttachments]',
    template: `
        <div class="flex">
            <div class="col-12 p-0 m-0">
                <material-attachments
                  [attachments]="grAttachments"
                  [viewMode]="viewMode"
                  (attachmentUpload)="handleAttachmentUpload($event)"
                  (attachmentRemove)="handleAttachmentRemove($event)"
                  (attachmentDownload)="handleAttachmentDownload($event)"
                  [uploadBtnPosition]="'TOP'"
                  [disabled]="disabled"
                >
                </material-attachments>
                @if(instances?.length > 0) {
                  <p-table [value]="instances">
                    <ng-template pTemplate="body" let-instance>
                      <tr>
                        <td style="width: 60%; vertical-align: top;">
                          <strong>{{ instance.client }}/{{ instance.materialCode }}</strong>
                        </td>
                        <td style="width: 40%; vertical-align: top;">
                          @if(!disabled && !instance.openText && this.viewMode === ViewModeEnum.GR_EDIT){
                            <div class="d-flex justify-content-end">
                              <p-fileUpload class="action" chooseIcon="pi-upload"
                                            [disabled]="disabled" #fileUploadRef
                                            [accept]="acceptedAttachmentsMimeTypes()?.join(',')"
                                            multiple="false" mode="basic"
                                            auto="true" customUpload="true"
                                            (uploadHandler)="handleAttachmentInstanceUpload(instance.client, $event)"
                                            (onSelect)="handleValidation($event)"
                              ></p-fileUpload>
                            </div>
                          }
                        </td>
                      </tr>
                      <tr>
                        <td colspan="2" style="border-top: none;">
                          @if(instance.openText) {
                            <p-message severity="warn" text="{{ 'smartCreation.attachments.openTextEnabled' | translate }}" styleClass="w-100 text-center"></p-message>
                          } @else if(instance.attachments?.length > 0) {
                            <material-attachments
                                [attachments]="instance.attachments"
                                [viewMode]="viewMode"
                                [disabled]="disabled || instance.openText"
                                (attachmentDownload)="handleAttachmentDownload($event)"
                                (attachmentRemove)="handleAttachmentInstanceRemove(instance.client, $event)"
                                [uploadBtnPosition]="'HIDDEN'"
                            >
                            </material-attachments>
                          } @else {
                            <div>
                              {{ 'smartCreation.attachments.no-instance-attachments' | translate }}
                            </div>
                          }
                        </td>
                      </tr>
                    </ng-template>
                  </p-table>
                }
            </div>
        </div>
    `
})
export class MaterialGRAttachmentsComponent {

  @Input() disabled = false;
  @Input() instances: Array<AttachmentInstanceDetails>;
  @Input() grAttachments: AttachmentInfo[];
  @Input() viewMode: ViewModeEnum;

  @Output() attachmentUpload = new EventEmitter<File>();
  @Output() attachmentInstanceUpload = new EventEmitter<AttachmentInstanceUploadRequest>();
  @Output() attachmentRemove = new EventEmitter<string>();
  @Output() attachmentInstanceRemove = new EventEmitter<AttachmentInstanceCleanRequest>();
  @Output() attachmentDownload = new EventEmitter<string>();

  @ViewChild('fileUploadRef') fileUploadRef: FileUpload;
  acceptedAttachmentsMimeTypes = input<string[]>(null);

  protected readonly ViewModeEnum = ViewModeEnum;

  handleValidation(event$: FileSelectEvent) {
    EventUtils.stopPropagation(event$);
    this.fileUploadRef.msgs = undefined;
  }

  handleAttachmentUpload(file: File) {
    this.attachmentUpload.emit(file);
  }

  handleAttachmentRemove(uuid: string) {
    this.attachmentRemove.emit(uuid);
  }

  handleAttachmentInstanceUpload(instanceId: UUID, event$: FileUploadHandlerEvent) {
    const file = event$.files[0];
    this.attachmentInstanceUpload.emit({instance: instanceId, file});
    this.fileUploadRef.clear();
  }

  handleAttachmentInstanceRemove(instanceId: UUID, attachmentUUID: UUID) {
    this.attachmentInstanceRemove.emit({instance: instanceId, attachmentUUID});
  }

  handleAttachmentDownload(uuid: string) {
    this.attachmentDownload.emit(uuid);
  }

}
