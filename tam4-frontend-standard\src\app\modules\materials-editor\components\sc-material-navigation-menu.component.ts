import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, input, Signal } from '@angular/core';
import { Tam4TranslationModule } from "@creactives/tam4-translation-core";
import { TranslateModule } from '@ngx-translate/core';
import { CardModule } from 'primeng/card';
import { FieldsetModule } from 'primeng/fieldset';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { TooltipModule } from 'primeng/tooltip';
import { BaseMaterialEditorTabsKeys, DynamicFormGroup } from 'src/app/modules/materials-editor/models/material-editor.types';
import { EventUtils } from 'src/app/utils';

export interface NavigationMenuItem {
  hook: string;
  label: string;
  errors?: Array<string>;
}

export const attachmentTab: NavigationMenuItem = {
  hook: 'attachments',
  label: 'smartCreation.attachments.label'
};

export const thumbnailTab: NavigationMenuItem = {
  hook: 'thumbnails',
  label: 'smartCreation.thumbnails.label'
};

export const classificationTab: NavigationMenuItem = {
  hook: 'classification',
  label: 'smartCreation.classification.label'
};

export const similarMaterialTab: NavigationMenuItem = {
  hook: 'similarMaterials',
  label: 'smartCreation.smartValidation.selectFilters.similarMaterials'
};

export const additionalInformationTab: NavigationMenuItem = {
  hook: 'additionalInformation',
  label: 'smartCreation.approval.additionalInformation'
};

export const grInstancesTab: NavigationMenuItem = {
  hook: 'instances',
  label: 'layout.item-details.tabs.instances'
};

export const relationshipsTab: NavigationMenuItem = {
  hook: 'relations',
  label: 'layout.item-details.tabs.relations'
};

@Component({
  standalone: true,
  imports: [CommonModule, TranslateModule, CardModule, FieldsetModule, OverlayPanelModule, TooltipModule, Tam4TranslationModule],
  selector: 'div[scMaterialNavigationMenuComponent]',
  template: `
    <p-card>
      <ul class="sc-navigation-menu">
        <li *ngFor="let btElem of topListItems()" class="sc-navigation-menu-item"
            (click)="scrollTo($event, btElem.hook)">
          @if (btElem.errors?.length > 0) {
            <i class="align-content-center fa-solid exclamation-triangle-style fa-triangle-exclamation text-danger"
               (mouseenter)="topListItemsErrors?.show($event)"
               (mouseout)="topListItemsErrors?.hide()">
            </i>
            <p-overlayPanel #topListItemsErrors>
              <ul class="m-0 pl-2">
                <li *ngFor="let error of btElem?.errors" class="text-danger">
                  {{ error | translate}}
                </li>
              </ul>
            </p-overlayPanel>
          }
          <span class="tab-tile vertical-align-middle font-semibold">
            {{ btElem.label | translate }}
          </span>
        </li>
        @for (tab of dynamicFormGroup(); track tab; let i = $index) {
          @if (tab?.key !== BaseMaterialEditorTabsKeys.GOLDEN_RECORD || showGrFromScratch()) {
            <li class="sc-navigation-menu-item" (click)="scrollTo($event, tab.key)">
              @if (tab?.errors?.length > 0) {
                @if (errorLevel(tab) === 'error') {
                  <i class="align-content-center fa-solid exclamation-triangle-style fa-triangle-exclamation text-danger"
                     *ngIf="tab?.errors?.length > 0"
                     (mouseenter)="tabErrors?.show($event)"
                     (mouseout)="tabErrors?.hide()">
                  </i>
                } @else if (errorLevel(tab) === 'warning') {
                  <i class="align-content-center fa-solid fa-info-circle text-orange-500"
                     *ngIf="tab?.errors?.length > 0"
                     (mouseenter)="tabErrors?.show($event)"
                     (mouseout)="tabErrors?.hide()">
                  </i>
                }
              }
              <span
                  class="tab-tile vertical-align-middle font-semibold">{{ tab.title ? tab.title : tab.key }}</span>
              <i class="align-content-center exclamation-circle-style fa-regular fa-exclamation-circle text-warning"
                 *ngIf="tab?.hasMandatoryAttributes && isEditEnabled()"
                 pTooltip="{{'smartCreation.fields.mandatoryFields' | translate}}"
                 tooltipPosition="bottom">
              </i>
              <p-overlayPanel #tabErrors>
                <ul class="m-0 pl-2">
                  <li *ngFor="let error of tab.errors" class="{{error?.status === 'warning' ? 'text-warning' : 'text-danger'}}">
                    {{ error.statusMessage | translate}}
                  </li>
                </ul>
              </p-overlayPanel>
            </li>
          }
        }
        <li *ngFor="let btElem of bottomListItems()" class="sc-navigation-menu-item"
            (click)="scrollTo($event, btElem.hook)">
          @if (btElem.errors?.length > 0) {
            <i class="align-content-center fa-solid exclamation-triangle-style fa-triangle-exclamation text-danger"
               (mouseenter)="bottomListItemsErrors?.show($event)"
               (mouseout)="bottomListItemsErrors?.hide()">
            </i>
            <p-overlayPanel #bottomListItemsErrors>
              <ul class="m-0 pl-2">
                <li *ngFor="let error of btElem?.errors" class="text-danger">
                  {{ error | translate}}
                </li>
              </ul>
            </p-overlayPanel>
          }
          <span class="tab-tile vertical-align-middle font-semibold">
            {{ btElem.label | translate }}
          </span>
        </li>
      </ul>
    </p-card>
  `,
  host: {
    '[class]': "'flex-0 sc-material-navigation-menu-component'"
  },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ScMaterialNavigationMenuComponent {

  topListItems: Signal<NavigationMenuItem[]> = input([]);
  bottomListItems: Signal<NavigationMenuItem[]> = input([]);
  isEditEnabled: Signal<boolean> = input();
  showGrFromScratch: Signal<boolean> = input.required();


  dynamicFormGroup: Signal<DynamicFormGroup[]> = input([]);

  constructor() {

  }

  public scrollTo(event$: any, element: any): void {
    EventUtils.stopPropagation(event$);

    // const scrollRef :HTMLElement = document.getElementsByClassName('autoscroll-target').item(0) as HTMLElement
    const elRef: HTMLElement = document.getElementById(element) as HTMLElement;

    elRef?.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'start'
    });
  }

  errorLevel(tab: DynamicFormGroup): 'error' | 'warning' | '' {
    if (!tab?.errors?.length) { return ''; }
    if (tab.errors.some((error) => error.status === 'error')) { return 'error'; }
    if (tab.errors.some((error) => error.status === 'warning')) { return 'warning'; }
    return 'error';
  }

  protected readonly BaseMaterialEditorTabsKeys = BaseMaterialEditorTabsKeys;
}
