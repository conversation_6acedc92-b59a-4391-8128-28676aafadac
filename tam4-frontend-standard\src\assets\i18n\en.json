{"audit": {"actions": {"all": "All", "material_edit_workflow_started": "Material edit: workflow started", "material_edit_patch_assigned": "Material edit: patch assigned", "material_edit_patch_created": "Material edit: patch created", "material_edit_approved": "Material edit: patch approved", "material_edit_rejected": "Material edit: patch rejected", "material_edit_integrated": "Material edit: integrated in ERP", "material_edit_completed": "Material edit: patch completed", "material_edit_patch_failed": "Material edit: patch failed", "material_edit_workflow_ended": "Material edit: workflow ended", "material_creation_workflow_started": "Material creation: workflow started", "material_creation_approved": "Material creation: approved", "material_creation_assigned": "Material creation: assigned for patch", "material_creation_rejected": "Material creation: rejected", "material_creation_integrated": "Material creation: integrated in ERP", "material_creation_completed": "Material creation: completed", "material_creation_failed": "Material creation: failed", "material_creation_workflow_ended": "Material creation: workflow ended", "material_extension_edit_workflow_started": "Material Extension Edit: workflow started", "material_extension_integrated": "Material Extension Edit: integrated in ERP", "material_extension_edit_workflow_ended": "Material Extension Edit: workflow ended", "material_extension_creation_workflow_started": "Material Extension Creation: workflow started", "material_extension_creation_workflow_ended": "Material Extension Creation: workflow ended", "material_deduplicate_workflow_started": "Material Deduplicate: workflow started", "material_deduplicate_workflow_ended": "Material Deduplicate: workflow ended", "material_relationship_workflow_started": "Material Relationship: workflow started", "relationship_material_erp_updated": "Material Relationship: integrated in ERP", "material_relationship_failed": "Material Relationship: failed", "material_edit_approveds_relationship": "Material Relationship: workflow approved", "material_edit_rejecteds_relationship": "Material Relationship: workflow rejected", "material_relationship_workflow_ended": "Material Relationship: workflow ended", "user_requested_material_creation": "Material creation requested", "user_requested_material_extension": "Material extension requested", "material_edit_approved_extension": "Material extension approved", "material_edit_approved_extension_edit": "Material extension edit approved", "material_edit_rejected_extension": "Material extension rejected", "material_edit_rejected_extension_edit": "Material extension edit rejected", "material_extension_edit_created": "Material extension edit created", "user_requested_material_extension_edit": "Material extension edit requested", "material_rejected": "Material rejected", "material_warning_created": "Material warning created", "material_warning_deleted": "Material warning deleted", "user_requested_materials_deduplicate": "Material deduplicate requested", "materials_deduplicate_completed": "Material deduplicate completed", "material_created": "Material Created", "relationship_rejected": "Relationship rejected", "relationship_created": "Relationship created", "material_updated": "Material Updated", "material_relationship_updated": "Relationship Updated", "material_relationship_deleted": "Relationship Deleted", "material_warning_managed": "Warning Managed", "material_extension_edit": "Extension updated", "material_extension_created": "Extension created", "materials_deduplicated": "Material deduplicated", "material_creation_imported": "Material creation imported", "material_extension_imported": "Material extension imported", "material_golden_record_deleted": "Material golden record deleted", "role_update": "Role Updated", "role_creation": "Role Created", "role_delete": "Role Deleted", "export_role_excel": "Roles Excel Export", "group_creation": "Group Created", "group_update": "Group Updated", "group_delete": "Group Deleted", "export_group_excel": "Group Excel Export", "custom_role_creation": "Custom Role Created", "custom_role_update": "Custom Role Updated", "custom_role_delete": "Custom Role Deleted", "user_role_add": "User Roles Added", "user_role_update": "User Roles Updated", "user_role_set": "User Roles Set", "user_role_delete": "User Roles Deleted", "user_block_account": "User Blocked", "user_unblock_account": "User Unblocked", "user_resend_activation_mail": "Resend Activation E-Mail", "user_create": "User Created", "user_update": "User Updated", "delete_users": "User Deleted", "change_mail_settings": "Mail Settings Changed", "change_fallback_languages": "Fallback Languages Changed", "crate_user_with_roles": "User with Roles Created", "export_users_excel": "Users Excel Export"}, "audit-view": {"paging-title": "Currently viewing: {{itemsInPage}} results of {{resultCount}}", "filters": {"date-range": "Date range", "action-type": "Action type", "user": "User", "material": "Material"}, "testing": "Testing the translation", "additional": {"assigned-to-user": "Assigned to user", "assigned-to-roles": "Assigned to roles", "warning-type": "Warning type", "system-admin": "System admin"}}, "loading-view": {"searching": "Searching..."}, "no-result-view": {"title": "No records found", "subheading": "There are no results found for audit log:", "suggestion-title": "Suggestions:", "line1": "Make sure that you have the right permissions.", "line2": "Try a different filter combination"}, "error-view": {"head-text": "Error while processing your request"}, "errors": {"default": "Something went wrong, please try again later", "save": "An error occurred while saving, please check your data and try again.", "materials": {"material-with-this-id-not-found": "Material was not found!"}}}, "bulk-upload": {"table": {"REQUESTED": "Creation requested", "STARTED": "Process started", "WAITING_FOR_EDIT": "Waiting for editing", "WAITING_FOR_APPROVAL": "Waiting for approval", "WAITING_FOR_ERP": "Waiting for ERP", "COMPLETED": "Creation completed", "REJECTED": "Creation rejected", "DUPLICATE": "Row marked as duplicate", "DELETED": "Row removed", "ABORTED": "Creation aborted"}, "fields": {"material-type": "Material Type", "material-group": "Material Group", "base-unit-of-measure": "Base Unit of Measure", "manufacturer-part-number": "Manufacturer Part Number", "short-text": "Short Text", "long-text-md": "Long Text", "long-text-po": "Long Text PO", "purchasing-group": "Purchasing Group"}, "pagination": {"first": "First", "previous": "Previous", "next": "Next", "last": "Last"}, "process-status": {"uploaded": "Uploaded", "syntactic-validation-in-progress": "Syntactic validation in progress", "syntactic-validation": "Syntactic validation to be reviewed", "semantic-classification": "Semantic classification to be reviewed", "waiting-for-completion": "Waiting for completion", "completed": "Completed", "duplicates-check": "Duplicates check", "archived": "Archived", "deleted": "Deleted", "editing": "Editing", "data-ingestion": "Data ingestion", "ai-analysis": "ai-analysis", "file-validation": "File validation", "file-validation-errors": "File validation errors", "invalid-file": "Invalid file", "duplicates-request-to-be-done": "Duplicates request to be done", "duplicates-waiting-for-report": "Duplicates waiting for report", "duplicates-report-to-be-retreived": "Duplicates report to be retrieved", "duplicates-to-be-reviewed": "Duplicates to be reviewed", "sending-creation-requests": "Sending creation requests", "waiting-for-process-completion": "Waiting for process completion", "finished": "Finished", "terminated": "Terminated", "rejected": "Rejected", "file-validation-errors-internal": "File validation internal errors", "ignore": "Ignore", "data-default-enrichment": "Data default enrichment", "file-validation-internal": "File validation internal"}, "errors": {"error-view": {"head-text": "Error while processing the request", "all-errors": "All errors ({{value}})"}, "default": "Something went wrong, please try again later", "bom-critical-errors": "Critical error during BOM structure validation", "critical-errors": "Blocking errors report", "validation-generic-error": "Something went wrong while validating the file", "invalidFileUploaded": "The uploaded file is invalid", "cannot-delete-bom-present": "Cannot delete, B<PERSON> present.", "cannot-generate-excel-template": "Could not generate excel template", "file-stream-error": "File stream error", "master-data-does-not-belong-to-process": "Masterdata does not belong to process", "masterdata-not-updated": "Masterdata is not updated", "process-not-found": "Process not found", "reclassification-error": "Error while reclassifying masterdata details", "status-not-found": "Status not found", "status-not-recognized": "Status not recognized", "unauthorized-action": "You are not authorized to perform this action", "user-type-not-found": "User type not found"}, "operations": {"completed": "The operation was completed successfully", "processing-data": "Processing data..."}, "search": {"title": "No records found", "subheading": "There are no results matching your filters:", "suggestion-title": "Suggestions:", "line1": "Try a different filter combination", "line2": "Try to reset your filters"}, "bulk-card-upload": {"file-upload": "File upload", "download-file-materials": "Get template (materials)", "download-file-services": "Get template (services)", "custom-template-upload": "Upload custom template", "custom-template-download": "Download template"}, "syntactic-validation": {"popup": {"header": {"attribute": "Attribute", "value": "Value", "error": "Error"}}, "navigation-header": {"blocking-errors": "Blocking errors", "warnings": "Warnings", "materials-with-issues": "Materials with issues", "materials-without-issues": "Materials without any issues", "errors-report": "Rows with errors"}, "errors": {"title": "No materials found for the selected error type", "filter-materials": "Select an error type:", "material-group": {"empty": "Material group is not defined", "not-in-list": "Material group is not recognized"}, "material-type": {"empty": "Material type is not defined", "not-in-list": "Material type not in list"}, "base-unit-of-measure": {"empty": "Base unit of measure is not defined", "not-in-list": "Base unit of measure is not recognized"}, "manufacturer-part-number": {"empty": "Manufacturer part number is empty"}, "short-text": {"empty": "Short text is empty", "limit": "Reached maximum length for short text field"}, "long-text-md": {"empty": "Long text is empty"}, "long-text-po": {"empty": "Long text PO is empty"}, "purchasing-group": {"empty": "Purchasing group is empty", "not-in-list": "Purchasing group is not recognized"}, "bom": {"bom-row-id-equal-to-parent": "BOM Row Id is equal to BOM Parent Id", "bom-row-id-must-not-be-empty": "BOM row id must not be empty", "bom-loop": "Parent Row Id may not be equal to Row Id in the same Excel row", "bom-quantity-empty": "BOM Quantity must be greater than 0", "bom-multi-client": "Multiple client values found", "parent-not-found": "<PERSON><PERSON> Id not found"}, "client": {"empty": "Client is not defined", "not-in-list": "Client not in list", "not-authorized": "No permission for client"}, "mdDomain": {"empty": "Domain field cannot be empty"}, "plant": {"not-valid-value": "Plant - not allowed value", "not-authorized": "No permission for plant"}, "short-text-md": {"empty": "Short text is empty", "limit": "Reached maximum length for short text field", "duplicates": {"it": "Row duplicated by ITALIAN short text", "en": "Row duplicated by ENGLISH short text", "es": "Row duplicated by SPANISH short text", "de": "Row duplicated by GERMAN short text", "pt": "Row duplicated by PORTUGUESE short text", "fr": "Row duplicated by FRENCH short text", "ru": "Row duplicated by RUSSIAN short text", "zh": "Row duplicated by CHINESE short text", "ko": "Row duplicated by KOREAN short text", "ro": "Row duplicated by ROMANIAN short text", "gr": "Row duplicated by GREEK short text", "pl": "Row duplicated by POLISH short text", "cs": "Row duplicated by CZECH short text", "hr": "Row duplicated by Croatian short text", "iw": "Row duplicated by HEBREW short text"}}, "manufacturer-code-and-manufacturer-part-number": {"duplicates": "Row duplicate by 'Manufacturer' and 'Manufacturer Part Number'"}, "unknown-error-while-parsing": "Unknown error occurred while parsing Excel", "unknown-error-while-validating": "Unknown error occurred while validating Excel", "unhandled-column": "Unhandled column in the Excel: {{param}}", "warehouse": {"not-valid-value": "Warehouse - not allowed value"}, "category": {"empty": "Category is not set"}, "descriptions": {"empty": "All descriptions are empty, provide at least one"}, "material-key-masterdata-exist": "Material code uploaded is already existing in the database for the specific client"}, "warnings": {"title": "No material found for the selected warning type", "filter-materials": "Select a warning type:"}, "material-errors-popup": {"row-number": "Details of row #"}, "materials-with-issues": {"no-materials-found": "There are no materials with syntactic issues found"}, "materials-without-issues": {"no-materials-found": "There are no materials without issues found"}, "no-data-available": "No data available", "continue": "Continue", "edit-selected": "Edit selected", "edit-errors-modal": {"edit-all": "Edit errors for row #", "title": "Edit value for field", "selected-number": "Number of selected items:", "row-number": "Row number:", "error-type": "Error type:", "field-name": "Field name:", "current-value": "Current value:", "new-value": "New value", "insert-value": "Insert value ...", "no-data": "No data available", "cancel": "Cancel", "save": "Save", "search-value": "Search for a value ..."}, "delete-materials": {"delete-selected": "Delete selected", "delete-materials-successful": "The selected materials were deleted successfully!"}, "affected-rows": "Affected row numbers", "critical-error-title": "PROCESS TERMINATED BY BLOCKING ERRORS (These errors must be fixed manually in the source file)", "confirm-popup": {"title": "Master data categorization to be confirmed", "message": "There are some line items yet to be validated with Medium or Good confidence level. Are you sure you want to proceed?", "actions": {"cancel": "Cancel", "confirm": "Continue"}}, "waiting-for-internal-user": {"title": "The current process is WAITING", "message": "Waiting for internal user operations..."}, "error-during-validation": "Unknown error during file validation"}, "semantic-classification": {"confirm-selected": "Confirm selected", "navigation-header": {"confirmed-materials": "Confirmed materials", "poor-completeness": "Poor completeness", "fair-completeness": "Fair completeness", "good-completeness": "Good completeness"}, "empty-state": {"title": "No materials found for the chosen confidence level", "subtitle": "Materials generated for the selected confidence or status will appear here"}, "change-category-modal": {"title": "Change classification", "row-number": "Row number :", "description": "Description", "suggestested": {"material-group-classification": "Suggested material group classifications: ", "enriched-material-group": "Suggested enriched material groups: ", "technical-classification": "Suggested technical classifications: "}, "no-data": "No data available", "manual-search": "None of these is correct? Try searching manually here", "cancel": "Cancel", "confirm-selection": "Confirm selection", "change-to": "Change to"}, "continue": "Continue"}, "duplicates-check": {"navigation-header": {"low": "Low", "medium": "Medium", "high": "High", "none": "None", "to-create": "To create"}, "modal": {"group-card": {"basic-data": "Basic data", "description": "Description", "unit-of-measure": "Unit of measure", "source": "Source", "material-type": "Material type", "manufacturer-part-number": "Manufacturer part number", "long-text-md": "Long text MD", "long-text-po": "Long text PO"}, "select-for-use": "Select for use", "duplicate-detected": "Potential duplicate detected", "select": "Select", "ignore": "Ignore"}, "table": {"row-number": "Row number", "description": "Description", "action": "Action", "edit": "Edit"}}, "creation-requests": {"navigation-header": {"creations-requested": "Creations requested", "creations-completed": "Creations completed", "creations-rejected": "Creations rejected", "rows-ignored": "Rows ignored"}, "empty-state": {"title": "No materials found for the chosen workflow status", "subtitle": "Materials generated for the selected workflow status will appear here"}}, "terminate-process": {"terminate-button": "Terminate", "title": "Terminate process", "confirm-message": "Do you want to proceed ?", "cancel": "Cancel"}, "generate-report": "Generate Report", "historic-uploads": {"file-name": "File name", "uploaded-by": "Uploaded by", "upload-date": "Upload date", "see-details": "See details", "row-count": "Row count", "empty-view": {"title": "No files have been generated", "subtitle": "Downloads generated from other services will appear here"}, "status": "Status", "actions": "Actions", "summary": "Summary", "upload-id": "Upload Id", "upload-start-date": "Upload date range", "bom-masterdata": "BOM/masterdata", "total": "Total", "upload-description": "From this page you can upload excels containing masterdata. Please start by downloading the excel template, as it it will reduce errors when the excel is uploaded."}, "file-validation": {"data-quality": {"low": "LOW", "medium": "MEDIUM", "good": "GOOD", "approved": "APPROVED", "submitted": "SUBMITTED", "to-rework": "TO REWORK"}, "step": {"uploaded": "Uploaded", "file-validation": "File Validation", "data-validation": "Data Validation", "duplicates-check": "Duplicates Check", "waiting-for-completition": "Waiting For Completion", "done": "Done"}, "table": {"row-number": "Row number", "description": "Description", "error-types": "Error Types", "action": "Action", "view-details": "View details", "edit": "Edit", "delete": "Delete", "field-name": "Field Name", "field-value": "Field Value"}}, "data-validation": {"no-data-available": "No data are present in this tab", "data-quality": "Data quality", "categories": "Categories", "raw-data": "Raw data", "data-validation": "Data Validation", "success-message": "Changes saved successfully!", "button-labels": {"display-all-categories": "Display All Categories", "change-category": "Change Category", "approve": "Approve", "submit": "Submit", "ignore": "Ignore", "reject": "Reject"}, "table": {"row-number": "Row number", "short-text": "Short text", "uom": "UoM", "manufacturer": "Manufacturer", "manufacturer-nr": "Manufacturer Nr.", "classification": "Classification"}, "error-message": "Some error occurred while validating and saving!"}, "custom-template": {"all": "All", "empty": "Standard", "saving-error": "Error/s occurred during saving.", "validator": {"file-type-excel": "Only excel file with XLSX extension is allowed.", "layout-excel": "The columns layout is wrong.", "md-domain-excel": "The template is using the wrong domain (Material/Service).", "not-recognized": "The following field/s is/are not recognized:", "expected-field-not-found": "The template doesn't have the following field/s that are available:"}, "upload-popup": {"template-saved": "Template saved using the following language for column headers:", "header": "Import custom excel for bulk upload", "clients": "Clients", "clientsTooltip": "Client", "description": "Please upload a custom excel derived from the standard excel for bulk upload. This will avoid validation errors when the custom excel is uploaded.", "dragNdrop": "Drag 'n' Drop the file in the box below, or simply click on it to start the upload.", "already-exists-warning": "A template already exists for the selected client. Do you want to replace it?", "cancel": "Cancel", "confirm": "Confirm", "close": "Close"}, "download-popup": {"header": "Download excel for bulk upload", "download": "Download", "templates": "Templates", "templatesTooltip": "Template", "languages": "Languages", "languagesTooltip": "Language for column header"}, "all-custom": "(custom) All"}}, "data-loader": {"statistics": {"indicators": {"ignored": "Ignored Materials", "analyzed": "Analyzed Materials", "total-count": "Total Materials"}}, "validation": {"form": {"panel": "Dry validation of customer files", "title": "Please fill out this form", "files": {"label": "Compressed file (*.zip)", "placeholder": "Select file"}, "email": {"label": "Email", "placeholder": "Your company e-mail address"}, "scope": {"label": "<PERSON><PERSON>", "placeholder": "e.g. complete upload, refresh IT, ..."}, "notify": {"label": "I want to receive the result via e-mail"}, "reset": "Reset", "confirm": "Upload & Validate"}, "instructions": {"title": "Instructions", "content": "<p>This tool validates input used in TAM4 initial loading.</p>\n<ul>\n<li>Be advised that it may take <b>a while</b> to validate these files depending on the file size.</li><li>In case of errors, feel free to copy the log below to communicate these errors to the customer</li><li>Only .zip files are allowed to be uploaded</li><li>Make sure that your zip file <b>does not</b> contain folders.</li></ul>"}, "steps": {"upload": "Upload complete &mdash; Validation in progress.", "uploading": "Uploading file &mdash; Do not close this page.", "outcome": {"title": "Outcome", "success": "The file conforms to the required format.", "warning": "The file conforms to the required format. See below for suggestions.", "error": "The file does not conform to the required format. See below for more information."}, "warnings": {"title": "Warnings"}, "errors": {"title": "Errors"}}, "error": "An error occurred while processing the request"}, "export": {"title": "Corpus export", "corpus": {"download": {"title": "Download the last generated corpus available", "button": "Download"}, "generate": {"error": "Error occurred wile generating corpus", "title": "Generate a corpus file with the database's data", "button": "Generate", "ongoing": "Generating corpus, please wait", "stop": "Stop process"}}}, "ontology-management": {"form": {"panel": "Refresh TAM ontology", "title": "Please upload the new ontology file(s)", "files": {"label": "Compressed file (*.zip)", "placeholder": "Select file"}, "reset": "Reset"}, "instructions": {"title": "Instructions", "content": "<p>With this tool, it is possible to refresh TAM4 ontology and publish it in the various services.</p>\n<ul>\n<li>Be advised that it may take <b>a while</b>, depending on the file size.</li><li>In case of errors, feel free to copy the log below to communicate these errors to the customer</li><li>Only .zip files are allowed to be uploaded</li><li>Make sure that your zip file <b>does not</b> contain folders.</li></ul>"}, "upload": {"upload-button": "Upload", "uploading": "Uploading file &mdash; Do not close this page.", "success": "The file conforms to the required format.", "error": "The file does not conform to the required format. See below for more information.", "connection-error": "Unknown error during connection."}, "publish": {"publish-button": "Deploy", "success": "Ontology published correctly", "error": "Unable to publish ontology. See below for more information.", "publishing": "Publishing"}, "warnings": {"title": "Warnings"}, "errors": {"title": "Errors"}, "error": "An error occurred while processing the request"}, "errors": {"missing-client-code": "Client code is missing"}, "select-client": "Select a data loader client to show charts!", "validationload": {"form": {"panel": "Validation of customer files", "title": "Please fill out this form", "tableFiles": {"label": "CSV tables file (*.zip)", "placeholder": "Select file", "confirm": "Upload Data"}, "ruleFiles": {"label": "Rules file (*.json)", "placeholder": "Select file", "confirm": "Upload Rules", "lastUploadedRuleFile": "Last Uploaded Rule File"}, "email": {"label": "Email", "placeholder": "Your company e-mail address"}, "scope": {"label": "<PERSON><PERSON>", "placeholder": "e.g. complete upload, refresh IT, ..."}, "notify": {"label": "I want to receive the result via e-mail"}, "reset": "Reset", "confirm": "Run validation", "report": "Generate Report", "download": "Download Report"}, "instructions": {"title": "Instructions", "content": "<p>This tool loads input used in TAM4 initial loading to database tables.</p>\n<ul>\n<li>Be advised that it may take <b>a while</b> to load these files depending on the file size.</li><li>In case of errors, feel free to copy the log below to communicate these errors to the customer</li><li>Only .zip files are allowed to be uploaded</li><li>Make sure that your zip file <b>does not</b> contain folders.</li></ul>"}, "steps": {"upload": "Upload complete &mdash; Load to tables in progress.", "uploading": "Uploading file &mdash; Do not close this page.", "outcome": {"title": "Outcome", "success": "The files conforms to the required format.", "warning": "The files conforms to the required format. See below for suggestions.", "error": "The files does not conform to the required format. See below for more information."}, "outcomeValidate": {"title": "Outcome", "success": "All rules successfully validated.", "warning": "All rules validated. See below for suggestions.", "error": "Some rules failed validation. See below for more information."}, "warnings": {"title": "Warnings"}, "errors": {"title": "Errors"}, "uploadRules": "Rule file uploaded successfully.", "validate": "Validation completed successfully", "validating": "Validation in progress", "reporting": "Report generation in progress", "reported": "Report generated, check \"File Export\" tab to download.", "reportGeneration": {"title": "Outcome", "success": "Report generated, check \"File Export\" tab to download.", "error": "Something went wrong while generating the report"}}, "error": "An error occurred while processing the request"}}, "duplicates": {"group": {"affected-stock": "Affected stock", "total-amount-stock": "Total amount of stock", "affected-quantity": "Affected quantity", "total-amount": "Total amount of {{value}}", "total-consumption": "Total consumption", "total-consumption-value": "Total consumption value", "total-consumption-quantity": "Total consumption quantity", "amount-consumed": "Amount of consumed {{value}}", "confidence": "Confidence", "classification": "Classification", "accuracy-level": "Accuracy level", "of": "of", "metric-name": "This group has been created matching manufacturer name and code.", "send-enrichment": "Request edit", "assign-group-to-me": "Assign to me", "assign-group-materials": "Assign materials in group for deduplication", "hide-group": "Hide group", "hide-group-successful": "Group successfully hided", "show-group": "Unshelve group", "show-group-successful": "Group successfully shown", "create-rel": "Mark duplicate", "basic-data": "Basic data", "material-code": "Master data code", "material-type": "Material type", "technical-attributes": "Technical attributes", "fields-without-values": "Fields without values", "description": "Description", "standard-price": "Standard price ({{currencyCode}})", "unit-of-measure": "Unit of measure", "metric": {"Code": "This group has been created matching manufacturer name and code.", "TechnicalSheets": "This group has been created matching the technical sheet."}, "groups-all": {"card-filter-viewed": "Currently viewing: {{groupLength}} groups of {{totalGroups}}"}, "groups-exact-match": {"card-filter-viewed": "Currently viewing: {{groupLength}} groups of {{totalGroups}}"}, "groups-hidden": {"card-filter-viewed": "Currently viewing: {{groupLength}} groups of {{totalGroups}}"}, "assign-group-popup": {"note": "Note", "actions": {"cancel": "Cancel", "assign": "Assign", "loading": "Loading...", "no-roles": "No roles found", "roles-placeholder": "Select roles to assign group"}, "form-controls": {"chk-assign": "Assign to me", "lbl-select-role": "Role", "info-select-role": "it will be used if you unassign the group"}}, "list-truncated": "Currently viewing 10 of the {{totalMaterials}} materials in this group", "financial-data": "Valuations", "affected-ordered": "Affected purchase orders", "display-valuations": "Valuations", "customization": "Customization", "std-price-from-to": "from {{from}} to {{to}}", "assign-group": "Assign for processing", "shelve-group": "Shelve group", "shelve-group-successful": "Group successfully shelved", "dismiss-group": "Dismiss group", "dismiss-group-successful": "Group successfully dismissed", "confirm": "Confirm", "cancel": "Cancel", "confirmation-message": "The whole group will be removed from the list of potential duplicates, independently from the filter (e.g. client, category)", "locked": "Please ignore this group if you already completed your work on it (status is updating)", "total-ordered": "Total Purchase Orders", "total-ordered-value": "Total Purchase Orders value", "total-ordered-quantity": "Total Purchase Orders quantity", "display-label": "Show/hide block", "display-manufacturer": "Manufacturer Data", "display-customer-fields": "Custom Fields", "display-technical-attributes": "Technical Attributes", "plants": "Plants", "plants-more": "Show more ({{numberOfPlants}})", "countries": "Countries", "countries-more": "Show more ({{numberOfCountries}})", "description-show": "Show Other", "description-hide": "<PERSON>de Other", "descriptions": {"shortDescriptions": "Short Descr.", "purchaseOrderDescriptions": "Purchase Order Descr.", "internalNoteDescriptions": "Internal Note Descr.", "longDescriptions": "Long Descr.", "inspectionDescriptions": "Inspection Descr."}, "customer-fields": "Custom Fields", "manufacturer-data": "Manufacturer Data", "manufacturer-part-number": "Manufacturer Part Number", "no-plants": "No plants"}, "messages": {"group-hidden-successful": "Message group-hidden-successful", "group-hidden-failure": "Message group-hidden-failure"}, "errors": {"error-message": "Oops! Something went wrong...", "error-view": {"head-text": "Error while processing your request"}, "cannot-lock-group": "Operation not allowed, another user is currently working on the same group", "workflow": {"errors": {"cannot-start-process-materials-already-in-deduplication": "Unable to start the process. One or more masterdata are already in a deduplication process"}}}, "empty": {"numberOfGroupsExactMatch": {"title": "Empty Groups Exact Match Title", "subtitle": "Empty Groups Exact Match Subtitle."}, "numberOfGroups": {"title": "No tasks in your queue", "subtitle": "When a data manager assigns materials to one of your plants, you'll see it here."}, "numberOfHiddenGroups": {"title": "No tasks awaiting approval", "subtitle": "When you complete one of the tasks that have been assigned to you, it will appear here."}}, "home": {"numberOfGroupsExactMatch-filtered": {"title": "Groups of potential duplicated items found", "subtitle": "Groups with only materials matching filters"}, "numberOfGroupsExactMatch": {"title": "Groups of potential duplicated items", "subtitle": "Group's list of potential duplicated materials"}, "numberOfGroups": {"title": "Groups of potential duplicated items found", "subtitle": "Groups of materials partially matching the filters"}, "numberOfHiddenGroups": {"title": "Hidden groups", "subtitle": "Shelved groups"}, "numberOfHiddenDueToProcessGroups": {"title": "Groups with work in progress", "subtitle": "Groups with materials that have an active process"}}, "confirmation": {"yes": "Yes", "no": "No"}, "pagination": {"first": "First", "previous": "Previous", "next": "Next", "last": "Last"}, "file-export": {"launch-file-export": "Export", "launch-file-export-successful": "Export request generated"}, "search": {"no-records-found": "No records found.", "no-matching-global-filters-results": "There are no results matching your current filter combination.", "suggestions": "Suggestions", "filter-combination-sugg": "Try a different filter combination", "diff-search-terms-sugg": "Try a different code", "general-search-sugg": "Try more general search terms", "reset-filters-sugg": "Try resetting the filters"}, "filters": {"clear-filters": " Clear Filters", "metrics": "Metric", "confidence": "Confidence", "order-by": "Sorting", "default-value": "Any", "default-order": "<PERSON><PERSON><PERSON>", "stock-amount": "Stock Amount", "consumption-amount": "Consumption Amount", "confidence-values": {"GOOD": "GOOD", "FAIR": "FAIR", "POOR": "POOR"}, "consumption-amount-asc": "Consumption Amount (Ascending)", "materials": "Material(s)", "materials-placeholder": "Digit material code (enter to add)", "material-list": "Material List"}, "amounts": {"materials": "Materials", "stock": "Total Stock amount", "consumption": "Total Consumption amount", "ordered": "Total Ordered amount", "services": "services"}, "popups": {"delete-relationship": {"confirmation": {"header": "Potential Data Loss Warning", "message": "If you proceed, any unsaved changes in the previous section will be lost. Are you sure you want to continue?", "acceptLabel": "Yes", "rejectLabel": "No"}}}}, "folder-navigation": {"folder-list": {"node": "Node", "stock": "Stock", "consumption": "Consumption", "ordered": "Ordered", "row-count": "Master data", "total": "Total", "page-empty": {"title": "No folders have been defined", "subtitle": "subtitle"}}, "country-chart": {"title": "Country Distribution", "table": {"code": "Code", "row-count": "Item count"}, "page-empty": {"title": "Master data in this category are not extended on any plants", "subtitle": "subtitle"}}, "plant-chart": {"title": "Plant Distribution", "page-empty": {"title": "Master data in this category are not extended on any plants", "subtitle": "subtitle"}}, "attribute-chart": {"title": "Attribute Distribution", "page-empty": {"title": "No attributes have been defined for this category", "subtitle": "subtitle"}}, "pagination": {"first": "First", "previous": "Previous", "next": "Next", "last": "Last"}, "errors": {"error-view": {"head-text": "Errore durante l'elaborazione della richiesta"}, "default": "Something went wrong, please try again later"}, "search": {"title": "No records found", "subheading": "There are no results matching your filters:", "suggestion-title": "Suggestions:", "line1": "Try a different filter combination", "line2": "Try to reset your filters"}, "breadcrumb": {"categories": "categories"}, "actions": {"set-as-filter": "Set as filter"}, "master-data-type-chart": {"title-materials": "Material Type Distribution", "title-services": "Service Type Distribution", "page-empty": {"title": "Master data in this category do not have a material type", "subtitle": "subtitle"}}}, "horizontal-menu": {"menu-1": {"label": "<PERSON><PERSON>", "title": "Title"}, "pageTitle": {"home-page": "Home", "search": "Search", "duplicates-management": "Duplicate management", "worklists": "My tasks", "enrichment": "Enrichment", "edit": "Edit", "creation": "Creation", "approver": "Approval", "relationship": "Relationship", "administration": "Administration", "data-quality": "Data quality", "worklist-duplicates": "Duplicates", "worklist-extension": "Extensions", "worklist-relationship": "Relationships", "folder-navigation": "Category explorer", "smart-creation": "Smart creation", "reporting": "Reporting", "reporting-materials": "Master data", "reporting-workflow": "Workflow", "reporting-warnings": "Warning", "engine-warnings": "Warning", "reporting-relationship": "Relationship", "file-export": "File export", "events-store": "Events storage", "data-loader": "Data loading", "contracts": "Contract", "suppliers": "Supplier", "excel-integration": "Excel integration", "warnings-dashboard": "Warning management", "account-management": "Account management", "business-units-admin": "Business unit management", "clients-administrator": "Client management", "country-admin": "Country management", "country-region-admin": "Country / Region management", "custom-roles-admin": "Custom roles", "group-management": "Group management", "plant-admin": "Plant management", "region-admin": "Region management", "role-management": "Role management", "slim-account-management": "Users Account Management", "audit": "Audit", "bulk-upload": "Bulk upload", "changelog": "Change log", "ontology-management": "Ontology management", "scheduled-reports": "Scheduled reports", "supplier-portal": "Supplier portal", "supplier-to-upload": "Upload", "events-storage": "Events storage", "integration-monitor": "Integration Monitor", "massive-edit": "Massive Edit", "data-loader-report": "Data Loader Report", "manage-suppliers": "Suppliers management", "base": "Basic", "bulk-extension": "Bulk Extension", "starter": "Data Loader Starter", "validation": "Data Loader Validation", "analytics": "Analytics", "admin-tools": "<PERSON><PERSON>", "attribute-mapper-admin-tool": "Attributes map Admin tool", "change-doc-classification": "Can change categorizations", "workflow-processes-monitor": "Workflow Process Monitor", "can_propose_new_values": "Can propose new values", "can_manage_golden_record": "Can manage golden record", "can-upload-custom-template": "Can upload custom template", "massive-relationship": "Massive Relationship", "batch-api": "Batch API", "external-gateway-api": "External Gateway API", "data-loader-api": "Data Loader API", "internal-api": "Internal API", "organizations-management": "Organizations management"}, "pageSubTitle": {"home-page": "Home page", "search": "Free text search", "duplicates-management": "Duplicate management", "worklists": "Worklists", "enrichment": "Enrichment worklist", "edit": "Edit worklist", "creation": "Creation worklist", "approver": "Approval worklist", "relationship": "Relationships worklist", "administration": "Administration", "data-quality": "Data quality", "worklist-extension": "Plant extension worklist", "worklist-duplicates": "Deduplication worklist", "folder-navigation": "Explore the system data", "smart-creation": "Smart creation", "reporting": "Reporting", "reporting-materials": "Master data", "reporting-workflow": "Workflow", "reporting-warnings": "Warning", "engine-warnings": "Warning", "reporting-relationship": "Relationship", "file-export": "File Export", "events-store": "Events storage", "data-loader": "Data loading", "contracts": "Contract worklist", "suppliers": "Supplier worklist", "excel-integration": "Excel integration", "warnings-dashboard": "Warning management", "account-management": "Account management", "business-units-admin": "Business unit management", "clients-administrator": "Client management", "country-admin": "Country management", "country-region-admin": "Country / Region management", "custom-roles-admin": "Custom roles management", "group-management": "Groups management", "plant-admin": "Plants management", "region-admin": "Regions management", "role-management": "Roles management", "slim-account-management": "Users account management", "audit": "Audit", "bulk-upload": "Bulk Upload", "changelog": "Change log", "scheduled-reports": "Scheduled reports", "bulk-extension": "Bulk Extension", "events-storage": "Events storage", "integration-monitor": "Integration Monitor", "massive-edit": "Massive Edit", "data-loader-report": "Data Loader Report", "manage-suppliers": "Suppliers management", "starter": "Data Loader Starter", "validation": "Data Loader Validation", "analytics": "Analytics", "admin-tools": "<PERSON><PERSON>", "supplier-portal": "Supplier portal", "ontology-management": "Ontology settings", "attribute-mapper-admin-tool": "Category Admin tool", "change-doc-classification": "Can change categorizations", "workflow-processes-monitor": "Workflow Process Monitor", "can_propose_new_values": "Can propose new values", "can_manage_golden_record": "Can manage golden record", "can-upload-custom-template": "Can upload custom template", "massive-relationship": "Massive Relationship"}}, "header": {"toggle-aside-menu": "Toggle"}, "top-bar": {"select-language": "Select your language", "change-language": "Change language", "profile-menu": "Profile menu", "notifications": "User Notifications", "empty": "No new", "logout": "Logout"}, "aside": {"home": "Dashboard"}, "lang": {"it": "Italian", "en": "English", "es": "Spanish", "de": "German", "pt": "Portuguese", "fr": "French", "ru": "Russian", "zh": "Chinese", "ko": "Korean", "ro": "Romanian", "gr": "Greek", "pl": "Polish", "cs": "Czech", "hr": "Croatian", "iw": "Hebrew", "cz": "Czech", "sq": "Albanian", "ar": "Arabic", "be": "Dutch (Flemish)", "bg": "Bulgarian", "ca": "Catalan", "da": "Danish", "nl": "Dutch", "et": "Estonian", "fi": "Finnish", "el": "Greek", "hi": "Hindi", "hu": "Hungarian", "is": "Icelandic", "ja": "Japanese", "lv": "Latvian", "lt": "Lithuanian", "mk": "Macedonian", "ms": "Malay", "mt": "Maltese", "no": "Norwegian", "nb": "Norwegian Bokmål", "nn": "Norwegian Nynorsk", "sk": "Slovak", "sl": "Slovenian", "sr": "Serbian", "sv": "Swedish", "th": "Thai", "tr": "Turkish", "uk": "<PERSON><PERSON><PERSON>", "vi": "Vietnamese"}, "search-popup": {"favourite": "Favourite", "search": "Search", "code": "Code", "description": "Description", "loading": "Loading...", "keep-open": "Keep open", "apply": "Apply", "error-message": "Oops! Something went wrong.", "empty-search": "No result found"}, "layout": {"errors": {"unknown-error": "An unknown error occurred", "title": "Operation could not be completed", "warning": "Warning"}, "success": {"success-compited": "Operation completed successfully", "take-in-charge": "Operation taken in charge"}, "home-page": {"menu-item": {"material-count": {"title": "Materials", "subtitle": "Total amount of materials"}, "total-stock": {"title": "Total Stock", "subtitle": "Total stock in all countries"}, "total-consumption": {"title": "Total consumption", "subtitle": "Total consumption in all countries"}, "your-requests": {"title": "Your requests", "subtitle": "Total count ofyour active requests"}, "warnings": {"title": "Warnings", "subtitle": "There are items that requires your attention"}}, "enabled-features": "Enabled Features", "enabled-worklist-features": "Enabled Worklist Features", "enabled-administrative-features": "Enabled Administrative Features", "current-processes-overview": "Current processes overview", "task-card-title": "Tasks currently in your queue", "task-card-overview": "Your task overview", "last-updated-on": "Last update on:", "material-code": "Material Code:", "see-more": "See more...", "see-less": "See less...", "empty-tasks": {"title": "You have no tasks in queue", "subtitle": "You have no tasks in queue. When data managers assign items to one of your plants, you will see them here."}, "task-status": {"additional-enrichment-request": "Additional enrichment request", "additional-edit-request": "Additional edit request", "approved": "Approved", "cancelled": "Cancelled", "completed": "Completed", "done": "Done", "draft": "Draft", "erp-error": "<PERSON><PERSON>", "erp-updated": "Erp Updated", "feedback-received": "<PERSON><PERSON><PERSON> received", "in-progress": "In progress", "integration-errors": "Integration errors", "in-your-queue": "In your queue", "rejected": "Rejected", "requires-more-information": "Requires more information", "submitted": "Submitted", "waiting-for-approval": "Waiting for approval", "waiting-for-enrichment": "Waiting for enrichment", "waiting-for-edit": "Waiting for edit", "waiting-for-erp": "Waiting for ERP", "waiting-for-external-system": "Waiting for external system", "waiting-for-feedback": "Waiting for feedback", "waiting-for-handling": "Waiting for handling", "waiting-for-manual-handling": "Waiting for manual handling", "waiting-for-starting": "Waiting for starting", "waiting-for-apply": "Waiting for apply", "aborted": "Aborted"}}, "relationship-popup": {"choose-relationship-types": "Choose one of the following relationship types", "new-relationship": {"duplicate": "Create a new duplicate relationship", "equivalence": "Create a new equivalence relationship", "interchangeable": "Create a new interchangeable relationship", "noRelationship": "Declare No Relation"}, "relationship-type": "Relationship type", "primary-material": "Select primary material", "materials-code": "Material code", "services-code": "Service code", "material-description": "Description", "material-stock-amount": "Stock", "material-consumption-amount": "Consumption", "material-order-amount": "Order", "note": "Relationship rationale", "button-cancel": "Cancel", "button-back": "Back", "button-request-creation": "Request relationship", "button-next": "Next", "button-confirm-selection": "Confirm relationship", "relationship-types": {"duplicate": "Duplication", "duplicate-label-materials": "Parts which are fully interchangeable in planning. Secondary materials shall be made obsolete and only the primary material will be maintained long term in the Master Data.", "duplicate-label-services": "Parts which are fully interchangeable in planning. Secondary services shall be made obsolete and only the primary service will be maintained long term in the Master Data.", "duplicate-secondary-materials": "Secondary materials in a duplicate relationship will no longer appear in duplicates management.", "duplicate-secondary-services": "Secondary services in a duplicate relationship will no longer appear in duplicates management.", "equivalence": "Equivalence", "equivalence-label-materials": "Parts which are fully interchangeable in planning, inventory management and availability. Created between materials of equal application. All materials are maintained in the system.", "equivalence-label-services": "Parts which are fully interchangeable in planning, inventory management and availability. All services are maintained in the system.", "interchangeable": "Interchangeability", "interchangeable-label-materials": "Parts which are one-way interchangeable in planning, inventory and availability. It is established between articles in which one can replace another, but not the other way around. All materials are maintained in the system.", "interchangeable-label-services": "Parts which are one-way interchangeable in planning, inventory and availability. All services are maintained in the system.", "norelationship": "No Relation", "norelationship-label-materials": "Parts which are not related in anyway. All materials are maintained the no relationship information is used to not show the items together anymore", "norelationship-label-services": "Parts which are not related in anyway. All services are maintained the no relationship information is used to not show the items together anymore"}, "equivalence-note-materials": "Choose the material which will be marked as equivalent", "equivalence-note-services": "Choose the service which will be marked as equivalent", "materials-note": "Choose one material as primary by clicking on ", "services-note": "Choose one service as primary by clicking on ", "relationship-note": {"duplicate": "Explain why you are creating this duplicate relationship", "equivalence": "Explain why you are creating this equivalence relationship", "interchangeable": "Explain why you are creating this interchangeable relationship", "norelationship": "Explain why you are declaring this no relation"}, "deletion-relationship-note": {"duplicate": "The following duplicate relationship is requested to be deleted", "equivalence": "The following equivalence relationship is requested to be deleted", "interchangeable": "The following interchangeable relationship is requested to be deleted", "norelationship": "The following no relationship is requested to be deleted"}, "steps": {"step1": "Specify type", "step2-materials": "Choose materials", "step2-services": "Choose services", "step3": "Review"}, "success-messages": {"relationship-requested": "Relationship requested"}, "md-statuses": "Master data status", "client": "Client", "only-golden-record": "Attention: Only golden record creation process will start as there not enough elements selected to start a relationship process", "create-golden-record": "Create golden record", "golden-record": "Golden record", "errors": {"select-only-one": "Client {{client}}: please select a master or select only one item", "golden-record-no-slave": "Client {{client}}: please remove golden records from being a slave", "no-golden-record": "Client {{client}}: please either unselect golden records from the list or the option to create a new one", "missing-primary": "Client {{client}}: please select one primary item", "missing-instance": "Please select at least one instance item (primary or secondary) in order to create golden record", "missing-primary-client": "Please select one primary item", "existing-process": "There is already a pending relationship creation process: "}, "primary": "Primary", "secondary": "Secondary", "interchangeable": "Interchangeable", "equivalent": "Equivalent"}, "assignment-popup": {"new-assignment": "Create a new editing task", "material-details": "Details", "material-code": "Material code", "material-description": "Description", "note": "Explain why you are creating these tasks", "plants-grouping": "Group # {{value}}", "button-cancel": "Cancel", "button-confirm": "Confirm edit request", "button-confirm-additional-edits": "Request additional edit", "invalid-combinations": "Groups: {{value}} has not plants selected. Please select at least one plant.", "errors": {"error-message": "Oops! Something went wrong.", "cannot-perform-assignment": "Cannot perform assignment at this moment"}, "additional-edit": {"material-details": "Details", "material-code": "Material code", "requester-note": "Requester note", "header-info": "{{assignedCount}} out of {{total}} materials have ben successfully assigned for editing. The remaining {{remainingCount}} materials were skipped because they already have an associated edit request.", "header-info-all-missed": "All the materials were skipped because they already have an associated edit request."}, "material-quality": "Quality", "material-assign-on-plants": "Assign to plants"}, "plant-extension-popup": {"material-plant-extension": "You are requesting on extension for material code", "request-plant-extension-materials": "Request a plant extension for material code", "request-plant-extension-services": "Request a plant extension for service", "actions": {"actions": "Actions", "add-plant": "Add plant", "save-plant": "Save plant", "add-more": "Add more", "cancel-request": "Cancel request", "request-extension": "Request extension", "loading": "Loading...", "no-data": "No items found", "cancel": "Cancel", "confirm": "Confirm", "go-to-request": "Go to request", "confirm-popup": {"delete": {"title": "Confirm to delete", "message": "Are you sure that you want to delete request?"}, "cancel": {"title": "Are you sure that you want to cancel the request?", "message": "If you cancel the request, all requests will be deleted"}, "change": {"title": "Are you sure you want to change master data?", "message": "If you change master data, all pending extensions for the current one will be lost"}, "delete-golden-record": {"title": "Confirm to delete", "message": "The golden record will be deleted, and all its instances will be detached from it. Instances data will remain unchanged"}, "delete-golden-record-response": {"deleted-successfully": "The Golden record is deleted successfully", "pending-processes": "There are still pending processes on some of the golden record instances"}, "processes-pending": {"title": "Processes pending", "message": "This golden record has an ongoing edit process. Confirming the deletion will terminate the edit process"}}, "open-details": "<PERSON><PERSON>", "confirmation-message": "Changes made for the source masterdata will be lost", "alert-button-collapse-true": "Hide warning", "alert-button-collapse-false": "Show Warning"}, "form": {"plant-information": "Plant specific information", "planing-information": "Logistic information", "masterdata-valuation": "Master data valuation", "storage-locations": "Storage locations", "plant-code": "Plant code", "plant-description": "Plant description", "mrp-group": "MRP Group", "mrp-type": "MRP Type", "mrp-controller": "MRP Controller", "purchasing-group": "Purchasing Group", "logistics-group": "Logistics Group", "lead-time-in-days": "Lead time in days", "reorder-point": "Reorder point", "safety-stock": "Safety stock", "min-safety-stock": "Min. Safety Stock", "max-stock-level": "Max. Stock Level", "standard-price": "Standard price", "price-unit": "Price unit", "lot-size": "Lot Size", "min-lot-size": "<PERSON><PERSON>", "multiple-valuations": "Supports multiple valuations", "currency": "<PERSON><PERSON><PERSON><PERSON>", "available-storages": "Available storages", "master-data-status": "Master Data Status", "previous-value": "Previous value:", "origin-material": "Origin material", "intrastat-code": "Intrastat Code", "control-code-consumption-taxes-foreign-trade": "Control Code Consumption Taxes Foreign Trade", "cfop-category": "Material CFOP Category", "period-indicator": "Period Indicator", "special-procurement-type": "Special Procurement Type", "group-availability-check": "Checking Group Availability Check", "valuation-class": "Valuation Class", "moving-average-price": "Moving Average Price", "price-control-indicator": "Price Control Indicator", "usage-material": "Usage Material", "other-attributes": "Other Attributes", "other-attributes-logistic": "Other Logistic Attributes", "plant-data-status": "Plant Data Status", "other-attributes-master-data-valuations": "Other Master Data Valuation", "profit-center": "Profit Center", "plant-old-material-number": "Plant Old Material Number", "lot-size-for-product-costing": "Lot Size For Product Costing", "no-storage-bins": "No storage bins", "storage-location": "Storage Location", "storage-bin": "Storage Bin", "storage-bins": "Storage bins", "applied-edits": "Applied Edits", "attribute": "Attribute", "edit-type": "Edit type", "current-value": "Current value", "certificate-type": "Certificate Type", "requester-note": "Requester Note", "fixed-lot-size": "Fixed Lot Size", "procurement-type": "Procurement Type", "ordering-costs": "Ordering Costs", "storage-costs-indicator": "Storage Costs Indicator", "rounding-value-for-purchase-order-quantity": "Rounding Value For Purchase Order Quantity", "unit-of-issue": "Unit Of Issue", "strategy-group": "Strategy Group", "critical-part": "Critical Part", "effective-out-date": "Effective Out Date", "country-of-origin": "Country Of Origin", "loading-group": "Loading Group", "planning-time-fence": "Planning Time Fence", "control-code-for-consumption-taxes-in-foreign-trade": "Control Code For Consumption Taxes In Foreign Trade", "consumption-mode": "Consumption Mode", "consumption-period-backward": "Consumption Period Backward", "consumption-period-forward": "Consumption Period Forward", "follow-up-material-code": "Follow-up Material Code", "serial-number-profile": "Serial Number Profile", "maintenance-status": "Maintenance Status", "max-lot-size": "<PERSON>", "draft-status": "* Draft status", "no-value": "<< no value >>"}, "errors": {"material-is-extended": "Material is already extended on the selected plant, please select another one", "material": {"empty-description": "Please fill in the following fields and try again:", "description-length": "Following descriptions exceed the maximum length (40 digits):"}}, "client": "Client", "found-duplicates": "Found duplicates for client: {{client}}", "no-selection": "Select a different client", "instance-already-exist": {"title": "Attention", "content": "To extend this material to plants of the selected client, you should use the following instance"}, "no-gr-instance": "No golden record found", "start-gr-creation": "Start golden record creation process", "table": {"edit": "Edit", "code": "Code", "description": "Description", "country": "Countries", "extend-me": "Extend this item"}, "no-duplicates-found": "No duplicates found for client: {{client}}", "request-gr-creation": {"new-gr": "a new golden record creation", "new-masterdata-with-plants": "a new master data creation per client, extended on the specified plants", "new-masterdata-client": "a new masterdata creation on the target client", "plant-extension-request": "a plant extension request on the target plant"}, "no-languages-available": "No languages are associated for this plant.", "plant-status-changed": "Changed", "confirm-extension-warning": "By confirming this extension you will trigger", "plant-extension-successful": "Plant Extension Successful"}, "plant-update-popup": {"material-plant-update": "You are requesting an update for the following plants, on material code ...", "request-plant-update": "Request a plant update", "actions": {"actions": "Actions", "edit-plant": "Edit plant", "update-plant": "Update plant", "cancel-request": "Cancel request", "request-update": "Request update", "loading": "Loading...", "no-data": "No items found", "cancel": "Cancel", "confirm": "Confirm", "confirm-popup": {"delete": {"title": "Confirm to delete", "message": "Are you sure that you want to delete request?"}, "cancel": {"title": "Are you sure that you want to cancel request?", "message": "If you cancel the request all requests will be deleted"}}}, "plant-update-successful": "Plant Update Request Created"}, "plant-approval-popup": {"approve-extension": "Approve Extension", "approve-update": "Approve Update", "approve-relationship": "Approve Relationship", "approval-note": "Write the reason of your decision", "form": {"plant-information": "Plant specific information", "planing-information": "Logistic information", "masterdata-valuation": "Master data valuation", "storage-locations": "Storage locations", "plant-code": "Plant code", "plant-description": "Plant description", "mrp-group": "MRP Group", "mrp-type": "MRP Type", "mrp-controller": "MRP Controller", "material-status": "Material Status", "purchasing-group": "Purchasing Group", "logistics-group": "Logistics Group", "lead-time-in-days": "Lead time in days", "reorder-point": "Reorder point", "safety-stock": "Safety stock", "min-safety-stock": "Min. Safety Stock", "max-stock-level": "Max. Stock Level", "standard-price": "Standard price", "price-unit": "Price unit", "min-lot-size": "<PERSON><PERSON>", "multiple-valuations": "Supports multiple valuations", "currency": "<PERSON><PERSON><PERSON><PERSON>", "available-storages": "Available storages"}, "actions": {"back-to-overview": "Back to overview", "cancel": "Cancel", "confirm": "Confirm", "reject": "Reject", "save": "Save"}, "confirm-popup": {"title": "Are you sure that you want to cancel the request?", "message": "If you cancel the request all requests will be deleted"}, "approve-extension-for-material-code": "Approve extension for material code", "approve-update-for-material-code": "Approve update for material code", "extension-process-details-for-material-code": "Extension process details for material code", "update-process-details-for-material-code": "Update process details for material code"}, "material-history-popup": {"modal-title": "History for Material", "active-processes": "Active Processes", "completed-processes": "Completed Processes", "open-details": "Open the details for", "no-data": {"header": "No Data Found", "description": "There is no audit log for this material"}, "open-process-details": "Open the details for", "completed-process": {"material_created": "Created", "material_updated": "Updated", "material_relationship_created": "Added relationship", "material_relationship_updated": "Updated relationship", "material_relationship_deleted": "Deleted relationship", "material_warning_managed": "Managed warning", "material_extension_created": "Extended", "material_extension_edited": "Changed extension", "material_deduplicated": "Deduplicated", "material_extension_imported": "Extension imported", "material_creation_imported": "Imported", "CREATION": "Created", "UPDATE": "Updated", "RELATIONSHIP_CREATION": "Added relationship", "RELATIONSHIP_UPDATE": "Updated relationship", "RELATIONSHIP_DELETION": "Deleted relationship", "WARNING_MANAGEMENT": "Managed warning", "EXTENSION_CREATION": "Extended", "EXTENSION_EDIT": "Changed extension", "DEDUPLICATE": "Deduplicated", "GR_CREATION": "GR Created", "GR_UPDATE": "GR Updated", "GR_SCRATCH_CREATION": "GR from Scratch Created"}, "active-process": {"material_created": "Creating", "material_updated": "Updating", "material_relationship_created": "Adding relationship", "material_relationship_updated": "Updating relationship", "material_relationship_deleted": "Deleting relationship", "material_warning_managed": "Managing warning", "material_extension_created": "Extending", "material_extension_edited": "Changing extension", "material_deduplicated": "Deduplicating", "CREATION": "Creating", "UPDATE": "Updating", "RELATIONSHIP_CREATION": "Adding relationship", "RELATIONSHIP_UPDATE": "Updating relationship", "RELATIONSHIP_DELETION": "Deleting relationship", "WARNING_MANAGEMENT": "Managing warning", "EXTENSION_CREATION": "Extending", "EXTENSION_EDIT": "Changing extension", "DEDUPLICATE": "Deduplicating", "creation": "Creation", "enrichment": "Enrichment", "fix-entity": "Custom Processes", "relationship": "Relationship", "duplicates": "Duplicates", "extension": "Extension", "waiting-for-handling": "Waiting for handling", "waiting-for-feedback": "Waiting for feedback", "erp-error": "ERP error", "waiting-for-enrichment": "Waiting for enrichment", "waiting-for-approval": "Waiting for approval", "waiting-for-erp": "Waiting for ERP", "waiting-for-erp-create": "Waiting for ERP creation", "waiting-for-erp-update": "Waiting for ERP update", "waiting-for-job-done": "Waiting for job done", "more-information-needed": "More information needed", "GR_CREATION": "Creating GR", "GR_UPDATE": "Updating GR"}, "gr-deleted": "Golden Record Deleted", "gr-deleted-by": "Deleted", "instance-detached": "Instance Detached", "detached-from": "Instance Detached from golden record: ", "aborted-processes": "Aborted Processes", "gr-for-instance-not-found": "No Golden Record found for the given instance. It might not yet been approved/created!"}, "process-details-popup": {"cancel": "Cancel", "field": "Field", "previous-value": "Previous value", "updated-value": "Updated value", "new-value": "New value", "details-for": "Details for : ", "completed": "Completed", "in-progress": "In Progress", "start-date": "Start date: ", "end-date": "End date: ", "what-has-been-modified": "What has been modified ?", "all-fields-modified": "All fields which have been modified", "what-happened": "What happened ?", "list-of-steps": "List of steps involved in the operation", "material-details": "Material details", "requested-material-details": "Details of requested material", "relationship-details": "Relationship details", "requested-relationship-details": "Details of requested relationship", "extension-details": "Create Extension details", "requested-extension-details": "Details of requested creation extension", "material-plant-changes": "Edit Extension details", "requested-material-plant-changes": "Details of requested updated extension", "no-data-available": "No data available", "action-types": {"additional-enrichment-request": "Additional enrichment request", "additional-edit-request": "Additional edit request", "approved": "Approved", "cancelled": "Cancelled", "completed": "Completed", "done": "Done", "draft": "Draft", "erp-updated": "Erp Updated", "feedback-received": "<PERSON><PERSON><PERSON> received", "in-progress": "In progress", "integration-errors": "Integration errors", "in-your-queue": "In your queue", "rejected": "Rejected", "requires-more-information": "Requires more information", "submitted": "Submitted", "waiting-for-edit": "Waiting for edit", "waiting-for-external-system": "Waiting for external system", "waiting-for-manual-handling": "Waiting for manual handling", "waiting-for-starting": "Waiting for starting", "creation": "Creation", "enrichment": "Enrichment", "fix-entity": "Custom Processes", "relationship": "Relationship", "duplicates": "Duplicates", "extension": "Extension", "waiting-for-handling": "Waiting for handling", "waiting-for-feedback": "Waiting for feedback", "erp-error": "ERP error", "waiting-for-enrichment": "Waiting for enrichment", "waiting-for-approval": "Waiting for approval", "waiting-for-erp": "Waiting for ERP", "waiting-for-erp-create": "Waiting for ERP creation", "waiting-for-erp-update": "Waiting for ERP update", "waiting-for-job-done": "Waiting for job done", "waiting-for-apply": "Waiting for apply", "more-information-needed": "More information needed"}, "approved-by": "Approved by", "assigned-to": "Assigned to", "completed-by": "Completed by", "created-by": "Created by"}, "status-page": {"about": {"title": "About This Site", "subtitle": "Here we will post updates about TAM's services availability."}, "all-statuses-up": "All Systems Operational", "not-all-statuses-up": "Some of the systems are not operational", "services": {"rabbitmq": "Administration", "tam4_auth": "Authentication and User Management", "tam4_data_loader": "Online data loading", "tam4_duplicate_report": "Duplicates report generation", "tam4_duplicates_management": "Duplicates management", "tam4_events_store": "Events Store", "tam4_file_export": "Export files", "tam4_folder_navigation": "Category explorer", "tam4_legacy": "Legacy content", "tam4_materials": "Viewing content", "tam4_notifications": "Notification via e-mail", "tam4_ontology": "Semantic capabilities", "tam4_relationships": "Create and manage relationship", "tam4_search": "Search", "tam4_warnings": "Warning management", "tam4_workflow": "Internal workflow", "tam4_worklists": "Handle and manage tasks"}, "status": {"up": "Operational", "down": "Not operational"}}, "controls": {"notFoundText": "No data available", "loading": "Loading..."}, "item-details": {"not-analyzed": "Not Analyzed Semantically", "confirm-activation-cta": "Activate", "confirm-activation": "Do you confirm activation?", "no-data": "No data available", "no-data-fields": "No available values", "no-data-subtitle": "Click on display hidden to show attributes without values", "relationship": {"type": "Relationship type", "description": "Description", "client": "Client", "code": "Master data code", "role": "Role", "no-data": "No relationship data available", "gr-instance": "Instance of golden record"}, "instances": {"description": "Description", "client": "Client", "code": "Master data code", "no-data": "No instances available", "countries": {"title": "Countries", "empty": "N/A", "many": "{{data}} and other {{other}} countries"}, "plants": {"title": "Plants", "empty": "N/A", "many": "{{data}} and other {{other}} plants"}, "client-code": "Client/Code", "action": "Operation", "unlink": "Unlink from Golden Record", "relationshipType": "Relationship type", "role": "Role", "relationships": "Relationships", "expand-all": "Expand all", "collapse-all": "Collapse all"}, "fields": {"4_TAM_PlantPlanningInformation": "Plant planning information", "4_TAM_StorageLocations": "Storage locations", "4_TAM_PlantTotalValues": "Plant totals", "4_TAM_MaterialValuation": "Material valuations", "tables": {"plantKey": "Plant", "reorderPoint": "Reorder point", "maximumStockLevel": "Max. stock level", "minimumSafetyStock": "Min. safety stock", "minLotSize": "Min lot size", "leadTimeInDays": "Lead time in days", "supportsMultipleValuations": "Repairable?", "storageLocation": "Storage location", "client": "Client", "code": "Code", "valuationClass": "Valuation class", "roundingValueForPurchaseOrderQuantity": "Rounding Value For Purchase Order Quantity", "unitOfIssue": "Unit Of Issue", "plantMaterialStatus": "Plant Material Status", "validFromDate": "Valid From Date", "plantDeletionFlag": "Plant Deletion Flag", "seriable": "Seriable", "followUpMaterialCode": "Follow Up Material Code", "logisticsHandlingGroup": "LogisticsHandlingGroup", "inHouseProductionTime": "In House Production Time", "individualColl": "Individual Coll", "controlKeyForQM": "Control Key For QM", "certificateType": "Certificate Type", "batchManagementFlag": "BatchManagement Flag", "fixedLotSize": "Fixed Lot Size", "maximumLotSize": "Maximum Lot Size", "orderingCosts": "Ordering Costs", "storageCostsIndicator": "Storage Costs Indicator", "strategyGroup": "Strategy Group", "criticalPart": "Critical Part", "effectiveOutDate": "Effective Out Date", "countryOfOrigin": "Country Of Origin", "loadingGroup": "Loading Group", "planningTimeFence": "Planning Time Fence", "consumptionMode": "Consumption Mode", "consumptionPeriodBackward": "Consumption Period Backward", "consumptionPeriodForward": "Consumption Period Forward", "valuationCategory": "Valuation category", "valuationType": "Valuation type", "stockQuantity": "Stock quantity", "consumptionQuantity": "Consumption quantity", "orderedQuantity": "Ordered quantity", "movingAveragePrice": "Moving average price", "standardPrice": "Standard price", "priceUnit": "Price unit", "priceControlIndicator": "Price control indicator", "totalStockQuantity": "Total stock quantity", "totalStockAmountEUR": "Total stock amount", "totalConsumptionAmountEUR": "Total consumption amount", "totalOrderedAmountEUR": "Total ordered amount", "movingAveragePriceEUR": "Moving Average Price", "standardPriceEUR": "Standard price", "mrpGroup": "MRP Group", "schedulingMarginKeyForFloats": "Scheduling margin key for floats", "mrpController": "MRP Controller", "Purchasing_Group": "Purchasing Group", "goodReceiptProcessingTimeInDays": "GR Processing Time In Days", "status": "Status", "safetyStock": "Safety stock level", "safetyStockLevel": "Safety stock level", "totalOrderedQuantity": "Total ordered quantity", "mrpType": "MRP Type", "lotSize": "Lot Size", "lotSizeForProductCosting": "Lot Size For Product Costing", "serviceValuationClass": "Service Valuation Class", "stockAmount": "Total stock amount (local currency)", "consumptionAmount": "Total consumption amount (local currency)", "orderedAmount": "Total ordered amount (local currency)", "purchasingGroup": "Purchasing Group", "currency": "Plant Currency", "x": "x", "y": "y", "uma": "Unit of measurement", "4_TAM_UnitOfMeasure": "Standard UoM"}, "materialId": "uuid", "materialKey": {"client": "Client", "materialCode": "Material code"}, "materialGroup": "Material group", "materialType": "Material type", "manufacturerDetails": {"manufacturerCode": "Manufacturer code", "manufacturerPartNumber": "Manufacturer part number", "manufacturerName": "Manufacturer name"}, "unitsOfMeasure": {"baseUnitOfMeasurement": "Base unit of measurement", "purchasingUnitOfMeasurement": "Purchasing unit of measurement", "weightUnitOfMeasurement": "Weight unit of measurement", "alternativeUnitsOfMeasure": "Alternative unit of measurement", "alternative": {"alternativeUnitOfMeasurement": "Alternative unit of measurement", "numerator": "Numerator", "denominator": "Denominator"}}, "basicData": {"deletionFlag": "Deletion flag", "materialStatusValidFromDate": "Material status valid from date", "industrySector": "Industry sector", "productDivision": "Product division", "oldMaterialNumber": "Old material number", "documentNumber": "Document number", "basicMaterial": "Basic material", "laboratoryDesignOffice": "Laboratory design office", "batchManagementRequirementIndicator": "Batch management requirement indicator", "authorizationGroup": "Authorization group", "crossPlantMaterialStatus": "Cross plant material status", "crossPlantPurchasingGroup": "Cross plant purchasing group", "genericItemGroup": "Generic item group", "externalMaterialGroup": "External material group", "hazardousMaterialNumber": "Hazardous material number"}, "dimensions": {"netWeight": "Net weight", "grossWeight": "Gross weight", "sizeDimension": "Size dimension"}, "descriptions": {"shortDescriptions": "Short description", "purchaseOrderDescriptions": "Purchase order description", "internalNoteDescriptions": "Internal note description", "longDescriptions": "Long description", "normalizedShortDescriptions": "Normalized short description", "normalizedLongDescriptions": "Normalized long description"}, "metadata": {"revision": "Revision", "lastUpdatedDate": "Last updated date", "createdDate": "Created date", "semanticallyAnalyzed": "Semantically analyzed"}, "completeness": "Completeness", "countries": "Countries", "plants": "Plants", "attachments": "Attachments", "image": "Image", "mdDomain": "Domain", "famiglia": "Family", "sottoFamiglia": "Subfamily", "specificaTecnica": "Specifica Tecnica", "edizione": "Edizione", "revisione": "Revisione", "dataCustom": "Data", "productHierarchy": "Product Hierarchy", "volume": "Volume", "volumeUnit": "Volume Unit", "internationalArticleNumberEanUpc": "International Article Number Ean <PERSON>c", "generic": "Generic Masterdata", "storageLocation": "Warehouse(s)", "priceUnit": "Price Unit", "purchasingGroup": "Purchasing Group", "supportsMultipleValuations": "Allows Multiple Valuations", "leadTimeInDays": "Lead time in days", "checkingGroupAvailabilityCheck": "Checking Group Availability Check", "controlCodeConsumptionTaxesForeignTrade": "Control Code Consumption Taxes Foreign Trade", "genericMasterdata": "Generic Masterdata", "mrpGroup": "MRP Group", "mrpType": "MRP Type", "mrpController": "MRP Controller", "logisticsGroup": "Logistics Group", "reorderPoint": "Reorder point", "safetyStock": "Safety stock", "minSafetyStock": "Min. Safety Stock", "maxStockLevel": "Max. Stock Level", "standardPrice": "Standard price", "lotSize": "Lot Size", "minLotSize": "<PERSON><PERSON>", "multipleValuations": "Supports multiple valuations", "currency": "<PERSON><PERSON><PERSON><PERSON>", "masterdataStatus": "Master Data Status", "originMaterial": "Origin material", "intrastatCode": "Intrastat Code", "cfopCategory": "Material CFOP Category", "periodIndicator": "Period Indicator", "specialProcurementType": "Special Procurement Type", "valuationClass": "Valuation Class", "roundingValueForPurchaseOrderQuantity": "Rounding Value For Purchase Order Quantity", "unitOfIssue": "Unit Of Issue", "plantMaterialStatus": "Plant Material Status", "validFromDate": "Valid From Date", "plantDeletionFlag": "Plant Deletion Flag", "seriable": "Seriable", "followUpMaterialCode": "Follow Up Material Code", "logisticsHandlingGroup": "LogisticsHandlingGroup", "inHouseProductionTime": "In House Production Time", "individualColl": "Individual Coll", "goodReceiptProcessingTimeInDays": "Good Receipt Processing Time In Days", "controlKeyForQM": "Control Key For QM", "certificateType": "Certificate Type", "batchManagementFlag": "BatchManagement Flag", "fixedLotSize": "Fixed Lot Size", "maximumLotSize": "Maximum Lot Size", "orderingCosts": "Ordering Costs", "storageCostsIndicator": "Storage Costs Indicator", "strategyGroup": "Strategy Group", "criticalPart": "Critical Part", "effectiveOutDate": "Effective Out Date", "countryOfOrigin": "Country Of Origin", "loadingGroup": "Loading Group", "planningTimeFence": "Planning Time Fence", "consumptionMode": "Consumption Mode", "consumptionPeriodBackward": "Consumption Period Backward", "consumptionPeriodForward": "Consumption Period Forward", "valuationCategory": "Valuation Category", "movingAveragePrice": "Moving Average Price", "priceControlIndicator": "Price Control Indicator", "usageMaterial": "Usage Material", "plantDataStatus": "Plant Data Status", "profitCenter": "Profit Center", "plantOldMaterialNumber": "Plant Old Material Number", "lotSizeForProductCosting": "Lot Size For Product Costing", "Min_Lot_Size": "<PERSON>", "Planned_Delivery_Time": "Planned Delivery Time", "materialCFOPCategory": "Material CFOP Category", "status": "Plant Specific Material Status", "maximumStockLevel": "Maximum Stock Level", "minimumSafetyStock": "Minimum Safety Stock", "serviceValuationClass": "Service Valuation Class", "schedulingMarginKeyForFloats": "Scheduling MarginKey For Floats", "plantCurrency": "<PERSON><PERSON><PERSON><PERSON>", "4_SDM_Currency": "<PERSON><PERSON><PERSON><PERSON>", "storageBin": "Storage Bin", "procurementType": "Special Procurement Type", "descriptionsTab": "Descriptions"}, "completeness": "Completeness", "warnings": "Warnings", "show-empty": "Display empty attributes", "hide-empty": "Hide empty attributes", "tabs": {"classifications": "Classifications", "relations": "Relationships", "instances": "Instances", "Tecnical Attributes": "Technical Attributes", "customer-default": "Customer fields", "missing-tab": "Missing tab"}, "semantically-analyzed-preview-popup": {"removed": "Removed", "changed": "Changed", "added": "Added", "field": "Field", "before": "Before", "after": "After", "action": "Action", "no-fields-changed": "No fields changed", "semantic-analysis-preview": "Semantic analysis preview"}, "errors": {"could-not-retrieve-attributes": "Could not retrieve item details attributes from ontology", "validators": {"alt-uom-int-value": "Please enter a numeric value for the {{field}} for row: {{rowNumber}}", "alt-uom-max-length": "The number of digits of the {{field}} must be <= {{max_length}} for row: {{rowNumber}}"}}, "image": {"remove-image": "Remove Image", "upload": "Upload", "image-size-warning": "Image size must be less then 5Mb", "image-format-warning": "Image must be of type png, jpeg or bmp"}, "confirm-activation-text": "The activation will include this Material in the AI scope, which may improve its categorization and attributes. Activation counts as Creation according to your licensing model?", "user-material-plant-mismatch": "You are not authorized to work on any of the material's plants", "edit": "Edit", "external-link": {"title": "Notice: You are leaving TAM!", "body": "{{external-url}} \nis not an official Creactives site, please verify if the domain <b>{{external-domain}}</b> is trusted. Never enter your credentials on an untrusted website.", "accept-label": "Trust domain and open", "reject-label": "Cancel"}}, "tabular-reports": {"email-sent": "<PERSON><PERSON>"}, "warning": {"title": "Warning"}, "task-notes-history-popup": {"modal-title": "Task notes history", "date": "Date", "user-id": "User", "comment": "Comment", "role": "Role"}, "link-unlink": {"golden-record-response": {"instances-have-ongoing-link-unlink-process": "At least one of the selected instance has an ongoing link unlink process", "instances-have-ongoing-enrichment-process": "At least one of the selected instance has an ongoing enrichment process", "gr-has-ongoing-enrichment-process": "Golden record has an ongoing golden record enrichment process", "invalid-clients": "there are already instances with the same client", "pending-processes": ""}}}, "login": {"signin-header": "Sign in", "reset-password-header": "Reset your password", "field-required": "This field is required.", "password-format-incorrect": "The password cannot start or end with blank space", "signin": "Sign in", "saml-signin": "Single sign-on via SAML", "saml-not-authorized": "User not authorized to login through SAML2.0", "reset": "Submit", "reset-password-needed": "You need to set a new personal password. After clicking on submit you will need to complete the login using the new password.", "error-while-contacting-auth-server": "Error occurred while contacting authentication server.", "forgot-password": "Forgot password ?", "request-reset-password": {"header": "Forgot password ?", "description": "Enter your email to reset your password:", "email-not-correct": "Please enter a valid Email.", "cancel": "Cancel"}, "email-submitted-successfully": "A password reset message has been successfully sent to your email address.", "reset-token-expired": "Reset password request is expired.", "reset-failed": "Something went wrong! The operation couldn't be completed.", "reset-success": "Your password has been reset successfully.", "product-name": "Technical Attribute Management", "unable-to-login": "Error while trying to login. User does not exist, password is incorrect or account expired!", "user-locked": "User has been locked!", "invalid-password": "The password must comply with the following:\n* have length between 16 and 64 characters\n* have at least 1 lowercase letter\n* have at least 1 uppercase letter\n* have at least 1 digit\n* have at least 1 special character\n* have less than 2 consecutive repeated characters (e.g. 111, aaa)\n* have less than 4 consecutive characters (e.g. 123, abc)\n* different from the login\n\nNote: the allowed special characters are the following (the first is a whitespace)\n␣ * . ! @ # $ % ^ & ( ) { } [ ] : ; < > , . ? / ~ _ + - = | \\", "waiting-sso-token": "Waiting for login/logout..."}, "pages": {"page-not-found": "Page not found", "page-forbidden": "Access Denied", "reset-password": {"not-equal": "New Password and Confirm Password are not equal!", "invalid-password": "Your password does not fulfill the complexity rules: 8-64 characters, no username, sequential and repetitive characters, contextual or common passwords", "invalid-password-new": "Your password does not fulfill the complexity rules: 8-64 characters, 1 uppercase, 1 lowercase, 1 digit, 1 special character, no username, sequential and repetitive characters, contextual or common passwords"}, "profile": {"title": "Your profile", "email": "Email", "company": "Company", "personal-information": "Personal information", "change-password": "Change Password", "security-and-login": "Security & login", "email-settings": "Settings", "fallback-languages": "Fallback description languages", "personal-information-page": {"small-title": "View your personal information", "first-name": "First Name", "last-name": "Last Name", "email": "Email", "company": "Company"}, "change-password-page": {"small-title": "change your account password", "not-equal-password": "New Password and Confirm Password are not equal!", "password-changed": "Password changed successfully", "new-password": "New password", "confirm-password": "Confirm password", "cto-submit": "Change Password", "cto-cancel": "Cancel", "old-password": "Current password", "invalid-password": "The password must comply with the following:\n* have length between 16 and 64 characters\n* have at least 1 lowercase letter\n* have at least 1 uppercase letter\n* have at least 1 digit\n* have at least 1 special character\n* have less than 2 consecutive repeated characters (e.g. 111, aaa)\n* have less than 4 consecutive characters (e.g. 123, abc)\n* different from the login\n\nNote: the allowed special characters are the following (the first is a whitespace)\n␣ * . ! @ # $ % ^ & ( ) { } [ ] : ; < > , . ? / ~ _ + - = | \\"}, "email-settings-page": {"small-title": "Review your email preferences", "no-email-settings": "No email settings loaded", "email-changed": "Email changed successfully", "ignore-creation": "Ignore creation emails", "ignore-approver": "Ignore approval emails", "ignore-edit": "Ignore edit emails", "ignore-enrichment": "Ignore enrichment emails", "ignore-classification-hint": "Ignore classification suggestion emails", "ignore-bulk-upload": "Ignore bulk upload emails", "cto-submit": "Submit", "cto-cancel": "Cancel"}, "fallback-languages-page": {"small-title": "Change your fallback description languages", "tooltip": "The fallback languages affect only material descriptions in the application and are considered only when the description for the user’s application language (UI language) is missing for the material.", "fallback-languages": "Fallback languages", "submit": "Update"}}}, "global-filters": {"domain": {"default-title": "Domain", "materials-title": "Materials", "services-title": "Services", "materials-golden-record-title": "Materials Golden Record Material", "services-golden-record-title": "Services Golden Record Service"}, "filter-heading": "Geographical filters", "filter-operator": "is", "category": "Category", "plant": "Plant", "country": "Country", "client": "Client", "preview": {"cancel": "Cancel", "reset": "Reset all", "apply": "Apply", "active-filters": "Active filters", "set-default-filter-successfully": "Default filter set successfully", "unset-default-filter-successfully": "Default filter removed successfully", "save-filters": "Save filters", "change-name-successfully": "Name changed successfully", "share-filter-successfully": "<PERSON><PERSON> shared successfully", "unshare-filter-successfully": "Filter set to private successfully", "saved-filters": {"tab-title": "Saved filters", "shared-with-me": "Shared with me", "private-only": "Created by me", "no-filters-found": "No saved filters found!", "filter": {"delete": "Delete", "apply-filters": "Apply filters", "public": "Public", "private": "Private", "plant": "Plant", "plants-text": "=0 {Plant has not been specified} =1 {plant} >1 {plants}", "plants-details": "=1 {is {{plantsList}}} =2 {are {{plantsList}}} >2 {are {{plantsList}} or other {{plantsRemainingCount}} plants}", "country": "Country", "countries-text": "=0 {Country has not been specified} =1 {country} >1 {countries}", "countries-details": "=1 {is {{countriesList}}} =2 {are {{countriesList}}} >2 {are {{countriesList}} or other {{countriesRemainingCount}} countries}", "client": "Client", "clients-text": "=0 {Client has not been specified} =1 {client} >1 {clients}", "clients-details": "=1 {is {{clientsList}}} =2 {are {{clientsList}}} >2 {are {{clientsList}} or other {{clientsRemainingCount}} clients}", "category": "Category", "taxonomy-name": "{{taxName}}", "materials-classified": " Materials are classified in ", "category-not-specified": "Category has not been specified", "tooltips": {"remove-as-default": "Click to remove as default", "set-as-default": "Set as default"}}}}, "popup": {"title": {"category": "Category Lookup", "plant": "Plant Lookup", "country": "Country Lookup", "client": "Client Lookup"}, "no-data-found": "There are no results matching your search"}, "favourite-tab": {"text": "No attribute in this section<br>Use Search to add categories here"}, "save-filters": {"title": "Save current filters", "save": "Save", "type-name": "Type a name", "mark-as-default": "Mark as default", "default-tooltip": "Set selected filters as default", "filter-section": {"is": "is", "are": "are", "plant": "Plant", "plants": "Plants", "country": "Country", "countries": "Countries", "client": "Client", "clients": "Clients", "category": "Category", "taxonomy-name": "{{taxName}}", "missing-plant-filter": "Plant has not been specified", "missing-country-filter": "Country has not been specified", "missing-client-filter": "Client has not been specified", "missing-categories-filter": "Material and Services has not been specified", "or-others": " or others {{count}} "}, "empty-state": {"title": "No filters set", "subtitle": "You can save your current filters after choosing values for one or more filters."}, "save-successfully": "Global Filters saved successfully", "error": {"name-required": "Provide a name for your filters"}, "delete-save-filters": {"delete-successfully": "Saved filters deleted successfully"}}}, "ab-inbev-erp-integration": {"errors": {"multiple-upload-error": "Cant upload multiple files.", "empty-requests-for-excel-creation": "The generation of the Excel file cannot be performed because no new requests have been generated since the last Excel."}}, "relationships": {"errors": {"material-already-secondary-in-other-relationship": "Selected material is already secondary in another relationship.", "secondary-material-is-already-primary-in-this-relationship": "Selected material is already primary in this relationship.", "material-is-already-in-another-relationship-delete-process": "Selected material is already in a delete process.", "duplicated-materials-found-in-requested-relationship": "Duplicate materials found in the requested relationship.", "interchangeable-relationship-contains-more-than-two-materials": "Relationship contains more than two materials.", "error-view": {"head-text": "Error while processing your request"}, "default": "Something went wrong, please try again later", "primary-is-missing": "Client {{client}}: please select one primary item", "multiple-primaries-with-same-client": "Client {{client}}: multiple primary items selected", "secondary-is-missing": "Client {{client}}: please select at least one secondary item. A relationship needs at least two materials.", "validation-failed": "Relationship couldn't be created due to an error", "material-is-already-in-another-relationship-process": "Selected material is already in a relationship process.", "material-is-already-in-another-relationship": "Selected material is already in a relationship.", "relationship-must-contains-at-least-two-materials": "Client {{client}}: please select at least two materials.", "substitute-or-substituted-is-missing": "Client {{client}}: please select at least one item that can substitute and one item that can be substituted", "relationship-must-contains-at-least-two-materials-no-client": "Please select at least two materials.", "substitute-or-substituted-is-missing-no-client": "Please select at least one item that can substitute and one item that can be substituted"}, "statistics": {"indicators": {"duplicate-count": "Duplication relationships", "equivalence-count": "Equivalence relationships", "interchangeable-count": "Interchangeability relationships", "norelationship-count": "No-relationships", "total-count": "Relationships created"}, "filters": {"title": "Explore your data", "input-created-from": {"label": "Created starting from", "placeholder": "Choose a date"}, "input-created-to": {"label": "Created until", "placeholder": "Choose a date"}, "input-relationship-type": {"label": "Relationship type", "placeholder": "Choose relationship type"}, "applay-filters": "Apply filters", "export-in-excel": "Export raw data in Excel", "calendar-config": {"apply": "Apply", "cancel": "Cancel", "clear": "Clear"}, "export": "Export raw data in Excel"}, "relationship-by-types": {"title": "Relationships by type", "page-empty": {"title": "No relationships have been created yet", "subtitle": "subtitle"}}, "relationship-by-materials": {"title": "No. of materials in relationships", "page-empty": {"title": "No relationship types by materials", "subtitle": "subtitle"}}, "reporting-export-successful": "Reporting exported successful", "dashboard-empty-title": "Your dashboard is empty", "dashboard-empty-details": "Try to change your filters or global filters"}, "relationshipType": {"all-types": "All types", "total": "All types", "duplicate": "Duplication", "equivalence": "Equivalence", "interchangeable": "Interchangeability", "norelationship": "No-relationship"}, "search": {"no-results": {"title": "No records found", "subheading": "There are no results matching your filters:", "suggestion-title": "Suggestions:", "line1": "Try a different filter combination", "line2": "Try to reset your filters"}}}, "materials": {"errors": {"plants": {"plant-already-existing": "Plant Already Existing", "price-ok-currency-ko": "Currency Missing Value", "duplicate-plants": "Duplicate Plants in Request", "invalid-num-plants-to-update": "Invalid Number of Plants to Update", "plant-not-editable-because-missing": "Plants Not Editable Because Missing"}, "material-not-found": "Material was not found!", "error-view": {"head-text": "Error while processing your request"}, "default": "Something went wrong, please try again later", "too-many-clients": "Cannot perform action on master data belonging to different clients", "unauthorized-for-edit": "User is not enabled to edit any of the chosen Master Data", "bulk-extension": {"not-found": "Bulk extension file could not be found with id {{id}}", "error-parsing-excel": "Error parsing excel", "get-plants-authorization": "Error getting plants authorization"}}, "indicators": {"material-count": "Materials", "service-count": "Services", "total-stock-value": "Total stock value", "total-consumption-value": "Total consumption value", "total-ordered-value": "Total ordered value"}, "countries-by-stock": {"title": "Top 10 Countries by stock (mln {{currencySymbol}})", "page-empty": {"title": "No countries", "subtitle": "No countries by stock"}}, "plants-by-stock": {"title": "Top 10 Plants by stock (mln {{currencySymbol}})", "page-empty": {"title": "No Plants", "subtitle": "No plants by stock"}}, "manufacturer-by-consumption": {"title": "Top 10 Manufacturers by consumption (mln {{currencySymbol}})", "page-empty": {"title": "No manufacturer", "subtitle": "No manufacturer by consumption"}}, "material-quality": {"title": "Material completeness", "page-empty": {"title": "No data", "subtitle": "No data to display"}, "confidence-values": {"GOOD": "GOOD", "FAIR": "FAIR", "POOR": "POOR"}}, "categories-by-duplicate": {"title": "Top 10 Categories by duplicate count", "page-empty": {"title": "No duplicates categories", "subtitle": "No duplicates categories to display"}}, "search": {"title": "No records found", "subheading": "There are no results matching your filters:", "suggestion-title": "Suggestions:", "line1": "Try a different filter combination", "line2": "Try to reset your filters"}, "dashboard-empty-title": "Your dashboard is empty", "dashboard-empty-details": "Your dashboard is empty. Here you can see materials statistics", "service-quality": {"title": "Service quality"}, "poor-item-quality": "Poor average item quality"}, "workflow": {"errors": {"cannot-start-process-plant-extension-already-requested": "Cannot start plant extension process, because it was already requested.", "error-view": {"head-text": "Error while processing your request"}, "default": "Something went wrong, please try again later", "cannot-start-process-materials-already-in-deduplication": "Unable to start the process. One or more masterdata are already in a deduplication process.", "cannot-start-process-changes-overlap": "Unable to start the process. There is a pending modification process on the same fields for this material. ", "cannot-start-process-instances-overlap": "Unable to start the process. There is a pending golden record process on one of the selected instances. ", "cannot-delete-gr-overlap": "Unable to delete the Golden Record. There is already a pending deletion process on it.", "cannot-delete-gr-instance-overlap": "Unable to delete the Golden Record. There is already a pending process on one of the selected instances.", "workflow-not-finished": "Workflow must be finished!", "relationship-must-contain-at-least-two-masterdata": "Relationship must contain at least two masterdata."}, "no-result-view": {"title": "No records found", "subheading": "There are no results matching your filters:", "suggestion-title": "Suggestions:", "line1": "Make sure that all filters are correctly.", "line2": "Try a different filter combination", "filter-combination-sugg": "Try a different filter combination", "reset-filters-sugg": "Try to reset your filters"}, "result-view": {"item-list": {"material-card-flex": {"material-code": "Material code", "countries": "Countries", "client": "Client", "category": "Category"}}}, "status-distribution": {"title": "Current Active {{processType}} Processes", "page-empty": {"title": "Empty data", "subtitle": "No active processes"}}, "process-statuses": {"creation": "Creation", "enrichment": "Enrichment", "fix-entity": "Custom Processes", "relationship": "Relationship", "duplicates": "Duplicates", "extension": "Extension", "waiting-for-handling": "Waiting for handling", "waiting-for-feedback": "Waiting for feedback", "erp-error": "ERP error", "waiting-for-enrichment": "Waiting for enrichment", "waiting-for-approval": "Waiting for approval", "waiting-for-erp": "Waiting for ERP", "waiting-for-erp-create": "Waiting for ERP creation", "waiting-for-erp-update": "Waiting for ERP update", "waiting-for-job-done": "Waiting for job done", "more-information-needed": "More information needed", "request-apply": "Request to apply", "waiting-for-apply": "Waiting for apply"}, "chart-top-categories": {"title": "Top Categories by Process Count", "page-empty": {"title": "No categories", "subtitle": "No Categories by Process"}}, "reporting": {"indicators": {"active-processes": "Active processes", "users": "Users involved", "completed-enrichment-processes": "Completed enrichment processes", "item-created": "Created materials", "materials-created": "Materials created", "services-created": "Services created"}, "countries-by-process": {"title": "Top Countries by process", "page-empty": {"title": "No countries by process", "subtitle": "There is no countries by process. All countries by process, you will see them here."}}, "plants-by-process": {"title": "Top plants by process", "page-empty": {"title": "No plants by process", "subtitle": "There is no plants by process. All plants by process, you will see them here."}}, "dashboard-empty-title": "Your dashboard is empty", "dashboard-empty-details": "La tua dashboard è vuota. Qui puoi vedere le statistiche del flusso di lavoro", "active-processes-by-type": {"title": "Active processes by type", "page-empty": {"title": "No active processes", "subtitle": "There is no active processes. If you have active filters, try a different filter combination."}}}}, "errors": {"materials": {"material-with-this-id-not-found": "Material was not found!", "file-type-not-allowed": "The selected file's type is not allowed! Accepting: {{acceptedTypes}}", "invalid-file-extension": "Empty extension is not allowed!", "file-size-exceeded": "File Size exceed {{maxSize}}"}, "default": "Something went wrong, please try again later", "save": "An error occurred while saving, please check your data and try again.", "auth": {"sso-enabled": "Operation not supported, an external SSO is configured for the login"}}, "file-export": {"exports-requested": {"tab-name": {"today": "Today", "current-month": "Current Month"}, "table-header": {"details": "Details", "status": "Status", "request-rate": "Request date", "export-rate": "Export date", "items-count": "Number of items", "actions": "Actions", "service": "Requested from"}, "item-type": {"materials": "Materials", "duplicate-groups": "Duplicate Groups", "relationships": "Relationships", "tasks": "Tasks"}, "status": {"ready": "Ready", "in-progress": "In Progress", "in-queue": "In Queue", "error": "Error", "canceled": "Canceled"}, "item": {"request-date": "Request date", "export-date": "export date"}, "pagination": {"first": "first", "previous": "previous", "next": "next", "last": "last"}, "empty": {"title": "You have no files to download", "subtitle": "Downloads you request from other services will appear here"}}, "actions": {"download": "Download file", "delete": "Delete file", "relaunch-file-processing": "Relaunch file processing", "abort": "Abort", "autorefresh-toggle": "Toggle auto-refresh", "reload": "Reload"}, "errors": {"error-view": {"head-text": "Error while processing your request"}, "default": "Something went wrong, please try again later"}}, "search": {"menu": {"edit": "Edit", "request-edit": "Request edit", "show-details": "Show details", "plant-extension": "Request extension", "plant-edit": "Request plant edit", "master-data-changelog": "Change log", "authorizations": {"action-not-enabled": "Action not enabled", "user-material-plants-mismatch": "You are not authorized to work on any of the material’s plants", "authorization-info-not-available": "Could not retrieve authorization information", "material-flagged-for-deletion": "Material is marked for deletion", "not-authorized": "Not authorized", "already-extended-on-all-user-plants": "Material is already extended on all plants enabled for your account", "already-extended-on-all-plants": "Master is already extended on all plants enabled", "material-plants-empty": "Master is not enabled on any plants", "cannot-edit-plant-for-services": "Cannot edit plant information for services", "no-user-material-plants-in-common": "No user material plants in common", "material-flagged-for-deletion-at-plant-level": "Material flagged for deletion at plant level", "no-relations-on-golden-record": "Relations cannot be created on golden record", "missing-required-data": "Missing required data", "master-need-to-be-created-first": "Master need to be created first", "already-existing-edit-process": "Ongoing edit process {{processCode}} requested by {{requester}} is blocking new edit processes", "not-allowed-on-golden-record": "Action not allowed on the golden record", "already-existing-relationship-deletion-process": "Ongoing relationship deletion process {{processCode}} requested by {{requester}} is blocking new edit processes"}, "additional-actions": "Additional actions", "material-versioning": "Show material versioning", "edit-golden-record": "Edit Golden Record", "delete-golden-record": "Delete Golden Record", "copy-material": "Copy Material", "link-instances": {"popup": {"title": "Link instances on Golden Record", "error": {"client": "The client {{clientId}} is already selected for link!", "golden-record": "A Golden Record is already selected", "instances": "The selected instance is already associated with a Golden Record", "invalid-clients": "The client {{clientId}} is already used by another instance of the Golden Record"}, "validate": {"golden-record": "A Golden Record is required to proceed!", "instances": "At least one instance is required to proceed!", "deletion": "The selected instance is deleted!"}}}}, "loading-view": {"searching": "Searching..."}, "no-result-view": {"title": "No records found", "subheading": "There are no results matching your search:", "suggestion-title": "Suggestions:", "line1": "Make sure that all words are spelled correctly.", "line2": "Try a different filter combination", "line3": "Try different search terms", "line4": "Try more general search terms", "line5": "Try fewer search terms."}, "result-view": {"item-list": {"material-card-flex": {"material-code": "Material code", "countries": "Countries", "client": "Client", "category": "Category", "copy-material-code": {"copy": "Copy the material code", "copied": "<PERSON>pied"}, "add-to-your-list": "Add to your list", "delete": "Deleted"}, "categories": {"head-text": "Categories"}, "filters": {"head-text": "Technical Attributes", "select-value": "(Select a value)", "submit": "Apply filters", "value-not-exists": "* Value  doesn't exist"}}, "loading": "Loading"}, "search-input": {"placeholder": "Type something..", "actions": {"advanced-search": "Advanced Search", "manual-selection": "Manual Selection", "export-materials": "Export materials", "export-selected-materials": "Select materials for export", "request-edit": "Request Edit", "create-relationship": "Create Relationship", "add-to-your-list": {"title": "Add materials to your list", "add": "Add", "cancel": "Cancel", "select-list": "Select a list"}, "remove-material-from-list": "Remove from list", "perform-action": "Perform Action", "perform-actions-on-all-materials": "Perform Action on All Materials", "bulk-edit": "Request bulk edit", "bulk-extend": "Request bulk extension", "link-instances": "Link <PERSON>"}, "input-tooltip": "You cannot update the search input while in selection mode !"}, "search-list": {"advanced-toggle": "Advanced", "paging-title": "Currently viewing: {{itemsInPage}} results of {{resultCount}}", "item-selected": "Selected {{selectedItems}} item", "items-selected": "Selected {{selectedItems}} items", "no-items-selected": "No items selected", "error-view": {"head-text": "Error while processing your request"}, "file-export": {"launch-file-export": "Export", "launch-file-export-successful": "Export request generated", "export-selected-materials-successful": "Export request generated using selected materials"}}, "advanced-search": {"form-group-title": "Additional filters", "material-code": "Master data code(s)", "created-between": "Materials created between", "modified-between": "Materials modified between", "material-group": "Master data group(s)", "manufacturer": "Manufacturer(s) code", "manufacturer-part-number": "Manufacturer part number(s)", "old-material-number": "Old material number(s)", "material-type": "Master data Type", "actions": {"reset-filter": "Reset filter", "apply-filter": "Apply filter"}, "placeholders": {"enter-value": "Enter value", "start": "Start", "end": "End"}, "cancel-popup": {"title": "Closing advanced filters", "content": "Closing the advanced filter's panel will remove all active filters.", "question": "Do you want to proceed?", "actions": {"confirm": "Yes", "cancel": "No"}}, "status": "Master data status", "obsolete": "Include obsolete master data", "tooltip": "You can search multiple codes using spaces: e.g., CODE1 CODE2 CODE3", "manufacturer-name": "Manufacturer(s) name", "gr-instances": "Include golden record instances", "options": "Search options", "deleted": "Include deleted master data", "gr-only": "Show only golden records", "statusTooltip": "Please select a client in global filter to get Master data statuses", "completeness": "Completeness", "customer-fields": "Customer <PERSON>"}, "warnings": {"warning-types": "Warning Types", "warning-type": {"goods-receipt-processing-time": "GR Processing Time is not filled in", "plant-specific-material-status": "Plant sp. matl status is not filled in", "completeness": "Material with poor completeness level", "material-group": "Material group is missing or invalid"}, "too-many-records": "Total number of results is {{resultCount}}, only the first 10.000 will be exported."}, "relationship": {"parent-material": "Go to parent material", "gr-instance": "Instance of "}, "new-list-popup": {"title": "New list", "actions": {"cancel": "Cancel", "save": "Save"}, "form": {"list-name": "List Name", "list-name-length": "Max 30 characters.", "list-name-chars-left": "{{value}} characters left.", "errors": {"max-length": "List name must be maximum 30 chars", "list-name-required": "The list name is required"}}}, "list-dropdown-menu": {"standard-search": "Standard search", "no-lists-available": "No lists available", "create-new": "Create new", "import": "Import", "delete": {"confirm-title": "Are you sure that you want to delete this list?", "cancel": "Cancel", "confirm": "Confirm"}, "active": "ACTIVE", "create-list-suggestion": "Try creating a new one with selected materials", "tooltips": {"public-list": "Make list public", "private-list": "Make list private", "delete-list": "Delete list"}, "lock-list": "List is now private", "share-list": "List is now public", "import-excel-popup": {"message1": "Please upload a file in which the first two columns represents client and master data code", "message2": "First line is reserved for header, which doesn't matter as long as it's there", "message3": "There could be multiple other columns in the excel which will be ignored", "message4": "Drag 'n' Drop the file in the box below, or simply click on it to start the upload", "drag-and-drop": "Drag and drop file here", "or": "or", "browse": "Browse for file"}}, "ignored-materials-popup": {"partial-import": {"title": "List created partially", "message": {"title": "The list was created partially.", "content-one-material": "1 row has been ignored.", "content": "{{count}} rows have been ignored."}}, "no-list-imported": {"title": "The list can't be imported!", "message": "All the materials were ignored. Make sure the codes are correct or try importing a new list."}, "import-success": {"title": "Your list has been successfully imported!", "message": "The search will be updated."}, "actions": {"close": "Close", "view-list": "View list", "export-ignored": "Export ignored"}}, "operations": {"completed": "Operation completed successfully"}, "errors": {"default": "Something went wrong, please try again later", "reached-max-list-limit": "You have reached the limit of active lists (25). Please archive some existing lists in order to create new ones.", "multiple-upload-error": "Can't upload multiple files"}, "pagination": {"first": "First", "previous": "Previous", "next": "Next", "last": "Last"}, "bulk-edit-popup": {"title": "Bulk Edit", "operation-details": {"title": "Operation details", "step": "Step 1 of 2", "subtitle": "Choose the bulk action(s) you wish to perform on the {{selectedItems}} Master Data items", "change": "Change ", "apply-to-empty": "Apply only to empty values", "insert-value": "Insert value", "choose-value": "Choose value", "review-changes": "Review changes", "editable-master-data": "You are authorized to edit only {{editableItems}} out of the {{selectedItems}} Master Data items", "warning-no-tech-attributes": "No fields available for edit"}, "review-operations": {"title": "Review operation", "step": "Step 2 of 2", "updated-fields": "Updated fields", "field-name": "Field Name", "field-action": "Field Action", "field-actions": {"change-to": "Change to", "fill-empty-with": "Fill empty values with"}, "field-value": "Field Value", "empty-value": "empty", "info-message": "Normalized descriptions will be recalculated and potentially updated for {{selectedItems}} materials. Do you wish to continue ?", "actions": {"back": "Back", "confirm-edit": "Confirm Bulk Edit"}}, "confirm-success": "Bulk edit changes were confirmed successfully", "operation-outcome": {"title": "Operation outcome", "actions": {"close": "Close", "close-and-search": "Show errors search", "close-and-search-info": "This operation will reset search filters", "back": "Back"}, "alert": {"success": "{{success}} of {{total}} correctly started"}, "table": {"header": {"client": "Client", "code": "Code", "outcome": "Outcome", "outcome-description": "Error description"}}}}, "form-inputs": {"validation-errors": {"required": "Field is required", "maxlength": "Max length surpassed", "valueRequiredWhenApplyOnlyToEmpty": "Values is required when applying change only to empty values."}}, "import-excel": {"title": "Import Excel"}, "bulk-extend-popup": {"title": "Please select the list of plants you want to extend on"}, "export-modal": {"header": "Export request configuration...", "labels": {"extractionType": "Extraction purpose", "exportTechnicalAttributes": "Export technical attributes", "lboxLanguages": "Languages", "lboxDescriptions": "Description", "tsField": "Field Selection"}, "exportTypes": {"EXPORT_V2_CONSULTATION": "Consultation", "EXPORT_V2_BULK_EDIT": "Massive Edit"}, "btn": {"close": "Close", "submit": "Submit"}, "confirm": {"header": "Warning! ", "message": "This operation could take some minutes to be completed. \nTo monitor the export go in the function \"Export\" from the main menu", "rejectBtn": "Cancel", "acceptBtn": "Confirm"}, "error": {"toManyItemsToExport": "To many item selected for export: {{count}}/{{limit}} ", "mustChoseALanguage": "Must choose at least one language", "mustChoseADescription": "Must choose at least one description", "bulkEditSingleClientOnly": "Can't proceed with the bulk-edit purpose extraction: only masterdata of the same client can be selected."}}, "notification-export-request": {"limit-exceeded": "The request exceede the maximum number of item exportable!", "success": "Request successfull", "bulk-edit-single-client-only": "Can't proceed with the bulk-edit purpose extraction: only masterdata of the same client can be selected."}}, "search-service": {"upload-details": {"errors": {"null-file": "No file has been chosen. Please choose an .xlsx file", "not-excel": "The selected file is not valid. Please choose an .xlsx file", "lines-number": "The file contains to many rows, the limit is 10k rows", "header-mismatch": "The file must have 2 columns", "missing-data": "The file has no data"}}}, "warnings": {"home": {"warnings": "warning", "warning-section": {"title": "Warning overview for", "materials": "materials", "warning-overview": {"tite": "Warning overview", "view-period": {"all": "All", "week": "Week", "month": "Month"}}, "sort-by": "Sort By", "count": "Count", "name": "Name", "services": "services"}}, "details": {"back-to-overview": "Back to overview", "details-for": "Warnings Details for {{ section }} / {{warningType}}", "by-country": {"title": "By Country"}, "by-plants": "By Plants", "by-category": "By Category", "coverage": "Failures", "categories": {"other": "Others"}}, "warning-type": {"material-type": "Material Type", "material-group": "Material Group", "classification": "Classification"}, "page-empty": {"title": "No Warnings", "subtitle": "No warnings have been generated in this section"}, "errors": {"error-view": {"head-text": "Error while processing your request"}, "default": "Something went wrong, please try again later", "no-materials-matched-the-filter": "No materials found to create the list", "materials-number-exceeded-the-limit": "Operation cannot be performed on more than 10.000 materials."}}, "worklists": {"messages": {"task-assigned-to-you": "The task has been assigned to you", "task-removed-assignment": "The task assignment has been removed", "fix-warnings": "Warning fix successfully", "fix-warnings-ignored": "Warnings ignored", "set-resolution": "Resolution set successful", "currently-sorting-by": "Currently sorting by", "all-tasks-assigned-to-you": "All tasks have been assigned to you", "approved-all-bulk-changes": "Bulk edits approval requested successfully", "approved-selected-bulk-changes": "The selected bulk edit changes were approved", "rejected-all-bulk-changes": "Bulk edits rejection requested successfully", "rejected-selected-bulk-changes": "The selected bulk edit changes were rejected", "successful-operation": "The operation was completed successfully"}, "errors": {"task-already-assigned": "The task was already assigned to another user", "page-not-found": {"title": "WORKLIST NOT FOUND", "subtitle": "WE ARE SORRY, BUT THE WORKLIST YOU REQUESTED WAS NOT FOUND"}, "unknown-error": "An unknown error occurred", "error-view": {"head-text": "Error while processing your request"}, "default": "Something went wrong, please try again later"}, "grDelete": {"dialog": {"lbl-message": "Do you want to confirm the Golden Record delete request?", "closeButton": "Close", "confirmButton": "Confirm"}, "modalDetails": {"title": "Confirm Golden Record delete"}}, "enrichment": {"empty": {"completed": {"title": "No tasks have been completed recently", "subtitle": "When you complete tasks, they will appear here."}, "in-queue": {"title": "You have no tasks in queue", "subtitle": "When data managers assign items to one of your plants, you will see them here."}, "waiting-for-approval": {"title": "You have no tasks waiting for approval", "subtitle": "When you complete any of your assigned tasks, they will appear here."}, "waiting-external-system": {"title": "You have no items waiting for ERP", "subtitle": "When your tasks have been approved, they will appear here while they are synchronized with external systems."}, "waiting-feedback": {"title": "You have no tasks waiting for feedback", "subtitle": "When you ask for more information, they will appear here"}}, "home": {"completed": {"title": "Completed tasks", "subtitle": "My recently completed tasks."}, "in-queue": {"title": "In your queue", "subtitle": "All items waiting for editing"}, "waiting-for-approval": {"title": "Waiting for approval", "subtitle": "My tasks waiting to be approved."}, "waiting-external-system": {"title": "Waiting for external system", "subtitle": "My tasks waiting to be exported."}, "waiting-feedback": {"title": "Waiting for feedback", "subtitle": "My tasks awaiting required feedback."}}, "assignment": {"last-updated-on": "Latest update on", "process": "Process", "material-code": "Material Code", "assign-removal": "Remove assignment", "assign-to-me": "Assign to me", "creator": "Creator", "assigned-to": "Assigned to", "editor": "Editor", "approver": "Approver", "relationship-type": "Relationship Type", "requester-note": "Requester note", "approver-note": "Approver note", "approver-suggestion": "Approver suggestion", "approver-suggestion-before": "Use", "approver-suggestion-after": "instead of creating a new material", "last-update": "Latest update", "countries": "Countries", "categories": "Categories", "and-more": "and {{value}} more", "materials-table": {"description": "Description", "material-code": "Material Code", "status": "ERP Status", "masterdata-status": "Masterdata status"}, "ignore": "Ignore", "mark-as-done": "<PERSON> as <PERSON>", "rejection-cause": "Rejected, use the following material code", "material-not-available": "material code not available", "bulk-operation": "Bulk Operation", "re-assign": "Reassign", "assign-all-to-me": "Assign all to me", "description-not-available": "Description not available", "ERP-note": "ERP note", "assign-success": "Material assigned to user", "created-on": "Created on", "note-history": "Show Note History..."}, "search": {"assigned-to-me": "Assigned to me", "assigned-to-roles": "Assigned to one of my roles", "no-records-found": "No records found.", "search-inputs": {"material-code": "Material code:", "process-code": "Process code:"}, "no-matching-results": "There are no results matching a material code or process id.", "no-matching-global-filters-results": "There are no results matching your current filter combination.", "suggestions": "Suggestions", "filter-combination-sugg": "Try a different filter combination", "diff-search-terms-sugg": "Try different code", "general-search-sugg": "Try searching in a different Worklist", "reset-filters-sugg": "Try to reset your filters", "search-form": {"set-filters": "Set filters...", "material-code": {"label": "Material code", "placeholder": "e.g 50011768"}, "process-code": {"label": "Process code", "placeholder": "e.g P-201807-00027"}, "task-type": {"label": "Type", "placeholder": "(select a type)"}, "warning-type": {"label": "Warning type", "placeholder": "(select a type)"}, "sorting-type": {"label": "Sorting", "sort-asc": "Show most recent first", "sort-desc": "Show last recent first"}, "clear-filters": "Clear filters", "bulk-operation-code": {"label": "Bulk operation code", "placeholder": "e.g BE-202105-00103"}, "toggle-matching-materials-only": {"label": "Global filter effects", "show-btn": "Show not matching materials", "hide-btn": "Hide not matching materials"}, "authorization-group": {"label": "Authorization Group"}, "material-group": {"label": "Material Group"}, "material-type": {"label": "Material Type"}, "completeness": {"label": "Completeness"}, "requester": {"label": "Requester"}}}, "status": {"approved": "Approved", "rejected": "Rejected", "more-information-needed": "More Information needed", "additional-enrichment-request": "Additional edit request", "in-progress": "In progress", "erp-updated": "ERP Updated", "erp-error": "ERP Error", "waiting-for-enrichment": "Waiting for editing", "enrichment-requested": "Edit requested", "waiting-for-approval": "Waiting for approval", "waiting-for-global-approval": "Waiting for global approval", "waiting-for-country-approval": "Waiting for country global approval", "waiting-for-erp": "Waiting for ERP", "waiting-to-be-processed": "Waiting to be processed", "done": "Done", "cancelled": "Cancelled", "draft": "Draft", "waiting-for-handling": "Waiting for Handling", "waiting-for-feedback": "Waiting for <PERSON><PERSON><PERSON>", "waiting-for-starting": "Starting", "aborted": "Aborted"}}, "duplicates": {"home": {"completed": {"title": "Completed tasks", "subtitle": "My recently completed tasks."}, "in-queue": {"title": "In your queue", "subtitle": "All items waiting for editing"}, "waiting-feedback": {"title": "Waiting for feedback", "subtitle": "My tasks waiting to be exported"}}, "empty": {"completed": {"title": "No tasks have been completed recently", "subtitle": "When you complete tasks, they will appear here."}, "in-queue": {"title": "You have no tasks in queue", "subtitle": "When data managers assign items to one of your plants, you will see them here."}, "waiting-feedback": {"title": "You have no tasks waiting for feedback", "subtitle": "When you ask for more information, they will appear here"}}, "group": {"messages": {"no-group-found": "No group found to inspect", "confirmation-message": "Confirm to mark job as done", "job-done-successful": "Job done successful"}, "actions": {"inspect-group": "Inspect group", "display-in-separate-groups": "Display in a separate groups", "send-enrichment": "Request edit", "create-rel": "Mark duplicate", "job-done": "Job done", "cancel": "Cancel", "confirm": "Confirm", "display-single-page": "Display single page", "send-to-waiting": "Send to waiting", "send-to-your-queue": "Send to your queue", "unassign": "Unassign"}, "consumption-amount": "Total consumption", "consumption-quantity": "Total consumption quantity", "created-by": "Created by", "assigned-to": "Assigned to", "approver-note": "Approver note", "requester-note": "Requester note", "last-update": "Latest update on", "process": "# Process: ", "affected-stock": "Affected stock", "total-amount-stock": "Total amount of stock", "affected-quantity": "Affected quantity", "total-amount": "Total amount of {{value}}", "total-consumption": "Total consumption", "total-consumption-value": "Total consumption value", "total-consumption-quantity": "Total consumption quantity", "amount-consumed": "Amount of consumed {{value}}", "confidence": "Confidence", "classification": "Classification", "accuracy-level": "Accuracy level", "of": "of", "metric-name": "This group has been created matching manufacturer name and code.", "basic-data": "Basic Data", "material-type": "Material Type", "technical-attributes": "Technical attributes", "fields-without-values": "Fields without values", "description": "Description", "unit-of-measure": "Unit of measure", "metric": {"Code": "This group has been created matching manufacturer name and code.", "TechnicalSheets": "This group has been created matching the technical sheet."}, "groups-all": {"card-filter-viewed": "Currently viewing: {{groupLength}} groups of {{totalGroups}}"}, "groups-exact-match": {"card-filter-viewed": "Currently viewing: {{groupLength}} groups of {{totalGroups}}"}, "groups-hidden": {"card-filter-viewed": "Currently viewing: {{groupLength}} groups of {{totalGroups}}"}, "financial-data": "Valuations", "affected-ordered": "Affected purchase orders", "display-valuations": "Valuations", "assigne-group": "Assign group", "total-ordered": "Total Purchase Orders", "total-ordered-quantity": "Total Purchase Orders quantity"}, "table": {"material-code": "Material code", "description": "Description"}, "confirm-popup": {"actions": {"send-to-waiting": {"title": "Send to Waiting", "message": "Are you sure to move this group in waiting status?"}, "cancel": "Cancel", "confirm": "Confirm"}}}, "approver": {"extension-approval-popup": {"title": "Approve extension", "approval-note": "Write the reason of your decision.", "cancel": "Cancel", "confirm": "Confirm", "reject": "Reject"}, "relationship-approval-popup": {"title": "Approve relationship", "approval-note": "Write the reason of your decision.", "cancel": "Cancel", "confirm": "Confirm", "reject": "Reject"}, "plant-update-approval-popup": {"title": "Approve plant update", "approval-note": "Write the reason of your decision.", "cancel": "Cancel", "confirm": "Confirm", "reject": "Reject"}, "reject-title": "Reject request", "request-more-info": "Request more information"}, "filter": {"currentView": "Currently viewing: {{visibleItemsCount}} tasks of {{totalItemsCount}}"}, "pagination": {"first": "First", "previous": "Previous", "next": "Next", "last": "Last"}, "task-type": {"extension_creation": "Extension", "extension_update": "Update extension", "enrichment": "Edit", "creation": "Creation", "relationship_creation": "Relationship", "duplicates": "Deduplication", "fix_entity": "Warning", "gr_creation": "Golden record creation", "gr_deletion": "Golden record deletion", "gr_enrichment": "Golden Record Edit", "gr_scratch_creation": "Golden record creation from scratch"}, "resolve-warning-popup": {"title": "Fix material group", "material-code": "Material code", "plant": "Plant", "status": "Status", "mmsta": "Plant-Specific Material Status", "webaz": "Goods Receipt Processing Time in Days", "hazardous-material": "Hazardous material number", "individual-coll": "Individual and collective requirements", "margin-key": "Scheduling margin key for floats", "part-weight": "Gross Weight", "plant-specific-gr-time": "Plant specific gr time", "plant-specific-material-status": "Plant specific material status", "minimum-ordering-quantity": "Minimum ordering quantity", "mrp-controller-definitions": "Select the new value", "purchasing-group-definitions": "Select the new value", "mrp-controller-definitions-previous": "Current value", "purchasing-group-definitions-previous": "Current value", "no-values": "(Select a value)", "serial-no-profile": "Serial number profile", "size-dimension": "Size/Dimension", "warranty": "Warranty", "individual-and-collective": "Individual and collective requirements", "individual": "Individual requirements only", "collective": "Collective requirements only", "in-house-production-and-delivery-time": "In house production and delivery time", "description": "Description", "manufacturer": "Manufacturer information", "current-material-group": "Current material group", "suggested-material-groups": "Suggested material groups", "manual-search": {"try-searching-manually": "None of these is correct? Try searching manually ", "here": "here"}, "actions": {"cancel": "Cancel", "confirm": "Confirm selection", "ignore-suggestion": "Ignore suggestion, keep your current value"}}, "mrp-controller-definitions-popup": {"mrp-controller-definitions": "MRP controller", "mrp-controller-definitions-previous": "Current value"}, "purchasing-group-definitions-popup": {"purchasing-group-definitions": "Purchasing group", "purchasing-group-definitions-previous": "Current value"}, "individual-coll-warning-popup": {"individual-and-collective": "Individual and collective requirements", "individual": "Individual requirements only", "collective": "Collective requirements only"}, "quick-fix-status-popup": {"title": "Fix the plant status for the material"}, "quick-fix-gr-time-popup": {"title": "Fix the goods receipt processing time"}, "scheduling-margin-key-definitions-popup": {"margin-key": "Scheduling margin key for floats"}, "reassign": {"title": "Reassign", "cancel": "Cancel", "confirm": "Confirm selection", "no-roles": "No roles found"}, "inspect": {"more-information-needed": "Edit", "additional-enrichment-request": "Additional edit request", "erp-error": "Check errors", "waiting-for-enrichment": "Edit", "waiting-for-approval": "Approve", "default": "Inspect", "approved": "Approved"}, "relationships": {"DUPLICATE": "DUPLICATE", "EQUIVALENCE": "EQUIVALENT", "INTERCHANGEABILITY": "INTERCHANGEABILITY", "INTERCHANGEABLE": "INTERCHANGEABLE", "NORELATIONSHIP": "NO RELATIONSHIP"}, "bulk-popup": {"bulk-approve": "Bulk Approve", "title": "Approve Bulk Edit", "approving-changes": "You are approving changes on {{materialsNumber}} materials:", "field": "Field", "action": "Action", "value": "Value", "material-details": "Material Details:", "material-code": "Material Code", "material-description": "Description", "write-reason": "Write the reason of your decision:", "reject": "Reject all", "reject-selected": "Reject selected ({{selectedCount}})", "confirm": "Approve all", "confirm-selected": "Approve selected ({{selectedCount}})", "cancel": "Cancel", "field-actions": {"change-to": "Change to", "fill-empty-with": "Fill empty values with"}, "title-extension_creation": "Approve Bulk Extension Creation", "title-extension_update": "Approve Bulk Extension Update", "title-enrichment": "Approve Bulk Edit", "info-bulk-extension-creation": "On {{uploadDate}}, {{uploadedBy}} uploaded the file {{fileName}} containing a total of {{numberOfExtensions}} plant extensions concerning {{distinctMasterdataCount}} masterdata and {{distinctPlantCount}} plants", "info-bulk-extension-update": "  On {{uploadDate}}, {{uploadedBy}} uploaded the file {{fileName}} containing a total of {{numberOfExtensionUpdates}} extension updates concerning {{distinctMasterdataCount}} masterdata and {{distinctPlantCount}} plants", "info-bulk-extension-enrichment": "On {{uploadDate}}, {{uploadedBy}} uploaded the file {{fileName}} containing a total of {{updatedDescriptions}} updates of descriptions for {{distinctMasterdataCount}} masterdata, concerning {{distinctLanguages}} languages.", "title-creation": "Approve Bulk Creation", "info-bulk-creation-summary": "On {{uploadDate}}, {{uploadedBy}} uploaded file {{fileName}} with {{distinctMasterdataCount}} new masterdata creations concerning  {{distinctPlantCount}} plants across: ", "popup-confirm-title": "Confirm", "popup-confirm-message": "Some of the selected Masterdata have duplicates. Are you sure you want to continue?", "approving-creation": "Approve"}, "authorization-group": "Authorization Group:", "approver-roles": "Approver Roles", "process": {"process-notes": "Process notes for {{processCode}}", "process-notes-empty": "No notes history to show"}}, "popups": {"titles": {"individual-coll": "Individual or collective is not filled in, please provide a value", "serial-no-profile": "Serial number profile is not filled in, please provide a value", "mrp-controller": "MRP controller is not filled in, please provide a value", "purchasing-group": "Purchasing group is not filled in, please provide a value", "minimum-ordering-quantity": "Minimum ordering quantity is not filled in, please provide a value", "material-group": "Material group is missing or invalid", "goods-receipt-processing-time": "GR Processing Time is not filled in, please provide a value", "plant-specific-material-status": "Plant sp. matl status is not filled in, please provide a value", "margin-key": "Scheduling margin key is not filled in, please provide a value", "size-dimension": "Size/Dimension is not filled in, please provide a value", "obsolete-suppliers": "Suppliers is obsolete", "missing-material-group": "Material group is missing or invalid", "part-weight": "Part's weight is not filled in, please provide a value", "contracts": "Contract is expired", "expired-frame-contract": "Contract is expired", "warranty": "Warranty field is not filled in, please provide a value", "hazardous-material": "Missing data for hazardous materials", "inconsistent-mrp-value": "Inconsistent MRP Controller, please provide a value", "inconsistent-purchasing-group": "Inconsistent Purchasing Group, please provide a value", "cancel-title": "Cancel Task", "cancel-message": "Are you sure to cancel the process?", "gr-creation-approval": "Approve Golden Record Creation"}, "actions": {"confirm": "Confirm", "cancel": "Cancel", "reject": "Reject", "delete": "Delete", "delete-gr": "Delete golden record"}, "only-integer": "*Only numeric digits are allowed"}, "ab-inbev-integration": {"home-page": {"historic-downloads": {"historical-downloads": "Historical downloads", "export": "Export", "author-download": "Exported by", "actions": "Actions", "request-date": "Request date", "client": "Client", "number-of-requests": "Number of requests", "status": "Status", "empty-page": {"title": "No files have been generated", "subtitle": "Downloads generated from other services will appear here"}}, "historic-uploads": {"historical-uploads": "Historical uploads", "upload": "Upload", "author-updated": "Uploaded by", "request-date": "Request date", "number-of-requests": "Number of requests", "file-status": "Status", "empty-page": {"title": "No files have been generated", "subtitle": "Downloads generated from other services will appear here"}, "summary": "Summary"}, "actions": {"choose-file-to-upload": "Choose a file to upload", "download": "Download", "see-details": "See details", "generate-excel": "Generate Excel", "stream-pending-processes": "Pending Processes"}, "indicators": {"historic-downloads": {"title": "Downloads", "subtitle": "Previously generated files"}, "historic-uploads": {"title": "Uploads", "subtitle": "Previously uploaded files"}}}, "file-details-page": {"upload-summary": "Upload summary", "go-back": "Back to download center", "refresh": "Refresh", "processing": "Processing", "step-details": {"uploaded-by": "Uploaded by", "uploaded-on": "Uploaded on", "number-of-rows": {"label": "Number of rows", "description": "{{value}} lines"}, "number-of-processes": {"label": "Number of processes", "description": "{{value}} processes"}, "elapsed": {"label": "Elapsed", "description": "{{value}} ms"}, "message": {"label": "Message", "description": "{{value}} ms"}}, "step-validation": {"process-started": "Process Started", "file-uploaded": "File Uploaded", "file-validated": "File Validated", "process-validation": "Process Validation", "process-confirmation": "Upload Confirmation", "notify-requesters": "Notify Requesters", "completed": "Completed"}}, "file-status": {"processing": "Processing", "processed": "Processed", "error": "Error", "generating": "Generating", "ready-to-be-exported": "Ready to be exported", "waiting-for-feedback": "Waiting for feedback", "feedback-received": "<PERSON><PERSON><PERSON> received"}, "errors": {"error-view": {"head-text": "Error while processing your request"}, "multiple-upload-error": "Cant upload multiple files", "empty-requests-for-excel-creation": "The generation of the Excel file cannot be performed because no new requests have been generated since the last Excel", "empty-requests-for-excel-pending-creation": "The generation of the Excel file for pending processes cannot be performed because no new requests have been generated since the last Excel", "unable-to-retrieve-user-details": "The generation of the Excel file cannot be performed because user information cannot be retrieved at this moment. Please try again later. ", "default": "Something went wrong, please try again later", "user-not-authorized-on-any-client": "No processes available for user's clients!"}, "upload-details": {"errors": {"missing-material-code": "Missing Material Number for row: {{param0}}", "missing-client": "Missing Client for row: {{param0}}", "client-cell-mismatch": "Client in the excel does not correspond to master data client for row: {{param0}}", "client-unauthorized": "No permission for client for row: {{param0}}", "no-client-authorized": "User is not authorized on any client", "multiple-clients-uploaded": "Multiple clients uploaded; upload one excel for each client", "duplicate-material-code": "Duplicated Material Number for row: {{param0}}; each material needs to have a different Material Number", "existing-material-code": "Invalid Material Number for row: {{param0}}; the material already exists", "error-checking-material-code": "Error while checking Material Number for row: {{param0}}; material creation was skipped, please try to upload again this row", "null-file": "The file is missing", "not-excel": "The uploaded file is not an Excel file", "sheet-number": "The file must have exactly {{param0}} sheets.", "internal-error": "Internal error: {{param0}}", "cannot-contact-materials": "Cannot contact materials service; please try again later", "header-mismatch": "Header mismatch, Expected: '{{param0}}' at position: {{param1}}. Found: '{{param2}}'", "invalid-request-status": "Process with id: {{param0}} is in status: {{param1}} and not {{param2}}, as expected", "all-processes-invalid": "All processes are invalid", "cannot-contact-workflow": "Cannot contact materials service; please try again later"}, "warnings": {"send-email-failure": "Could not notify: {{param0}}"}}}, "languages": {"sq": "Albanian", "ar": "Arabic", "be": "Dutch (Flemish)", "bg": "Bulgarian", "ca": "Catalan", "zh": "Chinese", "hr": "Croatian", "cs": "Czech", "da": "Danish", "nl": "Dutch", "en": "English", "et": "Estonian", "fi": "Finnish", "fr": "French", "de": "German", "el": "Greek", "hi": "Hindi", "hu": "Hungarian", "is": "Icelandic", "it": "Italian", "ja": "Japanese", "ko": "Korean", "lv": "Latvian", "lt": "Lithuanian", "mk": "Macedonian", "ms": "Malay", "mt": "Maltese", "no": "Norwegian", "nb": "Norwegian Bokmål", "nn": "Norwegian Nynorsk", "pl": "Polish", "pt": "Portuguese", "ro": "Romanian", "ru": "Russian", "sk": "Slovak", "sl": "Slovenian", "sr": "Serbian", "es": "Spanish", "sv": "Swedish", "th": "Thai", "tr": "Turkish", "uk": "<PERSON><PERSON><PERSON>", "vi": "Vietnamese", "iw": "Hebrew"}, "language": "Language", "notifications": {"errors": {"invalid-cron-expression": "Invalid CRON Expression", "invalid-email": "Invalid email", "invalid-report-name": "Invalid report name", "tabular-report-not-found": "Tabular report not found."}, "scheduled-reports": {"table-columns": {"report": "Report", "description": "Description", "scheduling": "Scheduling", "recipients": "Recipients", "created-by": "Created By", "modified-by": "Modified By", "creation-date": "Creation date", "last-modification": "Last modification", "sql-command": "SQL Command"}, "actions": {"edit": "Edit", "delete": "Delete", "send-now": "Send now"}, "title": "Scheduled Reports", "add": "Add", "no-data-available": "No data available!", "modal": {"create-report": "Create Report", "edit-report": "Edit Report", "confirm": "Confirm deletion", "prompt": "Are you sure you want to delete this report?"}}}, "bulk-extension": {"page-title": "Upload a bulk-extension file.", "download-instructions": "This file can be downloaded from the search page.", "drag-drop": "Drag and drop file here or", "browse-button-label": "Browse Files", "notes-placeholder": "Notes", "upload-button-label": "Upload and Validate", "popup": {"close-button": "Close", "create-workflows-button": "Create Workflows", "cancel-creation": "Cancel Creation"}, "errors": {"error-title": "Please fix these validation errors and re-upload the file!", "message-info": "and the message is", "row-number": "The error occurred in row number", "empty-search": "Unable to generate bulk extension file for empty search request!", "no-results": "Unable to generate bulk extension! No results available", "max-size": "Unable to generate bulk extension! Search results size exceeds the max limit: {{limit}} ", "not-found": "Bulk extension file could not be found", "error-parsing-excel": "Error parsing excel", "get-plants-authorization": "Error getting plants authorization"}, "success": {"loading": "Creating Workflows", "success-title": "Create Workflows", "operation-codes-message": " The operation has been taken in charge and ", "processes-creation": " processes are under creation. Please use these bulk operation codes in the worklist to identify them: ", "oc-new-extensions": "-New Extensions: ", "oc-extension-updates": "-Extension Updates: ", "oc-localized-description": "-Localized description updates: ", "file-stats-description": " processes will be created in total to extend, or update extensions, ", "file-stats-of": "of ", "file-stats-to": " masterdata to ", "file-stats-plants": " plants.", "file-stats-localization-description": " processes will be created in addition to update localized description of masterdata.", "file-stats-end-note": "These processes may be approved individually and/or in bulk"}}, "massiveEdit": {"status": {"A_STARTING": "Start", "A_FORMAL_FILE_VALIDATION": "Formal file Validation", "A_EXTRACTING_DATA": "Extracting Data", "A_VALIDATING_DATA": "Validating Data", "H_CONFIRM_DATA": "User - Confirm changes", "A_EDIT_PROCESS_GENERATION": "Edit process Generation", "H_COMPLETED": "Completed", "H_ABANDONED": "Abandoned", "H_TERMINATED": "Terminated", "END": "End"}, "fieldStatus": {"IGNORED": "Ignored", "CONFIRMED": "Confirmed", "INVALID": "Invalid", "NOT_CHANGED": "Not Changed", "NOT_EDITABLE": "Not Editable", "VALIDATED": "Validated", "DRAFT": "Draft"}, "list": {"searchForm": {"actions": {"label": "Label", "toggleAutorefresh": "Autorefresh", "startProcess": "Upload", "search": "Search", "toggleAutorefresh_false": "Enable Autorefresh", "toggleAutorefresh_true": "Disable Autorefresh ({{interval}})"}, "dateFilter": {"label": "Uploaded Date", "placeholder": "Select date range"}, "status": {"label": "Status", "placeholder": "Select status"}, "user": {"label": "Uploaded By", "placeholder": "Select user"}}, "table": {"columns": {"processId": "Id", "uploadedBy": "Upl. By", "uploadDate": "Upl. Date", "filename": "File Name", "processSummary": "Summary", "currentStatus": "Status", "records": "No. Records", "actions": " ", "worklist-ref": "Bulk Process ID", "groups": "groups"}}, "modals": {"startProcessModal": {"header": "Start a new process...", "btnConfirm": "Confirm"}, "processCreatedDialog": {"successMsg": "Process {{processId}} created with success!", "btnClose": "Close", "btnOpenProcess": "Open"}}}, "processDetail": {"actions": {"show_history": "History", "toggle_more_info": "More Info", "backToList": "Back to process list", "export": "Export", "abandon": "Abandon", "reload": "Refresh", "reload_auto": "Refresh ({{countdown}})", "save": "Save", "next": "Next", "H_CONFIRM_DATA": {"next": "Start Proc.", "next_clear": "Start edit processes", "next_dirty": "Waiting for user to save processes"}}, "processInfo": {"labels": {"processId": "Process Id", "uploadedBy": "Uploaded by", "uploadDate": "Upload Date", "file_name": "File Name", "currentStatus": "Current Status", "records": "Records", "bulkProcessId": "Bulk Process ID", "groups": "Groups"}, "history": {"active": "Active", "tmsStart": "Started", "tmsEnd": "Ended", "tms_insert": "Insert Date", "user_insert": "Insert By", "tms_update": "Update Date", "user_update": "Update By", "title": "History", "show-all-substate": "Show all substate"}}, "steps": {"generic": {"default-message-text": "Waiting for automatic process to update the data..."}, "H_CONFIRM_DATA": {"labels": {"masterdata_header": "Masterdata to update", "masterdataId": "TAM ID", "client_code": "Client / Code", "masterdata_header_missing_materials": "Loaded Masterdata without changes are excluded"}, "fieldTable": {"toggleFieldFilter": {"true": "Hide AI-ignored fields", "false": "Show AI-ignored fields"}, "fieldName": "Field", "changes": "Changes", "status": "Status", "newValue": "New Value", "oldValue": "Old Value"}}, "A_EDIT_PROCESS_GENERATION": {"waiting-message": "Waiting for enrichment processes to be generated..."}, "END": {"H_TERMINATED_message": "Process terminated automatically! Some critical error occurred.", "H_COMPLETED_message": "Process completed!", "H_ABANDONED_message": "Process Abandoned by the user", "table": {"header": {"materialKey": "Code", "materialDescr": "Description", "processCode": "Outcome"}, "messages": {"process-not-generated": "No process generated", "process-generated": "Process <b>{{materialProcessId}}</b> started"}}}}}, "errors": {"extraction-purpose-metadata-mandatory": "file uploaded is not valid. Please export a massive edit template.", "language-metadata-mandatory": "Language metadata property is mandatory.", "customer-id-metadata-mandatory": "Customer Id metadata property is mandatory.", "unable-to-read-input-file": "Unable to read input file.", "input-file-mandatory": "The input excel file is mandatory.", "user-id-mandatory": "The User Id is mandatory.", "language-mandatory": "The current language is mandatory.", "validation-error": "Validation error", "unable-to-save-input-file": "Unable to read input file.", "unable-to-start-workflow": "Unable to start workflow."}, "messages": {"title": {"extraction-purpose-error": "Wrong extraction type", "extraction-purpose-success": "Extraction type", "language-error": "Wrong language", "language-success": "Language", "customer-id-error": "Wrong Customer ID", "customer-id-success": "Customer ID", "sheet-number-error": "Wrong sheets number", "sheet-number-success": "Sheets number", "wrong-column-position-error": "Wrong column position", "wrong-column-position-success": "Column position"}, "body": {"extraction-purpose-error": "The export template is not valid.", "extraction-purpose-success": "The export template is valid.", "language-error": "The language is not valid.", "language-success": "The language is valid.", "customer-id-error": "The Customer ID is not valid.", "customer-id-success": "The Customer ID is valid.", "sheet-number-error": "The file has the wrong number of sheets.", "sheet-number-success": "The file has the correct number of sheets.", "wrong-column-position-error": "A column is in the wrong position.", "wrong-column-position-success": "All columns are in the right place."}}, "internalStatus": {"PENDING": "PENDING", "WORKING": "WORKING", "COMPLETED": "COMPLETED", "ERROR": "ERROR"}}, "generic": {"tables": {"norowmessage": "No row find", "loading-message": "Loading rows"}, "messages": {"operation-success": "Operation Successful!", "error": {"title": "A generic error occurred", "body": "Some generic error occurred on server. Please contact support"}, "no-user-rights": {"title": "Forbidden!", "body": "The user don't have the rights to do the action!"}}}, "admin-page": {"enel-plugin": {"menu": {"root": "<PERSON><PERSON>", "check-auth-tests": "Auth Group Tests"}}}, "no-changes-allowed": {"messages": {"error": {"title": "No changes to apply", "body": "None of the masterdata changes were allowed/on non editable fields."}}}, "supplier": {"action": {"add": "Add", "create": "Create Supplier", "update": "Update Supplier", "delete": "Delete Supplier", "list": "Supplier List"}, "button": {"add": "Add", "upload-supplier": "File upload", "download-template": "Download template", "details": "Details", "edit": "Edit", "delete": "Delete"}, "title": {"empty": "No suppliers", "list": "List of Suppliers", "edit": "Edit Supplier", "create": "Create Supplier"}, "popup": {"title": "Supplier Exist", "message": "Supplier code already existing in this client:{{client}} {{code}} "}, "response_server": {"operation-finished-successfully": "Operation finished successfully", "supplier_already_in_use": "Supplier already in use", "supplier_already_exist": "Supplier already exist {{client}} {{code}}", "supplier_not_present": "Supplier should not be empty"}, "delete-confirm": {"title": "Delete the Supplier", "message": "This supplier is never used in TAM. Do you confirm deletion?"}, "excel-import": {"report": "Supplier import from excel report", "created": "Created", "updated": "Updated", "deleted": "Deleted", "error": "Error", "row": "Row Number"}, "client": "Client", "manufacturerCode": "Manufacturer Code", "locationId": "Location ID", "createdDate": "Created Date", "createdBy": "Created By", "lastUpdatedDate": "Last Updated Date", "updatedBy": "Updated By", "manufacturerName": "Manufacturer Name", "vatRegistrationNumber": "Registration Number", "contactLastName": "Contact Last Name", "manufacturerPartNumber": "Manufacturer Part Number", "countryCode": "Country Code", "address": "Address", "name2": "Name2", "name3": "Name3", "name4": "Name4", "supplierGroupNormalized": "Supplier Group Normalized", "supplierNameNormalized": "Supplier Name Normalized"}, "smartCreation": {"dynamic-components": {"no-value-selected": "<<No value selected>>"}, "attachments": {"label": "Attachments", "addAttachment": "Add an attachment", "no-attachment": "No attachments available", "no-instance-attachments": "No instance attachments", "openTextEnabled": "Attachments for this client are managed in a document managed system, please open the item details of the instance to retrieve the attachments"}, "thumbnails": {"label": "Thumbnails", "no-thumbnails": "No thumbnails available from instances"}, "classification": {"label": "Classification"}, "descriptions": {"shortDescriptions": "Short description", "longDescriptions": "Long description"}, "fields": {"mandatoryFields": "There are mandatory fields", "fetch-value-error": "Unable to fetch error for the field {{fieldName}}"}, "validation": {"checkFields": "Check fields", "checkWarnings": "Check warning on fields"}, "categories": {"dialog": {"closeButton": "Close", "codeFilter": "Code", "confirmButton": "Confirm", "descriptionFilter": "Description", "filter": "Filter", "title": "Select a category", "warning": "Select a category before confirming", "currentSelection": "Selected:"}, "title": "Categories", "emptyCategories": "No Categories Found", "selectManually": "select manually", "manuallySelectedCategoriesWarning": "The categorizations about <b>{{categories}}</b> that TAM proposed have a 95% chance of being correct. Are you sure you want to select another one?", "reset": "Reset Category"}, "domain": {"label": "Domain"}, "completeness": {"label": "Completeness"}, "materialImage": {"label": "Material image"}, "materialName": {"label": "Material name"}, "goldenRecordCode": {"label": "Golden record code"}, "Golden_Record_Code": "Golden record code", "Client_Master_Data_Code": "Client master data code", "smartCreation": {"title": "Smart creation", "previous": "Previous", "next": "Next", "search": "Search", "validate": "Validate", "plantData": {"add": "Add", "notFound": "No plants found", "duplicatePlantKeyError": "Plant {{plant_key}} already present", "plant-added": "Plant {{plantKey}} data added/updated.", "plant-removed": "Plant {{plantKey}} data removed."}, "validation": {"error": "Validation error"}, "backToValidationDialog": {"header": "Some operations are pending", "message": "Going back to Validation will reset all fields. Do you want to proceed?", "acceptLabel": "Yes", "rejectLabel": "No"}, "copy-material": {"info": "Copied from "}}, "smartValidation": {"mandatory": "Field is mandatory", "autocomplete": {"selected-items-group": "Selected", "filtered-group": "Available items"}, "no-items-in-list": "No items in list", "domain": {"label": "Domain"}, "title": "Smart Validation", "search": "Search", "reset": "Reset", "next": "Next", "description": {"label": "Description"}, "error": "Error", "normalizedDescriptions": {"title": "Normalized descriptions"}, "select": "Select", "selectFilters": {"label": "Select Filters", "similarMaterials": "Similar Materials", "notFound": "No filter found for the provided description"}, "similarMaterials": {"client": "Client", "description": "Description", "hideDuplicates": "Hide duplicates", "hideGoldenRecordInstances": "Hide golden record instances", "hideObsolete": "Hide obsolete", "includeGoldenRecords": "Include golden records", "includeMaterialsFromOtherClients": "Include materials from other clients", "materialCode": "Material code", "notFound": "No material found", "country": "Country", "plants": "Plants"}, "technicalAttributes": {"title": "Technical attributes"}, "client": {"label": "Client"}, "gr": {"toCreate": "To create", "plant": "Plant to extend", "noplant": "Plant not found", "client": "Client", "plantPanel": {"title": "Select plant data for client ", "add": "Add", "close": "Close", "error": "Check required fields"}, "enableCreation": "Enable creation", "autoExtensionPlant": "Auto extension plant", "confirmOpenModal": {"header": "Some operations are pending", "message": "The PLANT section is partially filled. Not confirmed data will be lost, would you proceed?", "acceptLabel": "Yes", "rejectLabel": "No"}}, "gr-governed-attribute": "Field is managed on the Golden Record", "mandatory-error": "{{field}} is empty", "manufacturerCodeIsAlreadyInUse": "Value is already in use in combination with another value, on client", "manufacturerPartNumberIsAlreadyInUse": "Value is already in use in combination with another value, on client", "attributeCodeIsAlreadyInUse": "Attribute is already in use in combination with another attribute, on client", "attributeBrandIsAlreadyInUse": "Attribute is already in use in combination with another attribute, on client", "plantRequired": "Plant is required", "materialCodeIsAlreadyInUse": "Material code is already in-use", "oldMaterialNumberIsAlreadyInUse": "Code is already in-use", "copy": {"typeError": "Copy not available on Golden Record"}}, "smartSummary": {"title": "Smart Summary", "previous": "Previous", "create": "Create", "submit": "Submit"}, "modalDetails": {"title": "Save material", "hideFieldNoValueButton": "Hide empty attributes", "showFieldNoValueButton": "Display empty attributes", "alternativeUnitOfMeasurement": {"add": "Add", "close": "Close", "notFound": "No Alternative units of measurement found", "checkFields": "Check all fields", "of": "of", "confirm": "Confirm"}, "closeButton": "Close", "validateButton": "Validate", "confirmButton": "Confirm", "confirmationMessage": "Material saved correctly", "confirmationMessageProcessCode": "The request was successfully created with process code: ", "savedTitle": "Save Material", "savedConfirmButton": "Ok", "saveButton": "Save", "deleteButton": "Delete", "approveButton": "Approve", "rejectButton": "Reject", "updateButton": "Edit", "additionalInfoButton": "Additional Information", "confirmDialog": {"checkFields": "The field is required!"}, "header": {"view": "View", "details": "Details", "edit": "Edit", "gr-edit": "Edit Golden Record", "gr-deletion": "Delete Golden Record", "approval-gr-deletion": "Approve Golden Record Deletion", "approve": "Approve edit", "enrichment": "Enrichment", "instance-of": "Golden Record Reference", "description": "Description", "gr-edit-details": "Edit Golden Record Process Details", "gr-approve": "Approve Golden Record", "gr-enrichment": "Golden Record enrichment", "process-details-edit": "Enrichment Process Details"}, "linkUnlink": {"link-instances": "Link instances", "unlink-instances": "Unlink instances", "search-instances": "Search instances for link", "noItemsSelected": "Please select at least one material to link", "confirm": {"title": "Link/Unlink instances on Golden Record", "message": "Are you sure you want to continue?", "linkMessage": "The following material(s) will be added to {{goldenRecordCode}} via a Golden Record Edit process", "unlinkMessage": "The following material(s) will be unlinked from {{goldenRecordCode}} via a Golden Record Edit process", "confirmButton": "Confirm"}, "search": {"btn": "Search", "label": "Code or description", "emptyText": "Please insert some text for search", "placeholder": "Insert material code or description to search"}, "description": "Description", "approval-gr-deletion": "Approval of Golden Record Deletion"}, "validation": {"thumbnailsRequired": "Please select an image from the available thumbnails"}}, "suggestionsAttribute": {"copyTooltip": "Copy value"}, "rejectPossibilities": {"incorrectDataGroup": "Incorrect/Not proper master data group", "missingManufacturerInfo": "Missing manufacturer information", "incorrectNamingConvention": "Incorrect naming convention", "translationNotProvided": "Translation not provided", "potentialDuplicate": "Potential duplicate", "incorrectUoM": "Incorrect UoM", "missingTechnicalAttributes": "Missing technical attributes or catalogue code", "other": "Other"}, "createdMaterial": {"dialog": {"title": "Material Created", "confirmButton": "Ok", "confirmationMessage": "The request was successfully created", "confirmationMessageProcessCode": "The request was successfully created with process code: "}}, "confirmCreation": {"dialog": {"title": "Add a comment for the approver", "closeButton": "Cancel", "confirmButton": "Confirm", "lbl-message": "Request notes"}}, "approval": {"title": "Approval Request", "note": "Note", "ignoredCategories": "Ignored Categories", "taxonomy": "Taxonomy", "additionalInformation": "Additional Information", "suggestCategories": "Suggest Categories", "currentCategory": "Category Selected", "otherInfo": "Other Info", "noInformation": "No information available", "smartValidation": "Smart Validation:", "smartCreation": "Smart Creation:", "smartSummary": "Summary:", "rejectLabel": "Reject", "approveLabel": "Approve", "additionalInfoLabel": "Additional Info", "reject": {"dialog": {"title": "Reject", "chooseRejectCause": "Choose a cause (required)", "comment": "Comment", "closeButton": "Cancel", "confirmButton": "Confirm", "terminateButton": "Terminate", "similarMaterial": "Similar Material", "note": "Please note that all changes will be lost"}}, "additionalInfo": {"dialog": {"title": "Additional Info"}, "supplier": {"action": {"add": "Add", "create": "Create Supplier", "update": "Update Supplier", "delete": "Delete Supplier", "list": "Supplier List"}, "button": {"add": "Add", "upload-supplier": "File upload", "details": "Details", "edit": "Edit", "delete": "Delete", "download-template": "Download Template"}, "title": {"empty": "No suppliers", "list": "List of Suppliers", "edit": "Edit Supplier", "create": "Create Supplier"}, "popup": {"title": "Supplier Exist", "message": "Supplier code already existing in this client:{{client}} {{code}} "}, "response_server": {"operation-finished-successfully": "Operation finished successfully", "client_not_found": "Client not found", "manufacturer_code_not_found": "Manufacturer code not found", "manufacturer_name_not_found": "Manufacturer name not found", "supplier_already_exist": "Supplier already exist {{client}} {{code}}", "supplier_already_in_use": "Duplicate Supplier", "supplier_not_present": "Supplier Not present"}, "delete-confirm": {"title": "Delete the Supplier", "message": "This supplier is never used in TAM. Do you confirm deletion?"}, "excel-import": {"report": "Supplier import from excel report", "created": "Created", "updated": "Updated", "deleted": "Deleted", "error": "Error", "row": "Row Number"}, "client": "Client", "manufacturerCode": "Manufacturer Code", "locationId": "Location ID", "createdDate": "Created Date", "createdBy": "Created By", "lastUpdatedDate": "Last Updated Date", "updatedBy": "Updated By", "manufacturerName": "Manufacturer Name", "vatRegistrationNumber": "Registration Number", "contactLastName": "Contact Last Name", "manufacturerPartNumber": "Manufacturer Part Number", "countryCode": "Country Code", "address": "Address", "name2": "Name2", "name3": "Name3", "name4": "Name4", "supplierGroupNormalized": "Supplier Group Normalized", "supplierNameNormalized": "Supplier Name Normalized", "field_required": "Field is required!"}}, "approve": {"dialog": {"title": "Approve"}}}, "confirmedCreation": {"dialog": {"title": "Additional info correctly added to the material"}}, "confirmedEdit": {"dialog": {"title": "Edit material"}, "no-changes-allowed": {"messages": {"error": {"title": "title", "body": "body"}}}, "supplier": {"action": {"add": "Add", "create": "Create", "update": "update", "delete": "Delete", "list": "List"}, "button": {"add": "Add", "upload-supplier": "Upload Supplier", "details": "Details", "edit": "Edit", "delete": "Delete", "download-template": "Download Template "}, "title": {"empty": "empty", "list": "List", "edit": "Edit", "create": "Create"}, "popup": {"title": "title", "message": "message"}, "response_server": {"operation-finished-successfully": "operation successfully completed", "supplier_not_present": "supplier not present", "supplier_already_exist": "duplicate supplier", "supplier_already_in_use": "Duplicate Supplier"}, "delete-confirm": {"title": "title", "message": "message"}, "excel-import": {"report": "Report", "created": "Created", "updated": "update", "deleted": "Deleted", "error": "Error", "row": "row"}, "client": "Client", "manufacturerCode": "Manufacturer Code", "locationId": "Location ID", "createdDate": "Date of Creation", "createdBy": "Created by", "lastUpdatedDate": "Last Update Date", "updatedBy": "Updated by", "manufacturerName": "Manufacturer Name", "vatRegistrationNumber": "VAT Number", "contactLastName": "Contact Last Name", "manufacturerPartNumber": "Manufacturer Part Numbert", "countryCode": "Country Code", "address": "Address", "name2": "Name 2", "name3": "Name 3", "name4": "Name 4", "supplierGroupNormalized": "Supplier Group Normalized", "supplierNameNormalized": "Supplier Name Normalized"}, "confirmedLink": {"dialog": {"title": ""}}, "confirmedUnlink": {"dialog": {"title": ""}}}, "materialModal": {"changes": {"title": "Changes", "attribute": "Attribute", "previousValue": "Previous value", "editType": "Edit type", "currentValue": "Current value", "viewer": {"title": "Detail", "open": "Open"}}, "linkunlink": {"title": "Instances to Link or Unlink", "label": {"link": {"tooltip": "The material will be added to the Golden Record", "badge": "Added"}, "unlink": {"tooltip": "The material will be removed from the Golden Record", "badge": "Removed"}}}}, "materialCodeGr": {"label": "Golden Record Code"}, "copyMaterial": {"label": "Copied from"}, "show-comments": "Show process comments:", "confirmedLink": {"dialog": {"title": "Link Golden Record Edit process has been created"}}, "confirmedUnlink": {"dialog": {"title": "Unlink Golden Record Edit process has been created"}}}, "role-management": {"list": {"pageTitle": "Role management", "searchPlaceholder": "Search by name…", "roleName": "Role name", "linkedAccount": "Linked Accounts", "linkedService": "Linked Services", "linkedGroup": "Linked Groups"}, "deleteConfirmationHeader": "Delete confirm", "deleteConfirmationMessage": "Do you want to delete the selected role?", "noSelection": "Select at least one role!", "deleteSuccess": "Deletion successful", "createSuccess": "Create successful", "filters": {"addNew": "New", "actions": {"title": "Actions", "delete": "Delete"}, "export": "Export"}, "edit": {"basicProfile": "Basic", "EDIT": "Edit role", "advancedProfile": "Advanced", "basic": "Edit basic data", "roles": "Edit roles", "administratorProfile": "Administrator", "createRole": "Create role", "name": "Name", "placeholderName": "Role name", "roleId": "Role ID", "placeholderRoleId": "ID", "internal": "Internal", "profileType": "Profile Type", "comment": "Comment", "accounts": {"title": "Accounts", "notfound": "Account not found"}, "groups": {"title": "Groups", "notfound": "Groups not founds"}, "services": "Services", "requesterRoles": "Requester Roles", "requesterCreationCustomRoles": "Requester Creation Custom Roles", "requesterEditCustomRoles": "Requester Edit Custom Roles", "approverRoles": "Approver Roles", "approverCustomRoles": "Approver Custom Roles", "duplicateCustomRoles": "Duplicates Custom Roles", "enabledActions": "Enabled Actions", "buttons": {"close": "Close", "create": "Create", "save": "Save", "edit": "Edit", "clone": "<PERSON><PERSON>"}, "checkRequiredFields": "Add required fields!", "DETAILS": "Role details", "CREATE": "Create new role", "DETAIL": "Role details", "CLONE": "<PERSON><PERSON> role", "organizations": "Organizations", "organizations_available": "Organizations available", "organizations_selected": "Organizations selected"}, "service-list": {"legenda": "<PERSON>a", "enableService": "Enable service", "disableService": "Disable service", "resetService": "Restore service", "enableServiceAndMakeAdmin": "Enable and make administrator", "cercaServiziPlaceholder": "Search services...", "azioni": "Actions", "deseleziona": "Deselect", "seleziona": "Select", "auth_level": {"enabled": "Enable", "disabled": "Disable", "administrator": "Administrator", "notAffected": "Not involved", "readOnly": "Read-only"}, "readOnlyService": "Read-only"}}, "custom-roles": {"list": {"pageTitle": "Custom Roles", "searchPlaceholder": "Search by name...", "name": "Name", "processType": "Process Type", "description": "Description"}, "deleteConfirmationHeader": "Delete confirm", "deleteConfirmationMessage": "Do you want to delete the selected custom role?", "noSelection": "Select at least one role!", "filters": {"addNew": "New", "actions": {"title": "Actions", "delete": "Delete"}, "export": "Export"}, "edit": {"processType": {"workflowCreation": "Creation Workflow", "workflowEnrichment": "Enrichment Workflow", "workflowApproval": "Approver Workflow", "workflowDuplicate": "Duplicates Workflow"}, "createGroup": "Create group", "roleId": "Role ID", "name": "Role name", "namePlaceholder": "Role name", "profileType": {"title": "Profile type", "emptyOption": "Select..."}, "comment": "Comment", "buttons": {"cancel": "Cancel", "create": "Create", "save": "Save"}, "requiredInformationInformation": "Enter the required information"}, "editConfirmHeader": "Edited custom role information for \"{{role}}\" ({{roleId}}) will be saved", "editConfirmMessage": "Do you want to proceed?", "createConfirmHeader": "The custom role \"{{role}}\" will be created", "createConfirmMessage": "Do you want to proceed?"}, "accounts-management": {"create": {"title": "New user", "subtitle": "Create new user from scratch..."}, "filters": {"addNew": "New", "actions": {"title": "Actions", "delete": "Delete", "resendActivationMail": "Resend activation email", "forceResetPassword": "Reset password", "changeRoleTo": "Change Role…", "changeRole": {"add": "Add", "change": "Edit", "remove": "Delete"}, "assignRole": "Modify Role(s)", "ClearSelection": "Clear Selection", "anonymize": "Anonymize"}, "export": "Export"}, "noSelection": "Select at least one user!", "resendActivationMailConfirmationHeader": "Confirm resend activation", "resendActivationMailConfirmationMessage": "Do you want to resend the activation email for the selected accounts?", "deleteConfirmationHeader": "Delete confirm", "deleteConfirmationMessage": "Do you want to delete the selected accounts?", "forceResetPasswordConfirmationHeader": "Confirm reset password", "forceResetPasswordConfirmationMessage": "Do you want to reset the password for the selected accounts?", "changeRoleToConfirmationHeader": "Confirm role change", "changeRoleToConfirmationMessage": "Do you want to change the role for the selected accounts?", "checkRequiredFields": "Enter required fields!", "list": {"pageTitle": "Users", "searchPlaceholder": "Search by name…", "displayName": "Display Name", "login": "<PERSON><PERSON>", "group": "Group", "securityRole": "Role", "status": "Status", "system": "Sys"}, "form": {"noCountryAssociated": "No associated country", "noRoleAssociated": "No roles associated", "requiredInformationInformation": "Please enter the required information", "invalidEmail": "Invalid email", "label": {"password": "Password", "system": "System", "id": "Id", "givenName": "Name", "lastName": "Surname", "displayName": "Display name", "securityRoles": "Roles", "inheritedRoles": "Inherited Roles", "inheritRoles": {"empty": "No roles"}, "ignoreCreationEmail": "Ignore email creation", "ignoreEnrichmentEmail": "Ignore email enrichment", "ignoreApprovalEmail": "Ignore email approver", "email": "Email", "company": "Company", "comment": "Comment", "login": "<PERSON><PERSON>", "group": "Group", "country": "Country", "language": "Language", "decimalSeparator": "Decimal Separator", "department": "Department", "internal": "Internal", "securityRoles_available": "Available", "securityRoles_selected": "Selected", "status": "Status", "targetUsers": "Target User(s)", "inheritedOrganizations": "Inherited Organizations", "organizations": "Organizations", "organizations_available": "Available", "organizations_selected": "Selected"}, "button": {"cancel": "Cancel", "save": "Edit", "create": "Create"}, "resetPassword": "Reset password", "lastLogin": "Last Access", "memberSince": "Creation date", "title": {"organizationConfiguration": "Organization", "actions": "Actions", "securityInfo": "Security Information", "sendEmailPermissions": "Email sending permissions", "credentials": "Credentials"}, "info": {"login": "Please note: a generated password will be sent to the user who will need to change their password upon first login."}, "default": {"country": "Select Country...", "selectMessage": "Select...", "securityRoles": "Select security roles..."}, "client": "Clients", "businessUnit": "Business unit", "requesterRoles": "Requester Roles", "requesterCreationCustomRoles": "Custom Roles creation", "requesterEditCustomRoles": "Edit Custom Roles", "approverRoles": "Approver", "approverCustomRoles": "Custom Roles Approver", "duplicatesCustomRoles": "Custom Roles Duplicates", "enabledActions": "Enabled Actions", "blockAccount": "Block account", "unblockAccount": "Unblock account", "resendActivationMail": "Resend activation email", "fieldMinLength": "Minimum length {{minlength}} characters.", "requiredField": "Required field!", "treeEmptyMessage": "No data available", "decimalSeparatorLang": {"en-US": "en-US -> 123,456.789", "it-IT": "it-IT -> 123.456,789"}}, "deleteSuccess": "Deletion successful", "createSuccess": "Creation successful", "genericSuccess": "Operation performed successfully", "blockAccountConfirmationHeader": "Confirm account block", "blockAccountConfirmationMessage": "Do you want to block this account?", "unblockAccountConfirmationHeader": "Confirm account unlocking", "unblockAccountConfirmationMessage": "Do you want to unlock this account?", "title": {"EDIT": "Edit account", "DETAILS": "Account details", "CREATE": "Create new account", "DETAIL": "Account details", "CLONE": "Clone account"}, "edit": {"buttons": {"close": "Close", "create": "Create", "save": "Save", "edit": "Edit", "continue": "Continue", "clone": "<PERSON><PERSON>"}, "basic": "Basic Info", "additionalData": "Additional Info", "preferences": "Preferences", "security": "Security"}, "userEditConfirmHeader": "Edited account information for \"{{displayName}}\" ({{login}}) will be saved", "userEditConfirmMessage": "Do you want to proceed?", "userCreateConfirmHeader": "The account \"{{displayName}}\" ({{login}}) will be created", "userCreateConfirmMessage": "Do you want to proceed?", "dialog": {"buttons": {"acceptLabel": "Accept", "rejectLabel": "Reject"}, "applyRole": {"header": "Choose the role(s) to apply"}}, "confirmation-messages": {"ResetPassword_list": {"header": "Confirm reset password", "message": "Do you want to reset the password for these {{selectedCount}} accounts?"}, "ResendActivationMail_list": {"header": "Confirm resend activation", "message": "Do you want to resend the activation email for these {{selectedCount}} accounts?"}, "LockAccount_list": {"header": "Confirm accounts lock", "message": "Do you want to block these {{selectedCount}} accounts?"}, "UnlockAccount_list": {"header": "Confirm accounts unlocking", "message": "Do you want to unlock these {{selectedCount}} accounts?"}, "AssignRole_list": {"header": "Confirm accounts role assignation", "message": "Do you want to assign role(s) <strong>{{rolesToApply}}</strong> to these {{selectedCount}} accounts?"}, "AssignGroup_list": {"header": "Confirm accounts group assignation", "message": "Do you want to assign group {{selected.name}} to these {{selectedCount}} accounts?"}, "Delete_list": {"deleteConfirmationHeader": "Delete confirm", "deleteConfirmationMessage": "Do you want to delete these {{selectedCount}} accounts?", "header": "Delete confirm", "message": "Do you want to delete these {{selectedCount}} accounts?"}, "Anonymize_list": {"header": "Confirm accounts anonymize", "message": "Do you want to anonymize these {{selectedCount}} accounts?"}}, "anonymizeSuccess": "Anonymization successful"}, "general": {"messages": {"copy-to-clipboard": "Text copied to clipboard:"}, "placeholders": {"dropdown-not-selected": "Select a value"}, "values": {"empty-string": "«empty»"}}, "group-management": {"idGroup": "Group Id", "list": {"pageTitle": "Groups Settings ", "searchPlaceholder": "Search by name…", "accountsNumber": "Linked Accounts", "emptyMessage": "No archive", "name": "Name", "internal": "Internal"}, "deleteConfirmationHeader": "Confirm Deletion", "deleteConfirmationMessage": "Do you want delete the Group selected?", "noSelection": "Select a group!", "deleteSuccess": "successfully deleted", "createSuccess": "Creation successful", "filters": {"addNew": "New", "actions": {"title": "Actions", "delete": "Delete"}, "export": "Export"}, "edit": {"createGroup": "Create group", "requiredInformationInformation": "Required information", "idGroup": "Group Id", "name": "Name", "namePlaceholder": "Group Name", "description": "Description", "internal": "Internal", "securityInfo": "Security Information", "securityRoles": "Security Roles", "securityRolesDefaultLabel": "Select security role...", "noRolesAssociated": "No roles associated", "sendEmailPermissions": "Email sending permissions", "ignoreCreationEmail": "Ignore Creation Email", "ignoreEnrichmentEmail": "Ignore Enrichment Email", "ignoreApprovalEmail": "Ignore Approver Email", "accountsInfo": "Accounts info", "accounts": {"displayName": "User Name", "login": "<PERSON><PERSON>", "email": "Email", "status": "Status", "emptyMessage": "No account linked with the group"}, "buttons": {"cancel": "Cancel", "create": "Create", "save": "Save"}}, "createConfirmHeader": "The group \"{{name}}\" will be created", "createConfirmMessage": "Do you want to proceed?", "editConfirmHeader": "Edited group information for \"{{name}}\" ({{id}}) will be saved", "editConfirmMessage": "Do you want to proceed?"}, "workflowProcessesMonitor": {"list": {"table": {"columns": {"client": "Client", "processCode": "Process Code", "requester": "Requester", "requestType": "Request Type", "approver": "Approver", "processStatus": "Process Status", "bulkProcessCode": "Bulk Process Code", "materialCode": "Material Code", "plantKey": "Plant Key", "shortDescription": "Short Description", "requestDate": "Request Date", "approvalDate": "Last Approval Date", "requesterNote": "Requester Note", "approverNote": "Approver Note", "completeness": "Completeness", "erpStatus": "ERP Status", "erpIntegrationStatus": "Last ERP Integration Status", "materialType": "Material type", "actions": "Actions", "lastERPOutcome": "Last ERP Outcome", "lastApprovalDate": "Last Approval Date", "processEndDate": "Process End Date", "allNotes": "All Notes", "isOutOfSLA": "Out of SLA"}}}}, "massiveRelationship": {"status": {"A_RELATIONSHIP_STARTING": "Start", "A_RELATIONSHIP_FORMAL_FILE_VALIDATION": "Formal file Validation", "A_RELATIONSHIP_EXTRACTING_DATA": "Extracting Data", "A_RELATIONSHIP_VALIDATING_DATA": "Validating Data", "H_RELATIONSHIP_CONFIRM_DATA": "User - Confirm changes", "A_RELATIONSHIP_PROCESS_GENERATION": "Process Generation", "H_RELATIONSHIP_COMPLETED": "Completed", "H_RELATIONSHIP_ABANDONED": "Abandoned", "H_RELATIONSHIP_TERMINATED": "Terminated", "END": "End", "A_STARTING": "STARTING", "A_FORMAL_FILE_VALIDATION": "FORMAL FILE VALIDATION", "A_EXTRACTING_DATA": "EXTRACTING DATA", "A_VALIDATING_DATA": "VALIDATING DATA", "H_CONFIRM_DATA": "CONFIRM DATA", "H_COMPLETED": "COMPLETED", "H_ABANDONED": "ABANDONED", "H_TERMINATED": "TERMINATED"}, "fieldStatus": {"IGNORED": "Ignored", "CONFIRMED": "Confirmed", "INVALID": "Invalid", "NOT_CHANGED": "Not Changed", "NOT_EDITABLE": "Not Editable", "VALIDATED": "Validated", "DRAFT": "Draft"}, "list": {"searchForm": {"actions": {"label": "Label", "toggleAutorefresh": "Autorefresh", "startProcess": "Upload", "search": "Search", "toggleAutorefresh_false": "Enable Autorefresh", "toggleAutorefresh_true": "Disable Autorefresh ({{interval}})", "downloadTemplate": "Download Template"}, "dateFilter": {"label": "Uploaded Date", "placeholder": "Select date range"}, "status": {"label": "Status", "placeholder": "Select status"}, "user": {"label": "Uploaded By", "placeholder": "Select user"}}, "table": {"columns": {"processId": "Id", "uploadedBy": "Upl. By", "uploadDate": "Upl. Date", "filename": "File Name", "processSummary": "Summary", "currentStatus": "Status", "groups": "No. Groups", "actions": " ", "worklist-ref": "Bulk Process ID"}}, "modals": {"startProcessModal": {"header": "Start a new process...", "btnConfirm": "Confirm"}, "processCreatedDialog": {"successMsg": "Process {{processId}} created with success!", "btnClose": "Close", "btnOpenProcess": "Open"}}}, "processDetail": {"actions": {"show_history": "History", "toggle_more_info": "More Info", "backToList": "Back to process list", "export": "Export", "abandon": "Abandon", "reload": "Refresh", "reload_auto": "Refresh ({{countdown}})", "save": "Save", "next": "Next", "replay": "Replay", "set_error": "Set error", "H_RELATIONSHIP_CONFIRM_DATA": {"next": "Start Proc.", "next_clear": "Start relationship processes", "next_dirty": "Waiting for user to save processes"}, "showAll": "Show all", "H_CONFIRM_DATA": {"next": "next", "next_clear": "next clear", "next_dirty": "next dirty"}}, "processInfo": {"labels": {"processId": "Process Id", "uploadedBy": "Uploaded by", "uploadDate": "Upload Date", "file_name": "File Name", "currentStatus": "Current Status", "groups": "Groups", "bulkProcessId": "Bulk Process ID"}, "history": {"active": "Active", "tmsStart": "Started", "tmsEnd": "Ended", "tms_insert": "Insert Date", "user_insert": "Insert By", "tms_update": "Update Date", "user_update": "Update By", "title": "History", "show-all-substate": "Show all substate"}}, "steps": {"generic": {"default-message-text": "Waiting for automatic process to update the data..."}, "H_RELATIONSHIP_CONFIRM_DATA": {"labels": {"masterdata_header": "Groups of relationship", "masterdataId": "TAM ID", "client_code": "Client / Code", "masterdata_header_missing_materials": "Loaded Masterdata without changes are excluded", "groupTitle": "Group ID: {{group}} - {{type}}", "groupInformation": "group Information"}, "groupDetails": {"title": "Group ID: {{group}} - Relationship type: {{type}}", "noItemsSelected": "No item selected", "primaryMaterial": "Primary Material", "secondaryMaterials": "Secondary Material(s)", "statusChange": "Status Change", "comment": "Comment", "note": "Note", "description": "Description", "changes": "Changes", "materialCode": "Material code", "status": "Status", "changesValue": "{{fieldName}}: {{oldValue}}->{{newValue}}", "materials": "Materials", "goldenRecord": {"materialInstances": "Material Instances", "materialDetails": "Material details"}, "syncInstancesMessage": "This information will be used for synchronization of the instances after creation of the Golden-Record", "invalidGroup": "The group is invalid"}}, "H_RELATIONSHIP_COMPLETED": {"rowExpand": {"primary": "Primary", "secondary": "Secondary"}}, "A_RELATIONSHIP_PROCESS_GENERATION": {"waiting-message": "Waiting for relationship processes to be generated..."}, "END": {"H_RELATIONSHIP_TERMINATED_message": "Process terminated automatically! Some critical error occurred.", "H_RELATIONSHIP_COMPLETED_message": "Process completed!", "H_RELATIONSHIP_ABANDONED_message": "Process Abandoned by the user", "table": {"header": {"materialGroup": "Group ID", "materialDescr": "Description", "processCode": "Outcome", "status": "Status", "type": "Type"}, "messages": {"process-not-generated": "No process generated", "process-generated": "Process <b>{{materialProcessId}}</b> started"}}, "H_TERMINATED_message": "TERMINATED message", "H_COMPLETED_message": "COMPLETED message", "H_ABANDONED_message": "ABANDONED message"}}, "actionBar": {"abandonDialog": {"header": "Confirm abandon", "message": "Are you sure you want to abandon the process", "acceptBtn": "Confirm", "rejectBtn": "Cancel"}}}, "errors": {"extraction-purpose-metadata-mandatory": "file uploaded is not valid. Please export a massive relationship template.", "language-metadata-mandatory": "Language metadata property is mandatory.", "customer-id-metadata-mandatory": "Customer Id metadata property is mandatory.", "unable-to-read-input-file": "Unable to read input file.", "input-file-mandatory": "The input excel file is mandatory.", "user-id-mandatory": "The User Id is mandatory.", "language-mandatory": "The current language is mandatory.", "validation-error": "Validation error"}, "messages": {"title": {"extraction-purpose-error": "Wrong extraction type", "extraction-purpose-success": "Extraction type", "language-error": "Wrong language", "language-success": "Language", "customer-id-error": "Wrong Customer ID", "customer-id-success": "Customer ID", "sheet-number-error": "Wrong sheets number", "sheet-number-success": "Sheets number", "wrong-column-position-error": "Wrong column position", "wrong-column-position-success": "Column position"}, "body": {"extraction-purpose-error": "The export template is not valid.", "extraction-purpose-success": "The export template is valid.", "language-error": "The language is not valid.", "language-success": "The language is valid.", "customer-id-error": "The Customer ID is not valid.", "customer-id-success": "The Customer ID is valid.", "sheet-number-error": "The file has the wrong number of sheets.", "sheet-number-success": "The file has the correct number of sheets.", "wrong-column-position-error": "A column is in the wrong position.", "wrong-column-position-success": "All columns are in the right place."}}, "internalStatus": {"PENDING": "PENDING", "WORKING": "WORKING", "COMPLETED": "COMPLETED", "ERROR": "ERROR"}, "validatingData": {"multipleLeadingMasters": "More than one leading master found in group '{{group}}'.", "noPrimaryRecord": "Leading master not present for group '{{group}}'.", "multipleRelationshipTypes": "Multiple relationship types found in group '{{group}}'.", "inconsistentMaterialCode": "Inconsistent Material Code for material in group '{{group}}'.", "fewerRecords": "The number of records is less than 2 for group '{{group}}'.", "clientCodeMissing": "Client code is not present for material with code '{{code}}' in group '{{group}}'.", "materialCodeMissing": "Material code is not present for material with client '{{client}}' in group '{{group}}'.", "relationshipTypeMissing": "Relationship type is not present for material with client '{{client}}' and code '{{code}}' in group '{{group}}'.", "statusPrefixInvalid": "Status prefix does not match client for material with client '{{client}}' and code '{{code}}' in group '{{group}}'.", "materialInOtherGroups": "Material with client '{{client}}' and code '{{code}}' is already present in other group with the same relationship type.", "noDataForGroup": "No data found for group.", "hasNotAuthorization": "Material with client '{{client}}' and code '{{code}}' in group '{{group}}' does not have the right authorization to create a relationship.", "materialNotFound": "Material with client '{{client}}' and code '{{code}}' in group '{{group}}' not found.", "materialDeleted": "Material with client '{{client}}' and code '{{code}}' in group '{{group}}' is deleted.", "materialInRelationship": "Material with client '{{client}}' and code '{{code}}' in group '{{group}}' is already present in a golden record relationship.", "materialInAnotherProcess": "Material with client '{{client}}' and code '{{code}}' in group '{{group}}' is already present in another relationship process.", "materialDuplicate": "Material with client '{{client}}' and code '{{code}}' in group '{{group}}' is already present in another relationship process as duplicate or Golden Record.", "materialInvalidGeneric": "Material with client '{{client}}' and code '{{code}}' in group '{{group}}' was invalid due to errors.", "materialInvalid": "Material with client '{{client}}' and code '{{code}}' in group '{{group}}' was invalid due to errors: {{errors}}.", "materialError": "Material with client '{{client}}' and code '{{code}}' in group '{{group}}' generated an error {{error}}.", "relationshipCrossClient": "Material with client '{{client}}' and code '{{code}}' in group '{{group}}' is not enabled for Cross-Client relationship.", "materialClassificationInvalid": "Material with client '{{client}}' and code '{{code}}' in group '{{group}}' has an invalid classification.", "genericError": "An unexpected error occurred.", "relationshipAuthorizationTitle": "Material Authorization.", "materialNotFoundTitle": "Material Not Found.", "materialStatusTitle": "Material Status.", "relationshipErrorTitle": "Relationship Error.", "materialErrorTitle": "Material Error.", "prevalidationErrorTitle": "Prevalidation Error.", "materialNotFoundBulkValidationMessage": "Material with client '{{client}}' and code '{{code}}' for process: '{{process}}' was not found.", "materialNotFoundBulkValidationTitle": "Material not found.", "genericProcessErrorMessage": "Process '{{process}}' generated an error.", "processValidatedSuccessMessage": "Successfully validated process '{{process}}'.", "genericTitle": "Validation Data", "noGroupsFound": "No groups found in the process"}, "extractingData": {"noLeadingMasterFound": "No leading master found.", "rowsEmpty": "Parsed rows list cannot be null.", "materialDetailsListNull": "Materials details list cannot be null.", "goldenRecordDetailsError": "Unable to fetch golden record details.", "invalidMaterialStatus": "Invalid format. Expected format: <client>_<materialStatus> - <description>", "invalidCodeDescription": "Invalid format. Expected format: <code>_<description>", "tooManyGroupsUploaded": "There are too many groups on the uploaded file"}}, "organization-management": {"createConfirmHeader": "The organization \"{{name}}\" will be created", "createConfirmMessage": "Do you want to proceed?", "editConfirmHeader": "Edited organization information for \"{{name}}\" ({{id}}) will be saved", "editConfirmMessage": "Do you want to proceed?", "deleteConfirmationHeader": "Delete confirm", "deleteConfirmationMessage": "Do you want to delete the selected organizations?", "list": {"pageTitle": "Organizations management", "searchPlaceholder": "Search by name…", "organizationName": "Organization name", "linkedAccount": "Linked Accounts", "linkedRole": "Linked Roles"}, "filters": {"addNew": "New", "actions": {"title": "Actions", "delete": "Delete"}, "export": "Export"}, "edit": {"id": "Id", "name": "Name", "flgActive": "Active", "namePlaceholder": "Organization Name", "buttons": {"cancel": "Close", "create": "Create", "save": "Save"}}}, "ontology": {"errors": {"cannot-specify-bin-without-warehouse": "Cannot specify bin without choosing warehouse.", "warehouses-bins-must-have-same-size": "Warehouses and storage bins must have the same size."}}, "integrationMonitor": {"buttons": {"search": "Search", "generateExcel": "Generate Excel"}, "filters": {"processTypes": "Process Types", "processStatuses": "Process Statuses", "eventIds": "Event Ids", "processCodes": "Process Codes", "clients": "Clients(ERPs)", "dateFrom": "Date From", "dateTo": "Date To"}, "list": {"table": {"columns": {"eventId": "Event Id", "processType": "Process Type", "status": "Status", "lastUpdated": "Last Updated", "processCode": "Process Code", "reason": "Reason", "actions": "Actions"}}}, "actions": {"download-event": "Download event", "reset-status": "Reset status"}, "messages": {"resetConfirmTitle": "Confirm operation.", "resetConfirmMessage": "Are you sure to reset the Status from 'PENDING' to 'NEW' for event [{{eventId}}]?", "resetOkTitle": "Operation successfully completed.", "resetOkMessage": "Status Reset for the selected event successfully completed."}}, "relationship-service": {"errors": {"relationship-already-requested": "Relationship already requested.", "relationship-already-exists": "Relationship already exists."}}, "cross-plant": {"Golden_Record_Code": "Golden Record Code"}}